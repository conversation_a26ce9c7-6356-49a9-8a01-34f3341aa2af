
### GetWhatsApp

https://api.brevo.com/v3/whatsapp/statistics/events?limit=10&offset=0&sort=desc HTTP/1.1
content-type: application/json
Accept: application/json
api-key: 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


###Get Template list

https://api.brevo.com/v3/whatsappCampaigns/template-list
content-type: application/json
Accept: application/json
api-key: 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

### Send Custom WhatsApp Message

POST https://api.brevo.com/v3/whatsapp/sendMessage
content-type: application/json
Accept: application/json
api-key: 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

{
    "contactNumbers": [
        "918447731090"
    ],
    "senderNumber": "918429564333",
    "text": "hello"
}

###Send with template
POST https://api.brevo.com/v3/whatsapp/sendMessage
content-type: application/json
Accept: application/json
api-key: 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

{
    "contactNumbers": [
        "919599665481"
    ],
    "senderNumber": "918429564333",
    "templateId": 30
}

###Get All whatsApp templates
https://api.brevo.com/v3/whatsappCampaigns/template-list HTTP/1.1
content-type: application/json
Accept: application/json
api-key: 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

###Test On-Demand WhatsApp
POST http://localhost:7071/api/notificationprocessor/triggerondemandnotification HTTP/1.1
content-type: application/json
Accept: application/json
Authorization: Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsIng1dCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSIsImtpZCI6IkNOdjBPSTNSd3FsSEZFVm5hb01Bc2hDSDJYRSJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.DDZCPAZpek8vaCU3ApMOTLwDbHyDLkm6WYBnGZWh-SzqZ02R4P6vKiED50TWRQUteQ4zkGQggzWac2I20deV8_wOw0brFiB8UuxcaLpik9sr58C95xjbDuJbYcRc5AVkYxzq-1Pl57vtrOx7caY2464ItsOcW3iq9XPaWi01AocSS6w_ZqVL_sCRxYBLs3akI5vGlbnusJ44QFrt6grqMYXrGp5WiqeOuzvPm8vIscQfmw9VXRF_GgOeZA_hMDIheNvdLvQer4dILmMgaNPjAzU5-HbX-VYKIw8KDIMyqZgcDlx56qiYSiw2RV58tEEiQI-gsoO6PlsKufBVFCU84A

{
  "NotificationType": "YieldReportSummary",
  "ReportName": "YieldReportSummary",
  "Parameters": {
    "fromDate": "2023-01-01 12:00 AM",
    "toDate": "2025-01-31 11:59 PM"
  }
}
