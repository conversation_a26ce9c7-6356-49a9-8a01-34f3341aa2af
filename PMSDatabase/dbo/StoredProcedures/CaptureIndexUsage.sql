CREATE PROCEDURE dbo.CaptureIndexUsage
AS
BEGIN
    INSERT INTO IndexUsageStats (TableName, IndexName, UserSeeks, UserScans, UserLookups, UserUpdates, LastUsed)
    SELECT 
        OBJECT_NAME(i.object_id) AS TableName,
        i.name AS IndexName,
        ius.user_seeks,
        ius.user_scans,
        ius.user_lookups,
        ius.user_updates,
        COALESCE(
            NULLIF(ius.last_user_seek, '1900-01-01'),
            NULLIF(ius.last_user_scan, '1900-01-01'),
            NULLIF(ius.last_user_lookup, '1900-01-01')
        ) AS LastUsed
    FROM sys.dm_db_index_usage_stats ius
    RIGHT JOIN sys.indexes i ON ius.object_id = i.object_id 
        AND ius.index_id = i.index_id
    WHERE database_id = DB_ID();
END
GO

