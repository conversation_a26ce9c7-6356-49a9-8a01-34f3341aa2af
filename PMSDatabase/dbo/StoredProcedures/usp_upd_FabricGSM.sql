--usp_upd_FabricGSM 442
CREATE PROCEDURE usp_upd_FabricGSM  
    @SaleOrderId INT  
AS  
BEGIN  
    DECLARE @ProductId INT, @AvgGSM FLOAT;  
  
    SELECT   
       @ProductId = pr.ProductId,  
       @AvgGSM = pr.AvgGSM  
    FROM   
        [dbo].[SaleOrderTable] s  
    INNER JOIN   
        SaleOrderProductionTable a ON s.SaleOrderId = a.SaleOrderId  
    INNER JOIN   
       [dbo].[ProductMaster] pr ON a.FabricProductId = pr.ProductId  
    WHERE   
        s.SaleOrderId = @SaleOrderId  
  
    IF @AvgGSM IS NOT NULL  
    BEGIN  
          
  UPDATE A  
          SET FabricGSM = @AvgGSM    
          FROM SaleOrderProductionTable A  
          INNER JOIN [dbo].[ProductMaster] pr ON A.FabricProductId = pr.ProductId  
          WHERE A.SaleOrderId = @saleorderid;  
  
  
        UPDATE ifcm  
          SET ifcm.FabricGSM = @AvgGSM  
          FROM InspectionSaleFormulationCodeMaster ifcm  
          inner JOIN  [dbo].[InspectionFormulationCodeMixingTable] isfc  
          ON ifcm.InspectionSaleFormulationCodeId = isfc.InspectionSaleFormulationCodeId  
          WHERE 
		  --ifcm.FabricProductId = @ProductId and 
		  isfc.SaleOrderId = @SaleOrderId;  
  
  
        UPDATE [dbo].[SaleFormulationCodeMaster]  
        SET FabricGSM = @AvgGSM  
        WHERE FabricProductId = @ProductId;  
  
        UPDATE SaleOrderProductionCompleteTable  
        SET FabricGSM = @AvgGSM  
        WHERE SaleOrderId = @SaleOrderId;  
    END;  
END;
GO

