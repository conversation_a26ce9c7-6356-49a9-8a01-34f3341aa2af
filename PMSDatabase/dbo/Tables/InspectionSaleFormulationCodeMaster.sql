CREATE TABLE [dbo].[InspectionSaleFormulationCodeMaster] (
    [InspectionSaleFormulationCodeId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [InspectionSaleFormulationCode]   VARCHAR (50)    NULL,
    [AddedBy]                         VARCHAR (50)    NULL,
    [AddedDate]                       DATETIME        NULL,
    [CategoryId]                      BIGINT          NULL,
    [ThicknessId]                     BIGINT          NULL,
    [PreSkinGSM]                      DECIMAL (18, 2) NULL,
    [SkinGSM]                         DECIMAL (18, 2) NULL,
    [FoamGSM]                         DECIMAL (18, 2) NULL,
    [AdhesiveGSM]                     DECIMAL (18, 2) NULL,
    [FabricGSM]                       DECIMAL (18, 2) NULL,
    [TotalGSM]                        DECIMAL (18, 2) NULL,
    [FabricProductId]                 BIGINT          NULL,
    [FabricProductQty]                DECIMAL (18, 2) NULL,
    [FabricWidthInMeter]              DECIMAL (18, 4) NULL,
    [ShiftSupervisorWorkerId]         BIGINT          NULL
);
GO

ALTER TABLE [dbo].[InspectionSaleFormulationCodeMaster]
    ADD CONSTRAINT [PK_InspectionSaleFormulationCodeMaster] PRIMARY KEY CLUSTERED ([InspectionSaleFormulationCodeId] ASC);
GO

