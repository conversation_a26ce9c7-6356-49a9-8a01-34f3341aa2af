CREATE TABLE [dbo].[OutpassItemTable] (
    [OutpassItemId]       BIGINT          IDENTITY (1, 1) NOT NULL,
    [OutpassId]           BIGINT          NULL,
    [StockProductId]      BIGINT          NULL,
    [ProductName]         VARCHAR (500)    NULL,
    [Quantity]            DECIMAL (18, 2) NULL,
    [RackId]              BIGINT          NULL,
    [ReturnedQuantity]    DECIMAL (18, 2) NULL,
    [ReturnedRackId]      BIGINT          NULL,
    [Amount]              DECIMAL (18, 2) NULL,
    [Unit]                VARCHAR (50)    NULL,
    [ReturnCompletedBy]   VARCHAR (50)    NULL,
    [ReturnCompletedDate] DATETIME        NULL
);
GO

ALTER TABLE [dbo].[OutpassItemTable]
    ADD CONSTRAINT [PK_OutpostItemTable] PRIMARY KEY CLUSTERED ([OutpassItemId] ASC);
GO

