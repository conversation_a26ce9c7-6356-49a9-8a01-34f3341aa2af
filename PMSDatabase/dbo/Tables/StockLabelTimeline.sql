CREATE TABLE [dbo].[StockLabelTimeline] (
    [LabelTimelineId]   BIGINT        IDENTITY (1, 1) NOT NULL,
    [StockLabelId]      BIGINT        NOT NULL,
    [OldStatus]         VARCHAR (20)  NULL,
    [NewStatus]         VARCHAR (20)  NULL,
    [ChangeDate]        DATETIME      NULL,
    [ChangedBy]         VARCHAR (50)  NULL,
    [Remark]            VARCHAR (200) NULL,
    [RelatedEntityId]   BIGINT        NULL,
    [RelatedEntityType] VARCHAR (50)  NULL
);
GO

ALTER TABLE [dbo].[StockLabelTimeline]
    ADD CONSTRAINT [PK_StockLabelTimeline] PRIMARY KEY CLUSTERED ([LabelTimelineId] ASC);
GO

ALTER TABLE [dbo].[StockLabelTimeline]
    ADD CONSTRAINT [FK_StockLabelTimeline_StockLabelTable] FOREIGN KEY ([StockLabelId]) REFERENCES [dbo].[StockLabelTable] ([StockLabelId]);
GO

