CREATE TABLE [dbo].[OutpassMaster] (
    [OutpassId]           BIGINT        IDENTITY (1, 1) NOT NULL,
    [OutpassTo]           VARCHAR (500) NULL,
    [OutpassNumber]       VARCHAR (50)  NULL,
    [OutpassDate]         DATET<PERSON>E      NULL,
    [OutpassType]         VARCHAR (50)  NULL,
    [Purpose]             VARCHAR (50)  NULL,
    [Remark]              VARCHAR (500) NULL,
    [IsOutpassIn]         BIT           NULL,
    [AddedBy]             VARCHAR (50)  NULL,
    [AddedDate]           DATETIME      NULL,
    [OutpassToCustomerId] BIGINT        NULL,
    [PurposeId]           BIGINT        NULL,
    [ExpectedReturnDate]  DATETIME      NULL,
    [Status]              VARCHAR (20)  NULL,
    [TransportId]         BIGINT        NULL,
    [VehicleId]           BIGINT        NULL,
    [IsGateIn]            BIT           NULL
);
GO

ALTER TABLE [dbo].[OutpassMaster]
    ADD CONSTRAINT [PK_OutpostMaster] PRIMARY KEY CLUSTERED ([OutpassId] ASC);
GO

