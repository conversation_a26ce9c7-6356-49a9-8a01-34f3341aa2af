CREATE TABLE [dbo].[ProformaInvoiceTable] (
    [ProformaInvoiceId]     BIGINT          IDENTITY (1, 1) NOT NULL,
    [CustomerId]            BIGINT          NULL,
    [GSTN]                  VARCHAR (50)    NULL,
    [ProformaInvoiceNumber] VARCHAR (50)    NULL,
    [ProformaInvoiceDate]   DATE            NULL,
    [ConsignorReference]    VARCHAR (50)    NULL,
    [ReferenceType]         VARCHAR (50)    NULL,
    [BuyerReferenceNumber]  VARCHAR (50)    NULL,
    [CountryOfOrigin]       VARCHAR (50)    NULL,
    [CountryOfDestinaton]   VARCHAR (50)    NULL,
    [MaterialType]          VARCHAR (50)    NULL,
    [HSNcode]               VARCHAR (50)    NULL,
    [BankName]              VARCHAR (100)   NULL,
    [BankBranch]            VARCHAR (100)   NULL,
    [BankAccountNumber]     VARCHAR (50)    NULL,
    [IFSCCode]              VARCHAR (50)    NULL,
    [SwiftCode]             VARCHAR (50)    NULL,
    [BeneficiaryName]       VARCHAR (50)    NULL,
    [PortOfLoading]         VARCHAR (50)    NULL,
    [PortOfDischarge]       VARCHAR (50)    NULL,
    [FinalDestination]      VARCHAR (50)    NULL,
    [ModeOfTransport]       VARCHAR (50)    NULL,
    [AddedBy]               VARCHAR (50)    NULL,
    [AddedDate]             DATETIME        NULL,
    [MOQ]                   DECIMAL (18, 2) NULL,
    [MOQTotal]              DECIMAL (18, 2) NULL,
    [GST]                   DECIMAL (18, 2) NULL,
    [TotalPrice]            DECIMAL (18, 2) NULL,
    [TermsCondition]        VARCHAR (500)   NULL,
    [Currency]              VARCHAR (10)    NULL,
    [Discount]              DECIMAL (18, 2) NULL,
    [BankId]                BIGINT          NULL
);
GO

ALTER TABLE [dbo].[ProformaInvoiceTable]
    ADD CONSTRAINT [PK_ProformaInvoiceTable] PRIMARY KEY CLUSTERED ([ProformaInvoiceId] ASC);
GO

