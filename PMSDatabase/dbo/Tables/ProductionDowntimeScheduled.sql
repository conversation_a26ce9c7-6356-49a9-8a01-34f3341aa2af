CREATE TABLE [dbo].[ProductionDowntimeScheduled] (
    [ScheduledDowntimeId]        BIGINT        IDENTITY (1, 1) NOT NULL,
    [ProductionDowntimeReasonId] BIGINT        NOT NULL,
    [StartTime]                  TIME (7)      NOT NULL,
    [EndTime]                    TIME (7)      NOT NULL,
    [RecurrencePattern]          VARCHAR (20)  NOT NULL,
    [ApplicableDays]             VARCHAR (100) NULL,
    [DayOfMonth]                 INT           NULL,
    [IsRecurring]                BIT           DEFAULT ((0)) NOT NULL,
    [EffectiveFrom]              DATETIME      NULL,
    [EffectiveTo]                DATETIME      NULL,
    [ProductionLineNo]           INT           NOT NULL,
    [IsActive]                   BIT           DEFAULT ((1)) NOT NULL,
    [CreatedBy]                  VARCHAR (50)  NOT NULL,
    [CreatedOn]                  DATETIME      DEFAULT (getdate()) NOT NULL,
    [ModifiedBy]                 VARCHAR (50)  NULL,
    [ModifiedOn]                 DATETIME      NULL,
    [IsDeleted]                  BIT           DEFAULT ((0)) NOT NULL,
    CONSTRAINT [PK_ProductionDowntimeScheduled] PRIMARY KEY CLUSTERED ([ScheduledDowntimeId] ASC),
    CONSTRAINT [CHK_ProductionDowntimeScheduled_EffectiveDates] CHECK ([EffectiveTo] IS NULL OR [EffectiveTo]>=[EffectiveFrom]),
    CONSTRAINT [CHK_ScheduledDowntime_DayOfMonth] CHECK ([DayOfMonth] IS NULL OR [DayOfMonth]>=(1) AND [DayOfMonth]<=(31)),
    CONSTRAINT [CHK_ScheduledDowntime_RecurrencePattern] CHECK ([RecurrencePattern]='Monthly' OR [RecurrencePattern]='Weekly' OR [RecurrencePattern]='Daily'),
    CONSTRAINT [FK_ScheduledDowntime_ProductionDowntimeReasonMaster] FOREIGN KEY ([ProductionDowntimeReasonId]) REFERENCES [dbo].[ProductionDowntimeReasonMaster] ([ProductionDowntimeReasonId])
);
GO

CREATE NONCLUSTERED INDEX [IX_ProductionDowntimeScheduled_ProductionDowntimeReasonId]
    ON [dbo].[ProductionDowntimeScheduled]([ProductionDowntimeReasonId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_ProductionDowntimeScheduled_ProductionLine_Active]
    ON [dbo].[ProductionDowntimeScheduled]([ProductionLineNo] ASC, [IsActive] ASC)
    INCLUDE([StartTime], [EndTime], [ApplicableDays]);
GO


