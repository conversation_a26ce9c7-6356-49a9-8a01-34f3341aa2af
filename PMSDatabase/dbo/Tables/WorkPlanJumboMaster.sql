CREATE TABLE [dbo].[WorkPlanJumboMaster] (
    [WorkPlanJumboMasterId]   BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]             BIGINT          NULL,
    [JumboRollDate]           DATETIME        NULL,
    [JumboRollStartTime]      DATETIME        NULL,
    [JumboRollEndTime]        DATETIME        NULL,
    [JumboNo]                 VARCHAR (50)    NULL,
    [Rate]                    DECIMAL (18, 2) NULL,
    [Amount]                  DECIMAL (18, 2) NULL,
    [JumboRolQty]             DECIMAL (18, 2) NULL,
    [Weight]                  DECIMAL (18, 2) NULL,
    [RackId]                  BIGINT          NULL,
    [StoreId]                 BIGINT          NULL,
    [RackCode]                VARCHAR (50)    NULL,
    [RackName]                VARCHAR (50)    NULL,
    [StoreCode]               VARCHAR (50)    NULL,
    [StoreName]               VARCHAR (50)    NULL,
    [Remark]                  VARCHAR (500)   NULL,
    [Yield]                   DECIMAL (18, 2) NULL,
    [AddedBy]                 VARCHAR (50)    NULL,
    [AddedDate]               DATETIME        NULL,
    [IsInspectionCompleted]   BIT             NULL,
    [ActualQuantity]          DECIMAL (18, 2) NULL,
    [WastagePrint]            DECIMAL (18, 2) NULL,
    [WastageEmbossing]        DECIMAL (18, 2) NULL,
    [WastageLacquer]          DECIMAL (18, 2) NULL,
    [WastageVacuum]           DECIMAL (18, 2) NULL,
    [WastageTumbling]         DECIMAL (18, 2) NULL,
    [ShiftSupervisorWorkerId] BIGINT          NULL
);
GO

ALTER TABLE [dbo].[WorkPlanJumboMaster]
    ADD CONSTRAINT [PK_WorkPlanJumboMaster] PRIMARY KEY CLUSTERED ([WorkPlanJumboMasterId] ASC);
GO

