CREATE TABLE [dbo].[StockLabelMovementHistoryTable] (
    [LabelMovementId] BIGINT        IDENTITY (1, 1) NOT NULL,
    [StockLabelId]    BIGINT        NOT NULL,
    [FromStoreId]     BIGINT        NOT NULL,
    [FromRackId]      BIGINT        NOT NULL,
    [ToStoreId]       BIGINT        NOT NULL,
    [ToRackId]        BIGINT        NOT NULL,
    [MovementDate]    DATETIME      NOT NULL,
    [MovedBy]         VARCHAR (50)  NOT NULL,
    [Reason]          VARCHAR (250) NOT NULL,
    [IssueId]         BIGINT        NOT NULL
);
GO

ALTER TABLE [dbo].[StockLabelMovementHistoryTable]
    ADD CONSTRAINT [PK_StockLabelMovementHistoryTable] PRIMARY KEY CLUSTERED ([LabelMovementId] ASC);
GO

ALTER TABLE [dbo].[StockLabelMovementHistoryTable]
    ADD CONSTRAINT [FK_StockLabelMovementHistoryTable_StockLabelTable] FOREIGN KEY ([StockLabelId]) REFERENCES [dbo].[StockLabelTable] ([StockLabelId]);
GO


ALTER TABLE [dbo].[StockLabelMovementHistoryTable]
    ADD CONSTRAINT [FK_StockLabelMovementHistoryTable_IssueProductTable] FOREIGN KEY ([IssueId]) REFERENCES [dbo].[IssueProductTable] ([IssueId]);
GO

