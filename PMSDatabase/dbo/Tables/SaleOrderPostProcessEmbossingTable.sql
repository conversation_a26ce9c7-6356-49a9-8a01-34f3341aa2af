CREATE TABLE [dbo].[SaleOrderPostProcessEmbossingTable] (
    [SaleOrderPostProcessEmbossingId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                     BIGINT          NULL,
    [EmbossingRack]                   BIGINT          NULL,
    [EmbossingCompletedQuantity]      DECIMAL (18, 2) NULL,
    [EmbossingWastageQuantity]        DECIMAL (18, 2) NULL,
    [EmbossingMeasurementUnit]        VARCHAR (50)    NULL,
    [EmbossingStatus]                 VARCHAR (50)    NULL,
    [AddedBy]                         VARCHAR (50)    NULL,
    [AddedDate]                       DATETIME        NULL,
    [ReceivedQuantity]                DECIMAL (18, 2) NULL,
    [Remark]                          VARCHAR (100)   NULL,
    [Rank]                            INT             NULL,
    [StartDateTime]                   DATETIME        NULL,
    [EndDateTime]                     DATETIME        NULL,
    [LineNo]                          INT             NULL,
    [ShiftSupervisorWorkerId]         BIGINT          NULL,
    [PricePerUnit]                    DECIMAL (18, 3) NULL
);
GO

ALTER TABLE [dbo].[SaleOrderPostProcessEmbossingTable]
    ADD CONSTRAINT [PK_SaleOrderPostProcessEmbossingTable] PRIMARY KEY CLUSTERED ([SaleOrderPostProcessEmbossingId] ASC);
GO


ALTER TABLE [dbo].[SaleOrderPostProcessEmbossingTable]
    ADD CONSTRAINT [FK_SaleOrderPostProcessEmbossingTable_FactoryWorkersMaster] FOREIGN KEY ([ShiftSupervisorWorkerId]) REFERENCES [dbo].[FactoryWorkersMaster] ([WorkerId]);
GO

