CREATE TABLE [dbo].[NotificationGroupsTable] (
    [NotificationGroupUserId]       BIGINT         IDENTITY (1, 1) NOT NULL,
    [NotificationType]              NVARCHAR (50)  NULL,
    [TriggerType]                   VARCHAR (50)   NULL,
    [ReportName]                    VARCHAR (50)   NULL,
    [Name]                          NVARCHAR (50)  NULL,
    [Email]                         NVARCHAR (100) NULL,
    [EnableToEmail]                 BIT            NULL,
    [EnableCCEmail]                 BIT            NULL,
    [EnableBCCEmail]                BIT            NULL,
    [AddedBy]                       NVARCHAR (50)  NULL,
    [AddedDate]                     DATETIME       NULL,
    [Disabled]                      BIT            NULL,
    [DisabledBy]                    NVARCHAR (50)  NULL,
    [DisabledDate]                  DATETIME       NULL,
    [UserType]                      VARCHAR (50)   NULL,
    [UserMasterId]                  BIGINT         NULL,
    [IsWhatsAppNotificationEnabled] BIT            NULL,
    [MobileNumber]                  VARCHAR (200)  NULL,
    [PreferredNotificationTime]     TIME (7)       NULL,
    [TimeZone]                      VARCHAR (50)   DEFAULT ('UTC') NULL,
    [NotificationFrequency]         VARCHAR (50)   NULL,
    [WhatsAppTemplateMasterId]      BIGINT         NULL,
    [LastTriggeredBy]               NVARCHAR (100) NULL,
    [LastTriggeredDate]             DATETIME       NULL,
    CONSTRAINT [PK_NotificationGroups] PRIMARY KEY CLUSTERED ([NotificationGroupUserId] ASC),
    CONSTRAINT [FK_NotificationGroupsTable_WhatsAppTemplateMaster] FOREIGN KEY ([WhatsAppTemplateMasterId]) REFERENCES [dbo].[WhatsAppTemplateMaster] ([WhatsAppTemplateMasterId])
);
GO

