CREATE TABLE [dbo].[PurchaseOrderTable] (
    [POId]                BIGINT        IDENTITY (1, 1) NOT NULL,
    [PONumber]            VARCHAR (50)  NULL,
    [SupplierId]          BIGINT        NULL,
    [DeliveryTermId]      BIGINT        NULL,
    [PaymentTermId]       BIGINT        NULL,
    [Reference]           VARCHAR (100) NULL,
    [TransportId]         BIGINT        NULL,
    [POTotalAmount]       VARCHAR (50)  NULL,
    [POCreationDate]      DATETIME      NULL,
    [AddedDate]           DATETIME      NULL,
    [AddedBy]             VARCHAR (50)  NULL,
    [GRN]                 VARCHAR (50)  NULL,
    [IsPOComplete]        BIT           NULL,
    [ContactPersonUserId] BIGINT        NULL,
    [DeliveryDate]        DATETIME      NULL,
    [Status]              VARCHAR (50)  NULL,
    [Remarks]             VARCHAR (500) NULL,
    [ActionBy]            VARCHAR (50)  NULL,
    [ApprovedBy]          VARCHAR (50)  NULL,
    [ApprovedDate]        DATETIME      NULL,
    [DepartmentId]        BIGINT        NULL,
    [POType]              VARCHAR (50)  NULL
);


GO


ALTER TABLE [dbo].[PurchaseOrderTable]
    ADD CONSTRAINT [PK_PurchaseOrderTable] PRIMARY KEY CLUSTERED ([POId] ASC);


GO