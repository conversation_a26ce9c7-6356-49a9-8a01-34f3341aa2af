CREATE TABLE [dbo].[ProductionDowntimeTable] (
    [ProductionDowntimeId]       BIGINT          IDENTITY (1, 1) NOT NULL,
    [ProductionDowntimeReasonId] BIGINT          NULL,
    [ProductionLineType]         VARCHAR (50)    NULL,
    [ProductionLineNo]           INT             NULL,
    [StartTime]                  DATETIME        NULL,
    [EndTime]                    DATETIME        NULL,
    [ExcessDurationMinutes]      DECIMAL (10, 2) NULL,
    [Comments]                   VARCHAR (1000)  NULL,
    [CreatedBy]                  VARCHAR (100)   NULL,
    [CreatedOn]                  DATETIME        DEFAULT (getdate()) NULL,
    [ModifiedBy]                 VARCHAR (100)   NULL,
    [ModifiedOn]                 DATETIME        NULL,
    [IsDeleted]                  BIT             DEFAULT ((0)) NULL,
    [ActualDurationMinutes]      AS              (CONVERT([decimal](10,2),datediff(minute,[StartTime],[EndTime])+(datediff(second,[StartTime],[EndTime])%(60))/(60.0))) PERSISTED,
    PRIMARY KEY CLUSTERED ([ProductionDowntimeId] ASC),
    FOREIGN KEY ([ProductionDowntimeReasonId]) REFERENCES [dbo].[ProductionDowntimeReasonMaster] ([ProductionDowntimeReasonId])
);
GO

