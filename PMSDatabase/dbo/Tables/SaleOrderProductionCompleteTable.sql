CREATE TABLE [dbo].[SaleOrderProductionCompleteTable] (
    [SaleOrderProductionCompleteId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                   BIGINT          NOT NULL,
    [ManufacturedQuantity]          DECIMAL (18, 2) NULL,
    [PreSkinGSM]                    DECIMAL (18, 2) NULL,
    [SkinGSM]                       DECIMAL (18, 2) NULL,
    [FoamGSM]                       DECIMAL (18, 2) NULL,
    [AdhesiveGSM]                   DECIMAL (18, 2) NULL,
    [FabricGSM]                     DECIMAL (18, 2) NULL,
    [PreSkinScGSM]                  DECIMAL (18, 2) NULL,
    [SkinScGSM]                     DECIMAL (18, 2) NULL,
    [FoamScGSM]                     DECIMAL (18, 2) NULL,
    [AdhesiveScGSM]                 DECIMAL (18, 2) NULL,
    [PreSkinRemainingPasteQty]      DECIMAL (18, 2) NULL,
    [PreSkinActualPasteQty]         DECIMAL (18, 2) NULL,
    [SkinRemainingPasteQty]         DECIMAL (18, 2) NULL,
    [SkinActualPasteQty]            DECIMAL (18, 2) NULL,
    [FoamRemainingPasteQty]         DECIMAL (18, 2) NULL,
    [FoamActualPasteQty]            DECIMAL (18, 2) NULL,
    [AdhesiveRemainingPasteQty]     DECIMAL (18, 2) NULL,
    [AdhesiveActualPasteQty]        DECIMAL (18, 2) NULL,
    [Addedby]                       VARCHAR (100)   NULL,
    [AddedDate]                     DATETIME        NULL
);
GO

ALTER TABLE [dbo].[SaleOrderProductionCompleteTable]
    ADD CONSTRAINT [PK_SaleOrderProductionCompleteTable] PRIMARY KEY CLUSTERED ([SaleOrderProductionCompleteId] ASC);
GO

