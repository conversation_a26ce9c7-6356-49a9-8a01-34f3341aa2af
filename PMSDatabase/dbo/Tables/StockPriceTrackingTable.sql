CREATE TABLE [dbo].[StockPriceTrackingTable] (
    [StockPriceTrackingId]       BIGINT          IDENTITY (1, 1) NOT NULL,
    [StockProductId]             BIGINT          NULL,
    [PricePerUnit]               DECIMAL (10, 2) NULL,
    [NewPricePerUnit]            DECIMAL (10, 2) NULL,
    [ShippingHandlingPerUnit]    DECIMAL (18, 2) NULL,
    [NewShippingHandlingPerUnit] DECIMAL (18, 2) NULL,
    [FreightPerUnit]             DECIMAL (18, 2) NULL,
    [NewFreightPerUnit]          DECIMAL (18, 2) NULL,
    [InvoicePricePerUnit]        DECIMAL (18, 2) NULL,
    [NewInvoicePricePerUnit]     DECIMAL (18, 2) NULL,
    [MiscPerUnit]                DECIMAL (18, 2) NULL,
    [NewMiscPerUnit]             DECIMAL (18, 2) NULL,
    [UpdatedById]                BIGINT          NULL,
    [UpdatedDate]                DATETIME        NULL
);
GO

ALTER TABLE [dbo].[StockPriceTrackingTable]
    ADD CONSTRAINT [PK_StockPriceTrackingTable] PRIMARY KEY CLUSTERED ([StockPriceTrackingId] ASC);
GO


ALTER TABLE [dbo].[StockPriceTrackingTable]
    ADD CONSTRAINT [FK_StockPriceTrackingTable_UserMaster] FOREIGN KEY ([UpdatedById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

