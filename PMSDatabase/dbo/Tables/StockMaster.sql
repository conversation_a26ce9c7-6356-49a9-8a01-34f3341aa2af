CREATE TABLE [dbo].[StockMaster] (
    [StockId]                        BIGINT       IDENTITY (1, 1) NOT NULL,
    [StockDate]                      DATETIME     NOT NULL,
    [InvoiceId]                      BIGINT       NOT NULL,
    [InspectionCompleted]            BIT          NULL,
    [Batch]                          VARCHAR (50) NULL,
    [AllocationCompleted]            BIT          NULL,
    [ManageRejectedItemsCompleted]   BIT          NULL,
    [IsOpeningStock]                 BIT          NULL,
    [AddedBy]                        VARCHAR (50) NULL,
    [AddedDate]                      DATETIME     NULL,
    [SaleOrderId]                    BIGINT       NULL,
    [InspectionCompletedBy]          VARCHAR (50) NULL,
    [InspectionCompletedDate]        DATETIME     NULL,
    [ProductQuality]                 VARCHAR (20) NULL,
    [IsQualityInspectionCompleted]   BIT          NULL,
    [QualityInspectionCompletedBy]   BIGINT       NULL,
    [QualityInspectionCompletedDate] DATETIME     NULL
);


GO


ALTER TABLE [dbo].[StockMaster]
    ADD CONSTRAINT [PK_StockMaster] PRIMARY KEY CLUSTERED ([StockId] ASC);


GO
ALTER TABLE [dbo].[StockMaster]
    ADD CONSTRAINT [FK_StockMaster_UserMaster] FOREIGN KEY ([QualityInspectionCompletedBy]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO


ALTER TABLE [dbo].[StockMaster]
    ADD CONSTRAINT [DEFAULT_StockMaster_IsQualityInspectionCompleted] DEFAULT ((0)) FOR [IsQualityInspectionCompleted];
GO

