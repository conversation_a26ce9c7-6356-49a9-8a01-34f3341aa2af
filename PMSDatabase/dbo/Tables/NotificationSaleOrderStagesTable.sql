CREATE TABLE [dbo].[NotificationSaleOrderStagesTable] (
    [StageId]            BIGINT        IDENTITY (1, 1) NOT NULL,
    [SaleOrderStages]    NVARCHAR (50) NULL,
    [OnlyInternal]       BIT           NULL,
    [OnlyCustomer]       BIT           NULL,
    [AddedBy]            NVARCHAR (50) NULL,
    [AddedDate]          DATETIME      NULL,
    [Disabled]           BIT           NULL,
    [DisabledBy]         NVARCHAR (50) NULL,
    [DisabledDate]       DATETIME      NULL,
    [WhatsappTemplateId] BIGINT        NULL
);
GO

ALTER TABLE [dbo].[NotificationSaleOrderStagesTable]
    ADD CONSTRAINT [PK_NotificationSaleOrderStagesTable] PRIMARY KEY CLUSTERED ([StageId] ASC);
GO

ALTER TABLE [dbo].[NotificationSaleOrderStagesTable]
    ADD CONSTRAINT [FK_NotificationSaleOrderStagesTable_WhatsAppTemplateMaster] FOREIGN KEY ([WhatsappTemplateId]) REFERENCES [dbo].[WhatsAppTemplateMaster] ([WhatsAppTemplateMasterId]);
GO

