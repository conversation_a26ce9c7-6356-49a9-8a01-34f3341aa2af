CREATE TABLE [dbo].[SaleOrderProductionLacquerRawMaterialTable] (
    [SaleOrderProductionLacquerRawMaterialId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderProductionId]                   BIGINT          NULL,
    [LacquerMasterId]                         BIGINT          NULL,
    [ProductId]                               BIGINT          NULL,
    [Quantity]                                DECIMAL (18, 2) NULL,
    [Unit]                                    VARCHAR (50)    NULL,
    [Price]                                   DECIMAL (18, 2) NULL,
    [Removed]                                 BIT             NULL,
    [RemovedBy]                               VARCHAR (50)    NULL,
    [RemovedDate]                             DATETIME        NULL,
    [Rank]                                    INT             NULL
);
GO

ALTER TABLE [dbo].[SaleOrderProductionLacquerRawMaterialTable]
    ADD CONSTRAINT [PK_SaleOrderProductionLacquerRawMaterialTable] PRIMARY KEY CLUSTERED ([SaleOrderProductionLacquerRawMaterialId] ASC);
GO

