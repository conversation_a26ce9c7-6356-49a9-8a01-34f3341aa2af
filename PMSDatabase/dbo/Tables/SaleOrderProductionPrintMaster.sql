CREATE TABLE [dbo].[SaleOrderProductionPrintMaster] (
    [SaleOrderProductionPrintMasterId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderProductionId]            BIGINT          NULL,
    [PrintMasterId]                    BIGINT          NOT NULL,
    [Quantity]                         DECIMAL (18, 2) NULL,
    [Price]                            DECIMAL (18, 2) NULL,
    [Total]                            DECIMAL (18, 2) NULL,
    [Removed]                          BIT             NULL,
    [RemovedBy]                        VARCHAR (50)    NULL,
    [RemovedDate]                      DATETIME        NULL,
    [Rank]                             INT             NULL
);
GO

ALTER TABLE [dbo].[SaleOrderProductionPrintMaster]
    ADD CONSTRAINT [PK_SaleOrderProductionPrintMaster] PRIMARY KEY CLUSTERED ([SaleOrderProductionPrintMasterId] ASC);
GO

