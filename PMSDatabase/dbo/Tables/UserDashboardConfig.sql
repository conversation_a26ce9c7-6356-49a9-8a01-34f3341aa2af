CREATE TABLE [dbo].[UserDashboardConfig] (
    [ConfigId]      BIGINT         IDENTITY (1, 1) NOT NULL,
    [UserId]        NVARCHAR (100) NOT NULL,
    [DashboardType] NVARCHAR (50)  NOT NULL,
    [ConfigJson]    NVARCHAR (MAX) NOT NULL,
    [ConfigName]    NVARCHAR (200) NULL,
    [Description]   NVARCHAR (500) NULL,
    [IsDefault]     BIT            DEFAULT ((0)) NULL,
    [Disabled]      BIT            DEFAULT ((0)) NULL,
    [Version]       INT            DEFAULT ((1)) NULL,
    [Tags]          NVARCHAR (200) NULL,
    [AddedBy]       NVARCHAR (100) NULL,
    [AddedDate]     DATETIME2 (7)  DEFAULT (getdate()) NULL,
    [ModifiedBy]    NVARCHAR (100) NULL,
    [ModifiedDate]  DATETIME2 (7)  DEFAULT (getdate()) NULL,
    PRIMARY KEY CLUSTERED ([ConfigId] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX_UserDashboardConfig_Default]
    ON [dbo].[UserDashboardConfig]([UserId] ASC, [DashboardType] ASC, [IsDefault] ASC) WHERE ([IsDefault]=(1));
GO

CREATE NONCLUSTERED INDEX [IX_UserDashboardConfig_User_Type]
    ON [dbo].[UserDashboardConfig]([UserId] ASC, [DashboardType] ASC);
GO

