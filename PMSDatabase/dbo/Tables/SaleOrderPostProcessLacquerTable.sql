CREATE TABLE [dbo].[SaleOrderPostProcessLacquerTable] (
    [SaleOrderPostProcessLacquerId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                   BIGINT          NULL,
    [LacquerRack]                   BIGINT          NULL,
    [LacquerCompletedQuantity]      DECIMAL (18, 2) NULL,
    [LacquerWastageQuantity]        DECIMAL (18, 2) NULL,
    [LacquerMeasurementUnit]        VARCHAR (50)    NULL,
    [LacquerStatus]                 VARCHAR (50)    NULL,
    [AddedBy]                       VARCHAR (50)    NULL,
    [AddedDate]                     DATETIME        NULL,
    [ReceivedQuantity]              DECIMAL (18, 2) NULL,
    [Remark]                        VARCHAR (100)   NULL,
    [Rank]                          INT             NULL,
    [StartDateTime]                 DATETIME        NULL,
    [EndDateTime]                   DATETIME        NULL,
    [LineNo]                        INT             NULL,
    [ShiftSupervisorWorkerId]       BIGINT          NULL,
    [PricePerUnit]                  DECIMAL (18, 3) NULL
);
GO

ALTER TABLE [dbo].[SaleOrderPostProcessLacquerTable]
    ADD CONSTRAINT [PK_SaleOrderPostProcessLacquerTable] PRIMARY KEY CLUSTERED ([SaleOrderPostProcessLacquerId] ASC);
GO


ALTER TABLE [dbo].[SaleOrderPostProcessLacquerTable]
    ADD CONSTRAINT [FK_SaleOrderPostProcessLacquerTable_FactoryWorkersMaster] FOREIGN KEY ([ShiftSupervisorWorkerId]) REFERENCES [dbo].[FactoryWorkersMaster] ([WorkerId]);
GO

