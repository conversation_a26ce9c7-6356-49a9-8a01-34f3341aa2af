CREATE TABLE [dbo].[SaleOrderPostProcessPrintTable] (
    [SaleOrderPostProcessPrintId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                 BIGINT          NULL,
    [PrintRack]                   BIGINT          NULL,
    [PrintCompletedQuantity]      DECIMAL (18, 2) NULL,
    [PrintWastageQuantity]        DECIMAL (18, 2) NULL,
    [PrintMeasurementUnit]        VARCHAR (50)    NULL,
    [PrintStatus]                 VARCHAR (50)    NULL,
    [AddedBy]                     VARCHAR (50)    NULL,
    [AddedDate]                   DATETIME        NULL,
    [ReceivedQuantity]            DECIMAL (18, 2) NULL,
    [Remark]                      VARCHAR (100)   NULL,
    [Rank]                        INT             NULL,
    [StartDateTime]               DATETIME        NULL,
    [EndDateTime]                 DATETIME        NULL,
    [LineNo]                      INT             NULL,
    [ShiftSupervisorWorkerId]     BIGINT          NULL,
    [PricePerUnit]                DECIMAL (18, 3) NULL
);
GO

ALTER TABLE [dbo].[SaleOrderPostProcessPrintTable]
    ADD CONSTRAINT [PK_SaleOrderPostProcessPrintTable] PRIMARY KEY CLUSTERED ([SaleOrderPostProcessPrintId] ASC);
GO


ALTER TABLE [dbo].[SaleOrderPostProcessPrintTable]
    ADD CONSTRAINT [FK_SaleOrderPostProcessPrintTable_FactoryWorkersMaster] FOREIGN KEY ([ShiftSupervisorWorkerId]) REFERENCES [dbo].[FactoryWorkersMaster] ([WorkerId]);
GO

