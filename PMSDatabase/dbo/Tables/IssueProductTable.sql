CREATE TABLE [dbo].[IssueProductTable] (
    [IssueId]             BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]         BIGINT          NULL,
    [FromStore]           BIGINT          NOT NULL,
    [FromRack]            BIGINT          NULL,
    [ToStore]             BIGINT          NOT NULL,
    [ProductId]           BIGINT          NOT NULL,
    [Quantity]            DECIMAL (18, 2) NOT NULL,
    [Remark]              VARCHAR (50)    NULL,
    [Status]              VARCHAR (50)    NOT NULL,
    [CreatedBy]           VARCHAR (50)    NULL,
    [CreatedDate]         DATETIME        NULL,
    [ActionBy]            VARCHAR (50)    NULL,
    [ActionDate]          DATETIME        NULL,
    [StockProductId]      BIGINT          NULL,
    [StockId]             BIGINT          NULL,
    [ToRack]              BIGINT          NULL,
    [ToNewStockId]        BIGINT          NULL,
    [ToNewStockProductId] BIGINT          NULL,
    [IssueSlipId]         BIGINT          NULL,
    [IssueNumber]         VARCHAR (50)    NULL,
    [DemandQuantity]      DECIMAL (18, 2) NULL,
    [RequestType]         VARCHAR (20)    NULL,
    [ApprovalMode]        VARCHAR (20)    NULL,
    [ParentIssueId]       BIGINT          NULL
);

GO
ALTER TABLE [dbo].[IssueProductTable]
    ADD CONSTRAINT [PK_IssueProductTable] PRIMARY KEY CLUSTERED ([IssueId] ASC);


GO