CREATE TABLE [dbo].[SaleOrderProductionTable] (
    [SaleOrderProductionId]    BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]              BIGINT          NULL,
    [ProductId]                BIGINT          NOT NULL,
    [ManufacturingProductCode] VARCHAR (50)    NULL,
    [ManufacturingProductName] VARCHAR (500)   NULL,
    [Lot]                      DECIMAL (18, 2) NULL,
    [Batch]                    DECIMAL (18, 2) NULL,
    [OrderQuantity]            DECIMAL (18, 2) NULL,
    [LMConstant]               DECIMAL (18, 2) NULL,
    [ExtraProduction]          DECIMAL (18, 2) NULL,
    [ManufacturingQuantity]    DECIMAL (18, 2) NULL,
    [Unit]                     VARCHAR (50)    NULL,
    [FabricProductId]          BIGINT          NULL,
    [FabricColorId]            BIGINT          NULL,
    [ColorId]                  BIGINT          NULL,
    [ColorPrice]               DECIMAL (18, 2) NULL,
    [Barcode]                  VARCHAR (50)    NULL,
    [GrainId]                  BIGINT          NULL,
    [<PERSON>rain<PERSON><PERSON>]               DECIMAL (18, 2) NULL,
    [Thick]                    DECIMAL (18, 2) NULL,
    [ThickPrice]               DECIMAL (18, 2) NULL,
    [Width]                    DECIMAL (18, 2) NULL,
    [WidthPrice]               DECIMAL (18, 2) NULL,
    [ProductionStatus]         VARCHAR (50)    NULL,
    [CostingStatus]            VARCHAR (50)    NULL,
    [SlippagePercent]          DECIMAL (18, 2) NULL,
    [TotalCost]                DECIMAL (18, 2) NULL,
    [AddedBy]                  VARCHAR (50)    NULL,
    [AddedDate]                DATETIME        NULL,
    [LacquerMasterId]          BIGINT          NULL,
    [ProcessFormulationCode]   VARCHAR (50)    NULL,
    [MixingFormulationCode]    VARCHAR (50)    NULL,
    [SalePrice]                DECIMAL (18, 2) NULL,
    [PreSkinGSM]               DECIMAL (18, 2) NULL,
    [SkinGSM]                  DECIMAL (18, 2) NULL,
    [FoamGSM]                  DECIMAL (18, 2) NULL,
    [AdhesiveGSM]              DECIMAL (18, 2) NULL,
    [FabricGSM]                DECIMAL (18, 2) NULL,
    [TotalGSM]                 DECIMAL (18, 2) NULL,
    [SaleOrderStoreId]         BIGINT          NULL
);
GO

ALTER TABLE [dbo].[SaleOrderProductionTable]
    ADD CONSTRAINT [PK_SaleOrderProductionTable] PRIMARY KEY CLUSTERED ([SaleOrderProductionId] ASC);
GO

