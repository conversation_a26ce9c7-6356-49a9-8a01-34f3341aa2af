CREATE TABLE [dbo].[NotificationReportScheduleMappingTable] (
    [ReportId]                BIGINT        IDENTITY (1, 1) NOT NULL,
    [ReportType]              VARCHAR (50)  NULL,
    [ReportName]              NVARCHAR (50) NULL,
    [NotificationGroupUserId] BIGINT        NULL,
    [TemplateMasterId]        BIGINT        NULL,
    [CronExpression]          VARCHAR (50)  NULL,
    [LastRunTime]             DATETIME      NULL,
    [NextRunTime]             DATETIME      NULL,
    [IsActive]                BIT           NOT NULL,
    [TimeZone]                VARCHAR (50)  NULL,
    [AddedBy]                 NVARCHAR (50) NULL,
    [AddedDate]               DATETIME      NULL,
    [Disabled]                BIT           NOT NULL,
    [DisabledBy]              BIGINT        NULL,
    [DisabledDate]            DATETIME      NULL
);
GO

ALTER TABLE [dbo].[NotificationReportScheduleMappingTable]
    ADD CONSTRAINT [PK_NotificationReportScheduleMappingTable] PRIMARY KEY CLUSTERED ([ReportId] ASC);
GO

