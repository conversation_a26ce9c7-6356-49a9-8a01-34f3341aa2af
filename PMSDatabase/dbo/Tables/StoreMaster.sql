CREATE TABLE [dbo].[StoreMaster] (
    [StoreId]               BIGINT        IDENTITY (1, 1) NOT NULL,
    [DeptId]                BIGINT        NULL,
    [BranchId]              BIGINT        NULL,
    [StoreName]             VARCHAR (500) NULL,
    [StoreCode]             VARCHAR (50)  NULL,
    [StoreDesc]             VARCHAR (500) NULL,
    [StoreAddedDate]        DATE          NULL,
    [StoreAddedBy]          VARCHAR (50)  NULL,
    [Disabled]              BIT           NULL,
    [DisabledBy]            VARCHAR (50)  NULL,
    [DisabledDate]          DATETIME      NULL,
    [IsWorkInProgressStore] BIT           NULL
);


GO


ALTER TABLE [dbo].[StoreMaster]
    ADD CONSTRAINT [PK_StoreMaster] PRIMARY KEY CLUSTERED ([StoreId] ASC);


GO
ALTER TABLE [dbo].[StoreMaster]
    ADD CONSTRAINT [DEFAULT_StoreMaster_IsWorkInProgressStore] DEFAULT ((0)) FOR [IsWorkInProgressStore];
GO

