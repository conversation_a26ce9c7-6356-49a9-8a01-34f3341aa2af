CREATE TABLE [dbo].[NotificationTemplateParameterTable]
(
    [ParameterId] BIGINT IDENTITY (1, 1) NOT NULL,
    [TemplateMasterId] BIGINT NOT NULL,
    [ParameterName] VARCHAR (100) NOT NULL,
    [ParameterType] VARCHAR (50) NOT NULL,
    [IsRequired] BIT DEFAULT ((1)) NOT NULL,
    [DefaultValue] NVARCHAR (500) NULL,
    [ValidationRegex] VARCHAR (500) NULL,
    [Sequence] INT NULL
);
GO

ALTER TABLE [dbo].[NotificationTemplateParameterTable]
    ADD CONSTRAINT [PK_NotificationTemplateParameterTable] PRIMARY KEY CLUSTERED ([ParameterId] ASC);
GO

ALTER TABLE [dbo].[NotificationTemplateParameterTable]
    ADD CONSTRAINT [FK_NotificationTemplateParameter_WhatsAppTemplateMaster] FOREIGN KEY ([TemplateMasterId]) REFERENCES [dbo].[WhatsAppTemplateMaster] ([WhatsAppTemplateMasterId]);
GO

