CREATE TABLE [dbo].[PackagingTypeMaster] (
    [PackagingTypeId]   BIGINT       IDENTITY (1, 1) NOT NULL,
    [PackagingTypeName] VARCHAR (50) NULL,
    [PackagingTypeCode] VARCHAR (50) NULL,
    [AddedById]         BIGINT       NULL,
    [AddedDate]         DATETIME     NULL,
    [Disabled]          BIT          NULL,
    [DisabledById]      BIGINT       NULL,
    [DisabledDate]      DATETIME     NULL
);
GO

ALTER TABLE [dbo].[PackagingTypeMaster]
    ADD CONSTRAINT [FK_PackagingTypeMaster_UserMaster_DisabledBy] FOREIGN KEY ([DisabledById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[PackagingTypeMaster]
    ADD CONSTRAINT [FK_PackagingTypeMaster_UserMaster_AddedBy] FOREIGN KEY ([AddedById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[PackagingTypeMaster]
    ADD CONSTRAINT [PK_PackagingTypeMaster] PRIMARY KEY CLUSTERED ([PackagingTypeId] ASC);
GO

