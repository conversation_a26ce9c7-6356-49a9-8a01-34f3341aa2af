CREATE TABLE [dbo].[SaleOrderPostProcessTumblingTable] (
    [SaleOrderPostProcessTumblingId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                    BIGINT          NULL,
    [TumblingRack]                   BIGINT          NULL,
    [TumblingCompletedQuantity]      DECIMAL (18, 2) NULL,
    [TumblingWastageQuantity]        DECIMAL (18, 2) NULL,
    [TumblingMeasurementUnit]        VARCHAR (50)    NULL,
    [TumblingStatus]                 VARCHAR (50)    NULL,
    [AddedBy]                        VARCHAR (50)    NULL,
    [AddedDate]                      DATETIME        NULL,
    [ReceivedQuantity]               DECIMAL (18, 2) NULL,
    [Remark]                         VARCHAR (100)   NULL,
    [Rank]                           INT             NULL,
    [StartDateTime]                  DATETIME        NULL,
    [EndDateTime]                    DATETIME        NULL,
    [LineNo]                         INT             NULL,
    [ShiftSuper<PERSON>WorkerId]        BIGINT          NULL,
    [PricePerUnit]                   DECIMAL (18, 3) NULL
);
GO

ALTER TABLE [dbo].[SaleOrderPostProcessTumblingTable]
    ADD CONSTRAINT [PK_SaleOrderPostProcessTumblingTable] PRIMARY KEY CLUSTERED ([SaleOrderPostProcessTumblingId] ASC);
GO

