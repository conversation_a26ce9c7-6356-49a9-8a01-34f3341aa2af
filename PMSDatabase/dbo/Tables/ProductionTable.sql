CREATE TABLE [dbo].[ProductionTable] (
    [ProductionId]             BIGINT          IDENTITY (1, 1) NOT NULL,
    [ProductId]                BIGINT          NOT NULL,
    [ManufacturingProductCode] VARCHAR (50)    NULL,
    [ManufacturingProductName] VARCHAR (500)   NULL,
    [Lot]                      DECIMAL (18, 2) NULL,
    [Batch]                    DECIMAL (18, 2) NULL,
    [OrderQuantity]            DECIMAL (18, 2) NULL,
    [ManufacturingQuantity]    DECIMAL (18, 2) NULL,
    [Unit]                     VARCHAR (50)    NULL,
    [ColorId]                  BIGINT          NULL,
    [Barcode]                  VARCHAR (50)    NULL,
    [GrainId]                  BIGINT          NULL,
    [ProductionStatus]         VARCHAR (50)    NULL,
    [CostingStatus]            VARCHAR (50)    NULL,
    [SlippagePercent]          DECIMAL (18, 2) NULL,
    [TotalCost]                DECIMAL (18, 2) NULL,
    [AddedBy]                  VARCHAR (50)    NULL,
    [AddedDate]                DATETIME        NULL,
    [LacquerMasterId]          BIGINT          NULL,
    [ProcessFormulationCode]   VARCHAR (50)    NULL,
    [MixingFormulationCode]    VARCHAR (50)    NULL,
    [ColorPrice]               DECIMAL (18, 2) NULL,
    [GrainPrice]               DECIMAL (18, 2) NULL,
    [Thick]                    DECIMAL (18, 2) NULL,
    [ThickPrice]               DECIMAL (18, 2) NULL,
    [Width]                    DECIMAL (18, 2) NULL,
    [WidthPrice]               DECIMAL (18, 2) NULL
);


GO


ALTER TABLE [dbo].[ProductionTable]
    ADD CONSTRAINT [PK_ProductionTable] PRIMARY KEY CLUSTERED ([ProductionId] ASC);


GO