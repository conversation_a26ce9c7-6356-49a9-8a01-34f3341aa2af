CREATE TABLE [dbo].[NotificationTrackingTable]
(
    [NotificationTrackingId] BIGINT IDENTITY (1, 1) NOT NULL,
    [NotificationType] VARCHAR (20) NOT NULL,
    [MessageType] VARCHAR (50) NOT NULL,
    [RecipientId] BIGINT NOT NULL,
    [MessageContent] NVARCHAR (MAX) NULL,
    [MasterTemplateId] BIGINT NULL,
    [Status] VARCHAR (20) NOT NULL,
    [ErrorMessage] NVARCHAR (500) NULL,
    [ProviderMessageId] NVARCHAR (100) NULL,
    [SentTime] DATETIME NOT NULL,
    [DeliveredTime] DATETIME NULL,
    [ReadTime] DATETIME NULL,
    [AddedBy] VARCHAR (50) NOT NULL,
    [AddedDate] DATETIME NOT NULL,
    [NotificationMessageId] NVARCHAR (100) NULL,
    [RecipientMobileNumber] NVARCHAR (50) NULL,
    [RecipientEmail] NVARCHAR (255) NULL,
    [NotificationGroupUserId] BIGINT NULL,
    CONSTRAINT [PK_NotificationTrackingTable] PRIMARY KEY CLUSTERED ([NotificationTrackingId] ASC)
);
GO

CREATE NONCLUSTERED INDEX [IX_NotificationTracking_SentTime]
    ON [dbo].[NotificationTrackingTable]([SentTime] ASC);
GO

CREATE NONCLUSTERED INDEX [IX_NotificationTracking_RecipientId]
    ON [dbo].[NotificationTrackingTable]([RecipientId] ASC);
GO

CREATE NONCLUSTERED INDEX [IX_NotificationTrackingTable_NotificationMessageId]
    ON [dbo].[NotificationTrackingTable]([NotificationMessageId] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_NotificationTrackingTable_RecipientEmail]
    ON [dbo].[NotificationTrackingTable]([RecipientEmail] ASC);
GO


CREATE NONCLUSTERED INDEX [IX_NotificationTrackingTable_RecipientMobileNumber]
    ON [dbo].[NotificationTrackingTable]([RecipientMobileNumber] ASC);
GO

