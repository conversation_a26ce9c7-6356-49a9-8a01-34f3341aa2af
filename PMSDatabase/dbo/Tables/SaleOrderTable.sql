CREATE TABLE [dbo].[SaleOrderTable] (
    [SaleOrderId]                 BIGINT         IDENTITY (1, 1) NOT NULL,
    [SaleOrderType]               VARCHAR (50)   NULL,
    [CustomerId]                  BIGINT         NULL,
    [SaleOrderNumber]             VARCHAR (50)   NULL,
    [SaleOrderDate]               DATETIME       NULL,
    [DeliveryDate]                DATETIME       NULL,
    [AddedBy]                     VARCHAR (50)   NULL,
    [AddedDate]                   DATETIME       NULL,
    [Remarks]                     VARCHAR (1000) NULL,
    [CostingAdded]                BIT            NULL,
    [SaleOrderStatus]             VARCHAR (50)   NULL,
    [CategoryId]                  BIGINT         NULL,
    [WorkPlanStatus]              BIT            NULL,
    [IsRawMaterialIssued]         BIT            NULL,
    [Status]                      INT            NOT NULL,
    [SaleFormulationCodeId]       BIGINT         NULL,
    [ProductionCompletionRemarks] VARCHAR (5000) NULL,
    [IsJumboRequired]             BIT            NULL,
    [ProformaInvoiceId]           BIGINT         NULL,
    [BORNumber]                   VARCHAR (50)   NULL,
    [SaleOrderCode]               VARCHAR (500)  NULL,
    [FinishCode]                  VARCHAR (200)  NULL,
    [HoldBy]                      VARCHAR (50)   NULL,
    [HoldDate]                    DATETIME       NULL,
    [ApprovedBy]                  VARCHAR (50)   NULL,
    [ApprovedDate]                DATETIME       NULL,
    [UpdatedBy]                   VARCHAR (500)  NULL,
    [UpdatedDate]                 DATETIME       NULL
);
GO

ALTER TABLE [dbo].[SaleOrderTable]
    ADD CONSTRAINT [PK_SaleOrderTable] PRIMARY KEY CLUSTERED ([SaleOrderId] ASC);
GO

