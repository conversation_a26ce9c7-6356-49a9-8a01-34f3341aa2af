CREATE TABLE [dbo].[SaleOrderPostProcessVacuumTable] (
    [SaleOrderPostProcessVacuumId] BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleOrderId]                  BIGINT          NULL,
    [VacuumRack]                   BIGINT          NULL,
    [VacuumCompletedQuantity]      DECIMAL (18, 2) NULL,
    [VacuumWastageQuantity]        DECIMAL (18, 2) NULL,
    [VacuumMeasurementUnit]        VARCHAR (50)    NULL,
    [VacuumStatus]                 VARCHAR (50)    NULL,
    [AddedBy]                      VARCHAR (50)    NULL,
    [AddedDate]                    DATETIME        NULL,
    [ReceivedQuantity]             DECIMAL (18, 2) NULL,
    [Remark]                       VARCHAR (100)   NULL,
    [Rank]                         INT             NULL,
    [StartDateTime]                DATETIME        NULL,
    [EndDateTime]                  DATETIME        NULL,
    [LineNo]                       INT             NULL,
    [ShiftSupervisorWorkerId]      BIGINT          NULL,
    [PricePerUnit]                 DECIMAL (18, 3) NULL
);
GO

ALTER TABLE [dbo].[SaleOrderPostProcessVacuumTable]
    ADD CONSTRAINT [PK_SaleOrderPostProcessVacuumTable] PRIMARY KEY CLUSTERED ([SaleOrderPostProcessVacuumId] ASC);
GO

