CREATE TABLE [dbo].[EstimationOrderTable] (
    [EstimationOrderId]           BIGINT          IDENTITY (1, 1) NOT NULL,
    [ProductCategoryId]           BIGINT          NULL,
    [ManufacturingProductName]    VARCHAR (50)    NULL,
    [OrderQuantity]               BIGINT          NULL,
    [LMConstant]                  BIGINT          NULL,
    [ExtraProduction]             BIGINT          NULL,
    [ManufacturingQuantity]       BIGINT          NULL,
    [ColorId]                     BIGINT          NULL,
    [GrainId]                     BIGINT          NULL,
    [GrainPrice]                  DECIMAL (18, 4) NULL,
    [Thick]                       BIGINT          NULL,
    [Width]                       BIGINT          NULL,
    [EstimationPrice]             DECIMAL (18, 4) NULL,
    [ProductionCostLm]            DECIMAL (18, 4) NULL,
    [TotalFinishPrice]            DECIMAL (18, 4) NULL,
    [OverheadCost]                DECIMAL (18, 4) NULL,
    [Rejection]                   DECIMAL (18, 4) NULL,
    [TotalCostPerLm]              DECIMAL (18, 4) NULL,
    [LineSpeed]                   DECIMAL (18, 4) NULL,
    [TotalProfitLoss]             DECIMAL (18, 2) NULL,
    [CustomerId]                  BIGINT          NULL,
    [Remarks]                     VARCHAR (MAX)   NULL,
    [Disabled]                    BIT             DEFAULT ((0)) NULL,
    [AddedBy]                     VARCHAR (MAX)   NULL,
    [DisabledBy]                  VARCHAR (MAX)   NULL,
    [AddedDate]                   DATE            NULL,
    [DisabledDate]                DATE            NULL,
    [EstimationOrderType]         VARCHAR (MAX)   NULL,
    [EstimationFormulationCodeId] BIGINT          NULL
);
GO

ALTER TABLE [dbo].[EstimationOrderTable]
    ADD CONSTRAINT [PK_EstimationOrderTable] PRIMARY KEY CLUSTERED ([EstimationOrderId] ASC);
GO

