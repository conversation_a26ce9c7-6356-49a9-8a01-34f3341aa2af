CREATE TABLE [dbo].[SaleFormulationCodeMaster] (
    [SaleFormulationCodeId]  BIGINT          IDENTITY (1, 1) NOT NULL,
    [SaleFormulationCode]    VARCHAR (50)    NULL,
    [AddedBy]                VARCHAR (50)    NULL,
    [AddedDate]              DATETIME        NULL,
    [CategoryId]             BIGINT          NULL,
    [ThicknessId]            BIGINT          NULL,
    [PreSkinGSM]             DECIMAL (18, 2) NULL,
    [SkinGSM]                DECIMAL (18, 2) NULL,
    [FoamGSM]                DECIMAL (18, 2) NULL,
    [AdhesiveGSM]            DECIMAL (18, 2) NULL,
    [FabricGSM]              DECIMAL (18, 2) NULL,
    [TotalGSM]               DECIMAL (18, 2) NULL,
    [FabricProductId]        BIGINT          NULL,
    [FabricProductQty]       DECIMAL (18, 2) NULL,
    [FinishedProductId]      BIGINT          NULL,
    [Disabled]               BIT             NULL,
    [DisabledBy]             VARCHAR (50)    NULL,
    [DisabledDate]           D<PERSON><PERSON><PERSON>E        NULL,
    [MinSpeed]               DECIMAL (18, 2) NULL,
    [MaxSpeed]               DECIMAL (18, 2) NULL,
    [IsOrderLinkingRequired] BIT             NULL
);
GO

ALTER TABLE [dbo].[SaleFormulationCodeMaster]
    ADD CONSTRAINT [PK_SaleFormulationCodeMaster] PRIMARY KEY CLUSTERED ([SaleFormulationCodeId] ASC);
GO

