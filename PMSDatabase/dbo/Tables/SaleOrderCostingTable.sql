CREATE TABLE [dbo].[SaleOrderCostingTable] (
    [SaleOrderId]              BIGINT          NOT NULL,
    [FabricCost]               DECIMAL (18, 2) NULL,
    [CoatingCost]              DECIMAL (18, 2) NULL,
    [PasteCost_LM]             DECIMAL (18, 2) NULL,
    [GrainCost_LM]             DECIMAL (18, 2) NULL,
    [FabricCost_LM]            DECIMAL (18, 2) NULL,
    [FinishingCost_LM]         DECIMAL (18, 2) NULL,
    [RMCost_LM]                DECIMAL (18, 2) NULL,
    [Rejection]                DECIMAL (18, 2) NULL,
    [PerLMConstant]            DECIMAL (18, 2) NULL,
    [OverheadCost]             DECIMAL (18, 2) NULL,
    [InlineScraping]           DECIMAL (18, 2) NULL,
    [ProductionCost_LM]        DECIMAL (18, 2) NULL,
    [SaleOrderMaterialType]    VARCHAR (50)    NULL,
    [PrintCostPerUnit]         DECIMAL (18, 2) NULL,
    [EmbossingCostPerUnit]     DECIMAL (18, 2) NULL,
    [TumblingCostPerUnit]      DECIMAL (18, 2) NULL,
    [LacquerCostPerUnit]       DECIMAL (18, 2) NULL,
    [VacuumCostPerUnit]        DECIMAL (18, 2) NULL,
    [PackagingCostPerUnit]     DECIMAL (18, 2) NULL,
    [MiscellaneousCostPerUnit] DECIMAL (18, 2) NULL,
    [AddedDate]                DATETIME        NULL,
    [AddedBy]                  VARCHAR (50)    NULL,
    [UpdatedDate]              DATETIME        NULL,
    [UpdatedBy]                VARCHAR (50)    NULL
);
GO

ALTER TABLE [dbo].[SaleOrderCostingTable]
    ADD CONSTRAINT [PK_SaleOrderCosting] PRIMARY KEY CLUSTERED ([SaleOrderId] ASC);
GO

