CREATE TABLE [dbo].[StockLabelTable] (
    [StockLabelId]     BIGINT          IDENTITY (1, 1) NOT NULL,
    [SerialNo]         VARCHAR (50)    NOT NULL,
    [StockId]          BIGINT          NOT NULL,
    [StockProductId]   BIGINT          NOT NULL,
    [OriginalLabelId]  BIGINT          NULL,
    [ProductId]        BIGINT          NOT NULL,
    [Quantity]         DECIMAL (18, 2) NULL,
    [PackagingUnit]    VARCHAR (50)    NULL,
    [MfgDate]          DATE            NULL,
    [ExpiryDate]       DATE            NULL,
    [IsActive]         BIT             NULL,
    [AddedBy]          VARCHAR (50)    NULL,
    [AddedDate]        DATETIME        NULL,
    [UpdatedBy]        VARCHAR (50)    NULL,
    [UpdatedDate]      DATETIME        NULL,
    [Grade]            VARCHAR (20)    NULL,
    [CurrentStoreId]   BIGINT          NULL,
    [CurrentRackId]    BIGINT          NULL,
    [InspectionStatus] VARCHAR (20)    NULL,
    [LabelStatus]      VARCHAR (50)    NULL,
    [AllocationId]     BIGINT          NULL,
    [ShortCode]        VARCHAR (50)    NULL
);
GO

ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [DEFAULT_StockLabelTable_IsActive] DEFAULT ((0)) FOR [IsActive];
GO

ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [PK_StockLabelTable] PRIMARY KEY CLUSTERED ([StockLabelId] ASC);
GO

ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [FK_StockLabelTable_ProductMaster] FOREIGN KEY ([ProductId]) REFERENCES [dbo].[ProductMaster] ([ProductId]);
GO

ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [FK_StockLabelTable_StockProductTable] FOREIGN KEY ([StockProductId]) REFERENCES [dbo].[StockProductTable] ([StockProductId]);
GO

ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [FK_StockLabelTable_StockMaster] FOREIGN KEY ([StockId]) REFERENCES [dbo].[StockMaster] ([StockId]);
GO


ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [FK_StockLabelTable_RackMaster] FOREIGN KEY ([CurrentRackId]) REFERENCES [dbo].[RackMaster] ([RackId]);
GO


ALTER TABLE [dbo].[StockLabelTable]
    ADD CONSTRAINT [FK_StockLabelTable_StoreMaster] FOREIGN KEY ([CurrentStoreId]) REFERENCES [dbo].[StoreMaster] ([StoreId]);
GO

