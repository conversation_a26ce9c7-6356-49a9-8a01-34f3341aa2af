CREATE TABLE [dbo].[ProductTransferTable] (
    [TransferId]    BIGINT          IDENTITY (1, 1) NOT NULL,
    [FromProductId] BIGINT          NULL,
    [ToProductId]   BIGINT          NULL,
    [Quantity]      DECIMAL (18, 2) NULL,
    [Status]        VARCHAR (50)    NULL,
    [RequestReason] VARCHAR (250)   NULL,
    [AddedById]     BIGINT          NULL,
    [AddedDate]     DATETIME        NULL,
    [ActionById]    BIGINT          NULL,
    [ActionDate]    DATETIME        NULL,
    [ActionRemark]  VARCHAR (250)   NULL
);
GO

ALTER TABLE [dbo].[ProductTransferTable]
    ADD CONSTRAINT [FK_ProductTransferTable_ProductMaster_ToProduct] FOREIGN KEY ([ToProductId]) REFERENCES [dbo].[ProductMaster] ([ProductId]);
GO

ALTER TABLE [dbo].[ProductTransferTable]
    ADD CONSTRAINT [FK_ProductTransferTable_ProductMaster_FromProduct] FOREIGN KEY ([FromProductId]) REFERENCES [dbo].[ProductMaster] ([ProductId]);
GO

ALTER TABLE [dbo].[ProductTransferTable]
    ADD CONSTRAINT [FK_ProductTransferTable_UserMaster_AddedBy] FOREIGN KEY ([AddedById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[ProductTransferTable]
    ADD CONSTRAINT [FK_ProductTransferTable_UserMaster_ActionBy] FOREIGN KEY ([ActionById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[ProductTransferTable]
    ADD CONSTRAINT [PK_ProductTransferTable] PRIMARY KEY CLUSTERED ([TransferId] ASC);
GO

