CREATE TABLE [dbo].[NotificationRateLimitTable] (
    [RateLimitId]           BIGINT       IDENTITY (1, 1) NOT NULL,
    [NotificationType]      VARCHAR (20) NOT NULL,
    [RecipientId]           BIGINT       NOT NULL,
    [MessagesSentToday]     INT          DEFAULT ((0)) NOT NULL,
    [MessagesSentThisMonth] INT          DEFAULT ((0)) NOT NULL,
    [LastMessageTime]       DATETIME     NOT NULL,
    [LastUpdated]           DATETIME     NOT NULL
);
GO

ALTER TABLE [dbo].[NotificationRateLimitTable]
    ADD CONSTRAINT [PK_NotificationRateLimitTable] PRIMARY KEY CLUSTERED ([RateLimitId] ASC);
GO

CREATE NONCLUSTERED INDEX [IX_NotificationRateLimit_RecipientId]
    ON [dbo].[NotificationRateLimitTable]([RecipientId] ASC);
GO

