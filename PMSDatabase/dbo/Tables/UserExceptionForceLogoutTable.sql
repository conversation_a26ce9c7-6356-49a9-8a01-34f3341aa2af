CREATE TABLE [dbo].[UserExceptionForceLogoutTable] (
    [UserExceptionId] BIGINT   IDENTITY (1, 1) NOT NULL,
    [UserId]          BIGINT   NULL,
    [IsActive]        BIT      NULL,
    [AddedById]       BIGINT   NULL,
    [AddedDate]       DATETIME NULL
);
GO

ALTER TABLE [dbo].[UserExceptionForceLogoutTable]
    ADD CONSTRAINT [FK_UserExceptionForceLogoutTable_UserMaster_AddedBy] FOREIGN KEY ([AddedById]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[UserExceptionForceLogoutTable]
    ADD CONSTRAINT [FK_UserExceptionForceLogoutTable_UserMaster] FOREIGN KEY ([UserId]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[UserExceptionForceLogoutTable]
    ADD CONSTRAINT [PK_UserExceptionForceLogoutTable] PRIMARY KEY CLUSTERED ([UserExceptionId] ASC);
GO

