CREATE TABLE [dbo].[FileUploadTable] (
    [FileUploadId]  BIGINT        IDENTITY (1, 1) NOT NULL,
    [EntityName]    VARCHAR (50)  NOT NULL,
    [EntityId]      BIGINT        NOT NULL,
    [ContainerName] VARCHAR (50)  NOT NULL,
    [FilePath]      VARCHAR (250) NOT NULL,
    [FileName]      VARCHAR (150) NOT NULL,
    [UploadedBy]    BIGINT        NOT NULL,
    [UploadedDate]  DATETIME      NOT NULL
);
GO

ALTER TABLE [dbo].[FileUploadTable]
    ADD CONSTRAINT [FK_FileUploadTable_UserMaster] FOREIGN KEY ([UploadedBy]) REFERENCES [dbo].[UserMaster] ([UserId]);
GO

ALTER TABLE [dbo].[FileUploadTable]
    ADD CONSTRAINT [PK_FileUploadTable] PRIMARY KEY CLUSTERED ([FileUploadId] ASC);
GO

