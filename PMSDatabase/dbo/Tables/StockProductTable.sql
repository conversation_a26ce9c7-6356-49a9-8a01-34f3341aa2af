CREATE TABLE [dbo].[StockProductTable] (
    [StockProductId]          BIGINT          IDENTITY (1, 1) NOT NULL,
    [StockId]                 BIGINT          NOT NULL,
    [ProductId]               BIGINT          NOT NULL,
    [SKU]                     VARCHAR (500)   NULL,
    [Barcode]                 VARCHAR (500)   NULL,
    [Quantity]                DECIMAL (18, 2) NULL,
    [ManufacturedDate]        DATETIME        NULL,
    [ExpiryDate]              DATETIME        NULL,
    [Unit]                    VARCHAR (50)    NULL,
    [PricePerUnit]            DECIMAL (18, 2) NULL,
    [Grade]                   VARCHAR (50)    NULL,
    [AcceptedQuantity]        DECIMAL (18, 2) NULL,
    [RejectedQuantity]        DECIMAL (18, 2) NULL,
    [Comments]                VARCHAR (1000)  NULL,
    [ReceivedQuantity]        DECIMAL (18, 2) NULL,
    [PostProcess]             VARCHAR (500)   NULL,
    [FreightPerUnit]          DECIMAL (18, 2) NULL,
    [MiscPerUnit]             DECIMAL (18, 2) NULL,
    [InvoicePricePerUnit]     DECIMAL (18, 2) NULL,
    [ShippingHandlingPerUnit] DECIMAL (18, 2) NULL
);


GO


ALTER TABLE [dbo].[StockProductTable]
    ADD CONSTRAINT [PK_StockProductTable] PRIMARY KEY CLUSTERED ([StockProductId] ASC);


GO