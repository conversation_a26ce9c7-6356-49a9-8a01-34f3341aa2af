﻿<?xml version="1.0" encoding="utf-8"?>
<SchemaComparison>
  <Version>10</Version>
  <SourceModelProvider>
    <ConnectionBasedModelProvider>
      <ConnectionString>Data Source=pms-mssqlserver-dev.database.windows.net;Initial Catalog=pmsdb;User ID="<PERSON> - <PERSON><EMAIL>";Connect Timeout=30;Encrypt=True;Trust Server Certificate=False;Authentication=ActiveDirectoryInteractive;Application Name=azdata;Command Timeout=30</ConnectionString>
    </ConnectionBasedModelProvider>
  </SourceModelProvider>
  <TargetModelProvider>
    <ProjectBasedModelProvider>
      <ProjectFilePath>/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/PMSDatabase.sqlproj</ProjectFilePath>
      <TargetScripts>[/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/StoredProcedures/usp_upd_FabricGSM.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/__EFMigrationsHistory.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/AuditTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/BankMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/BranchMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ColorMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ConfigTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ConsumeStockProductMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/CustomerMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/DeliveryTermMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/DemandTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/DeptMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ElementMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmailConfigTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmailGroupMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmailGroupTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmailSubscriptionCustomerTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmailTrackingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EmbossingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationCodeMixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationFabricProductDetail.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationFinishingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationMixingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationOrderStatus.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/EstimationOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/FactoryWorkersMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/FormulationCodeMixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/FormulationCodeMixingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/FormulationCodePrefixMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/GateInInvoiceMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/GateInTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/GrainMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/GRNMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/InspectionCancellationTrackingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/InspectionFormulationCodeMixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/InspectionFormulationCodeMixingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/InspectionSaleFormulationCodeMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/InvoiceMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/IssueProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/IssueSlipTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/JumboDispatchTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/JumboInspectionTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/KnittingFabricWeightInputTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/LacquerMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/LacquerRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/LinkedSaleOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MBFormulationMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MBFormulationProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MBFormulationRawMaterialMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MBFormulationRawMaterialProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MeasurementConversionMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MeasureUnitMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MixingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/MixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/NotificationGroupsTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OrderProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OutpassItemTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OutpassMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OutPassPurposeMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OverheadColumnMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OverheadCostingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OverheadCostMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OverheadCostMonthlyMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/OverHeadCostTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PaymentTermMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PostProcessCostingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PrintMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProcessMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProcessRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductCategoryMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductFirstSubCategoryMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionElementTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionEmbossingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionMixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionMixingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionPrintMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionProcessRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionProcessTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionTumblingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductionVacuumMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductMasterExtension.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProductSecSubCategoryMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProformaInvoiceItemTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ProformaInvoiceTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PurchaseOrderProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PurchaseOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/PurchaseOrderTimelineTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/RackMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ReportMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ResponsibilityMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleFormulationCodeMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderCostingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderDispatchItemsTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderDispatchReadyTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderDispatchTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessEmbossingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessLacquerTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessPrintTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessTumblingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderPostProcessVacuumTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionCompleteTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionElementTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionEmbossingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionLacquerRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionMiscellaneousRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionMixingRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionMixingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionPrintMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionRawMaterialTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionTumblingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderProductionVacuumMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SaleOrderTimelineTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StagesTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockPriceTrackingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockProductAllocationTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockProductManageRejectedTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockProductRejectedDispatchTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StockProductTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/StoreMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/SupplierMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/TagMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/ThicknessMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/TransportCompanyMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/TransportVehicleTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/TumblingMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/UserMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/UsernameUserRoleMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/UserRoleMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/UserRoleResponsibilityMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/UserStoreMappingTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/VacuumMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WhatsAppConfigTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WhatsAppSubscriptionCustomerTable.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WhatsAppTemplateMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WidthMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WorkerDesignationMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WorkPlanJumboMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WorkPlanMaster.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WorkPlanOrder.sql,/Users/<USER>/Documents/Misay/Repos/pms-database/PMSDatabase/dbo/Tables/WorkPlanOrderTrackingTable.sql]</TargetScripts>
      <Dsp>AzureV12</Dsp>
      <FolderStructure>SchemaObjectType</FolderStructure>
    </ProjectBasedModelProvider>
  </TargetModelProvider>
  <SchemaCompareSettingsService>
    <SchemaCompareSettingsService>
      <PropertyElementName>
        <Name>Version</Name>
        <Value>1</Value>
      </PropertyElementName>
    </SchemaCompareSettingsService>
    <ConfigurationOptionsElement>
      <PropertyElementName>
        <Name>PlanGenerationType</Name>
        <Value>SqlDeploymentOptions</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowExistingModelErrors</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowIncompatiblePlatform</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowTableRecreation</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>BackupDatabaseBeforeChanges</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreIndexesStatisticsOnEnclaveEnabledColumns</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>BlockOnPossibleDataLoss</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>BlockWhenDriftDetected</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>CompareUsingTargetCollation</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>CommentOutSetVarDeclarations</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>CreateNewDatabase</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DeployDatabaseInSingleUserMode</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DisableAndReenableDdlTriggers</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DisableIndexesForDataPhase</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DisableParallelismForEnablingIndexes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotAlterChangeDataCaptureObjects</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotAlterReplicatedObjects</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropConstraintsNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropDmlTriggersNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropExtendedPropertiesNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropIndexesNotInSource</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropPermissionsNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropObjectsNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropRoleMembersNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DropStatisticsNotInSource</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>GenerateSmartDefaults</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>HashObjectNamesInLogs</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDdlTriggerOrder</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDdlTriggerState</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreObjectPlacementOnPartitionScheme</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreAuthorizer</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDefaultSchema</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreRouteLifetime</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreCryptographicProviderFilePath</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreComments</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreWhitespace</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreKeywordCasing</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreSemicolonBetweenStatements</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnorePartitionSchemes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreTablePartitionOptions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreWithNocheckOnCheckConstraints</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreWithNocheckOnForeignKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreIdentitySeed</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreIncrement</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreFillFactor</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreIndexPadding</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreColumnCollation</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreColumnOrder</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreLockHintsOnIndexes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreTableOptions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreIndexOptions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDmlTriggerOrder</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDmlTriggerState</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreAnsiNulls</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreQuotedIdentifiers</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreUserSettingsObjects</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreFilegroupPlacement</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreFullTextCatalogFilePath</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreFileAndLogFilePath</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreLoginSids</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreNotForReplication</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreFileSize</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreSensitivityClassifications</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowUnsafeRowLevelSecurityDataMovement</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IncludeCompositeObjects</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IncludeTransactionalScripts</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IsAlwaysEncryptedParameterizationEnabled</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>NoAlterStatementsToChangeCLRTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>PopulateFilesOnFileGroups</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>PreserveIdentityLastValues</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>RegisterDataTierApplication</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>PerformIndexOperationsOnline</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>RebuildIndexesOfflineForDataPhase</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>RestoreSequenceCurrentValue</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptDatabaseCollation</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptDatabaseCompatibility</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptDatabaseOptions</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptDeployStateChecks</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptFileSize</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptNewConstraintValidation</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ScriptRefreshModule</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>TargetDatabaseName</Name>
        <Value>PMSDatabase.sqlproj</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>TargetConnectionString</Name>
        <Value>Integrated Security=True;Pooling=False;Connect Timeout=30</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>TreatVerificationErrorsAsWarnings</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>UnmodifiableObjectWarnings</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>VerifyCollationCompatibility</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>VerifyDeployment</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>RunDeploymentPlanExecutors</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowDropBlockingAssemblies</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotEvaluateSqlCmdVariables</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropAggregates</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropApplicationRoles</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropAssemblies</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropAsymmetricKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropAudits</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropBrokerPriorities</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropCertificates</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropClrUserDefinedTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropColumnEncryptionKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropColumnMasterKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropContracts</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropCredentials</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDatabaseScopedCredentials</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropCryptographicProviders</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDatabaseAuditSpecifications</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDatabaseRoles</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDatabaseTriggers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreDatabaseWorkloadGroups</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDatabaseWorkloadGroups</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>IgnoreWorkloadClassifiers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropWorkloadClassifiers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropDefaults</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropEndpoints</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropErrorMessages</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropEventNotifications</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropEventSessions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExtendedProperties</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalDataSources</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalFileFormats</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalLanguages</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalLibraries</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalStreamingJobs</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropExternalStreams</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropFilegroups</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropFiles</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropFileTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropFullTextCatalogs</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropFullTextStoplists</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropTableValuedFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropLinkedServerLogins</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropLinkedServers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropLogins</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropMessageTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropPartitionFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropPartitionSchemes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropPermissions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropQueues</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropRemoteServiceBindings</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropRoleMembership</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropRoutes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropRules</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropScalarValuedFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSearchPropertyLists</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSecurityPolicies</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSequences</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropServerAuditSpecifications</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropServerRoleMembership</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropServerRoles</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropServerTriggers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropServices</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSignatures</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropStoredProcedures</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSymmetricKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropSynonyms</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropUserDefinedDataTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropUserDefinedTableTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropUsers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropViews</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>DoNotDropXmlSchemaCollections</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeAggregates</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeApplicationRoles</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeAssemblies</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeAsymmetricKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeAudits</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeBrokerPriorities</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeCertificates</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeClrUserDefinedTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeColumnEncryptionKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeColumnMasterKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeContracts</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeCredentials</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeDatabaseScopedCredentials</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeCryptographicProviders</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeDatabaseAuditSpecifications</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeDatabaseRoles</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeDatabaseTriggers</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeDefaults</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeEndpoints</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeErrorMessages</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeEventNotifications</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalDataSources</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalFileFormats</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalLanguages</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalLibraries</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalStreamingJobs</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeExternalStreams</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeEventSessions</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeFilegroups</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeFiles</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeFileTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeFullTextCatalogs</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeFullTextStoplists</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeTableValuedFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeLinkedServerLogins</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeLinkedServers</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeLogins</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeMessageTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludePartitionFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludePartitionSchemes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeQueues</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeRemoteServiceBindings</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeRoutes</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeRules</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeScalarValuedFunctions</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSearchPropertyLists</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSecurityPolicies</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSequences</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeServerAuditSpecifications</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeServerRoleMembership</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeServerRoles</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeServerTriggers</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeServices</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSignatures</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeStoredProcedures</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSymmetricKeys</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeSynonyms</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeTables</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeUserDefinedDataTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeUserDefinedTableTypes</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeUsers</Name>
        <Value>True</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeViews</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>ExcludeXmlSchemaCollections</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowExternalLibraryPaths</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>AllowExternalLanguagePaths</Name>
        <Value>False</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlAssemblyFile</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseOptions</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlRole</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlFile</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlUser</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlServerAudit</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlCredential</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlCryptographicProvider</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseAuditSpecification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseEncryptionKey</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseCredential</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlEndpoint</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlErrorMessage</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlServerEventNotification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseEventNotification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlQueueEventNotification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlEventNotification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlEventSession</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlDatabaseEventSession</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlLinkedServerLogin</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlLinkedServer</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlLogin</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlMasterKey</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlRoute</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlServerAuditSpecification</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlServerRoleMembership</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlUserDefinedServerRole</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
      <PropertyElementName>
        <Name>Microsoft.Data.Tools.Schema.Sql.SchemaModel.SqlServerDdlTrigger</Name>
        <Value>ExcludedType</Value>
      </PropertyElementName>
    </ConfigurationOptionsElement>
  </SchemaCompareSettingsService>
  <SchemaCompareViewSettings>
    <GroupBy>2</GroupBy>
    <ZoomLevel>100</ZoomLevel>
    <Filter>Equals_Objects,Not_Supported_Deploy</Filter>
  </SchemaCompareViewSettings>
</SchemaComparison>