﻿using PmsEntity.ViewModel;
using PmsCommon;
using PmsData.DataFn;

namespace PmsBusiness
{
    public class StorageFunctions
    {
        private readonly GlobalDataEntity _globalData;
        public StorageFunctions(GlobalDataEntity globalData)
        {
            _globalData = globalData;
        }
        public StorageTokenVm GetStorageContainerToken(string containername)
        {
            var data = new StorageDataFn(_globalData);
            return data.GetStorageContainerToken(containername);
        }
        public StorageBlobSasResponseVm GetStorageBlobSASTokenByUploadId(long fileUploadId)
        {
            var data = new StorageDataFn(_globalData);
            return data.GetStorageBlobSASTokenByUploadId(fileUploadId);
        }
    }
}
