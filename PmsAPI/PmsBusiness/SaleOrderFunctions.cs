﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using System.Security.Cryptography;
using PmsData.Models;
using System.Net;
using PmsCommon;
using System.IO;

namespace PmsBusiness
{
    public class SaleOrderFunctions
    {
        public GlobalDataEntity GlobalData;
        public SaleOrderFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<SaleOrderTableVm> GetAllSaleOrderData()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetAllSaleOrderData();
        }
        public List<SaleOrderTableVm> GetAllSaleOrderDataForLinking()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetAllSaleOrderDataForLinking();
        }
        public List<SaleOrderTableVm> GetAllSaleOrderWithFilter(SaleOrderRequestFilter filter)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetAllSaleOrderWithFilter(filter);
        }
        public List<SaleOrderTableVm> GetAllSaleOrderForFilterByStatus(List<string> saleorderstatus)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetAllSaleOrderForFilterByStatus(saleorderstatus);
        }
        
        public List<SaleOrderTableVm> GetAllSaleOrderDataByStatus(string status)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetAllSaleOrderDataByStatus(status);
        }

        public ApiFunctionResponseVm AddSaleOrder(SaleOrderTableVm SaleOrder)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddSaleOrder(SaleOrder);
        }
        public ApiFunctionResponseVm EditPostProcess(SaleOrderTableVm SaleOrder)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.EditPostProcess(SaleOrder);
        }
        public ApiFunctionResponseVm UpdateSaleOrder(SaleOrderTableVm SaleOrder)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.UpdateSaleOrder(SaleOrder);
        }
        public ApiFunctionResponseVm UpdateSaleOrderGSM(SaleOrderProductionTableVm request)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.UpdateSaleOrderGSM(request);
        }
        public ApiFunctionResponseVm DeleteSaleOrder(long SaleOrder)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.DeleteSaleOrder(SaleOrder);
        }
        public SaleOrderTableVm GetSaleOrderDataById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderDataById(pid);
        }
        public SaleOrderCostingViewTableVm GetSaleOrderCostingViewDataById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderCostingViewDataById(pid);
        }
        public SaleOrderTableVm GetSaleOrderDataByIdForGradePrint(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderDataByIdForGradePrint(pid);
        }
        public SaleOrderTableVm GetSaleOrderDataForViewById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderDataForViewById(pid);
        }

        public SaleOrderTableVm GetSaleOrderDispatchDetailById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderDispatchDetailById(pid);
        }

        public SaleOrderTableVm GetSaleOrderPostProcessDataById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderPostProcessDataById(pid);
        }
        public SaleOrderTableVm GetSaleOrderCostingDataById(long pid)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderCostingDataById(pid);
        }
        public List<SaleFormulationCodeMasterVm> GetSaleFormulationCodeList()
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.GetSaleFormulationCodeList();
        }

        public SaleFormulationCodeMasterVm GetFormulationCodeById(long SaleFormulationCodeId)
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.GetFormulationCodeById(SaleFormulationCodeId);
        }

        public ApiFunctionResponseVm DisableFormulationCode(long saleFormulationCodeId)
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.DisableFormulationCode(saleFormulationCodeId);
        }
        public List<SaleFormulationCodeMasterVm> GetSaleFormulationCodeForFilter()
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.GetSaleFormulationCodeForFilter();
        }
        public ApiFunctionResponseVm AddFormulationCode(SaleFormulationCodeMasterVm mix)
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.AddFormulationCode(mix);
        }
        public ApiFunctionResponseVm UpdateFormulationCode(SaleFormulationCodeMasterVm mix)
        {
            var data = new FormulationCodeDataFn(GlobalData);
            return data.UpdateFormulationCode(mix);
        }
        public FormulationProductCalculatePasteReqResponse GetFormulationProductCalculatePasteReqQuantity(FormulationProductCalculatePasteReqRequest req)
        {
            var data = new CalculationsDataFn();
            return data.GetFormulationProductCalculatePasteReqQuantity(req);
        }

        public ApiFunctionResponseVm GetGSMFromPasteQuantity(FormulationProductCalculatePasteReqRequest req)
        {
            var data = new CalculationsDataFn();
            return data.GetGSMFromPasteQuantity(req);
        }
        // public ApiFunctionResponseVm UpdateSaleOrderStatus(long saleorderid, PmsCommon.ESalesOrderStatus status)
        // {
        //     var data = new SaleOrderDataFn(GlobalData);
        //     return data.UpdateSaleOrderStatus(saleorderid, status, "");
        // }

        public ApiFunctionResponseVm StartSaleOrderProduction(List<SaleOrderTableVm> saleorder)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.StartSaleOrderProduction(saleorder);
        }
        public ResultSaleOrder RemoveSaleOrderJumbo(SaleOrderJumboRemoveVm items)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.RemoveSaleOrderJumbo(items);
        }
        public ResultSaleOrder CompleteSaleOrderProduction(SaleOrderCompleteProductionVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.CompleteSaleOrderProduction(item);
        }

        public ApiFunctionResponseVm AddPostProcessPrint(SaleOrderPostProcessPrintTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddPostProcessPrint(item);
        }

        public ApiFunctionResponseVm AddPostProcessEmbossing(SaleOrderPostProcessEmbossingTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddPostProcessEmbossing(item);
        }

        public ApiFunctionResponseVm AddPostProcessVacuum(SaleOrderPostProcessVacuumTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddPostProcessVacuum(item);
        }

        public ApiFunctionResponseVm AddPostProcessLacquer(SaleOrderPostProcessLacquerTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddPostProcessLacquer(item);
        }

        public ApiFunctionResponseVm AddPostProcessTumbling(SaleOrderPostProcessTumblingTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddPostProcessTumbling(item);
        }

        // public ApiFunctionResponseVm AddSaleOrderDispatchReady(SaleOrderDispatchReadyTableVm item)
        // {
        //     var data = new SaleOrderDataFn(GlobalData);
        //     return data.AddSaleOrderDispatchReady(item);
        // }

        public ApiFunctionResponseVm AddSaleOrderDispatch(List<SaleOrderDispatchTableVm> item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddSaleOrderDispatch(item);
        }
        public ApiFunctionResponseVm AddJumboDispatch(JumboDispatchTableVm jumboDispatch)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddJumboDispatch(jumboDispatch);
        }
        public ApiFunctionResponseVm JumboDispatchEdit(JumboDispatchTableVm jumboDispatch)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.JumboDispatchEdit(jumboDispatch);
        }
        public ApiFunctionResponseVm JumboDispatchRemoveRoll(JumboDispatchTableVm jumboDispatch)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.JumboDispatchRemoveRoll(jumboDispatch);
        }
        public List<JumboDispatchListVm> GetPackingNumbers()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetPackingNumbers();
        }
        // public ApiFunctionResponseVm AddJumboInspection(WorkPlanJumboMasterVm item)
        // {
        //     var data = new SaleOrderDataFn(GlobalData);
        //     return data.AddJumboInspection(item);
        // }
        public ApiFunctionResponseVm AddJumboInspectionSingleObj(JumboInspectionTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddJumboInspectionSingleObj(item);
        }
        public ApiFunctionResponseVm RemoveJumboInspection(JumboInspectionTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.RemoveJumboInspection(item);
        }
        public ApiFunctionResponseVm CompleteJumboInspection(WorkPlanJumboMasterVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.CompleteJumboInspection(item);
        }
        public ApiFunctionResponseVm ReOpenJumboInspection(WorkPlanJumboMasterVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.ReOpenJumboInspection(item);
        }
        public ApiFunctionResponseVm AddJumboInspectionDispatchedQuantity(List<JumboInspectionTableVm> jumboinspection)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddJumboInspectionDispatchedQuantity(jumboinspection);
        }
        public ApiFunctionResponseVm AddUpdateSaleOrderCosting(SaleOrderTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.AddUpdateSaleOrderCosting(item);
        }
        public List<JumboInspectionTableVm> GetJumboInspectionList()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetJumboInspectionList();
        }
        public List<JumboDispatchListVm> GetJumboDispatchList()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetJumboDispatchList();
        }
        public List<JumboDispatchListVm> GetJumboDispatchListWithFilter(JumboDispatchListFilterVm filter)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetJumboDispatchListWithFilter(filter);
        }
        public JumboDispatchListVm GetJumboDispatchListByDispatchId(long dispatchId)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetJumboDispatchListByDispatchId(dispatchId);
        }
        public List<JumboInspectionTableVm> GetJumboInspectionListWithFilter(JumboInspectionFilterVm filter)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetJumboInspectionListWithFilter(filter);
        }
        public ApiFunctionResponseVm UpdateManufacturingQuantity(long saleorderid, decimal qty)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.UpdateManufacturingQuantity(saleorderid, qty);
        }
        public List<PostProcessCostingMasterVm> GetPostProcessCosting()
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetPostProcessCosting();
        }
        public ApiFunctionResponseVm LinkSaleOrder(List<LinkedSaleOrderTableVm> listItem)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.LinkSaleOrder(listItem);
        }
        public ApiFunctionResponseVm RemoveLinkSaleOrder(LinkedSaleOrderTableVm item)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.RemoveLinkSaleOrder(item);
        }
        public ApiFunctionResponseVm HoldSaleOrder(long saleorderId)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.HoldSaleOrder(saleorderId);
        }
        public ApiFunctionResponseVm ApproveSaleOrder(long saleorderId)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.ApproveSaleOrder(saleorderId);
        }
        public SaleOrderTableVm GetSaleOrderFormulationMixing(long saleorderId)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSaleOrderFormulationMixing(saleorderId);
        }
        public ApiFunctionResponseVm SendDispatchPackagingEmail(Stream st, long poid, string[] emaillist)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.SendDispatchPackagingEmail(st, poid, emaillist);
        }
        public List<SaleOrderTableVm> GetPostProcessListWithFilters(SaleOrderRequestFilter filter)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetPostProcessListWithFilters(filter);
        }
        public ApiFunctionResponseVm GetSalePriceByProduct(SaleOrderRequestFilter filter)
        {
            var data = new SaleOrderDataFn(GlobalData);
            return data.GetSalePriceByProduct(filter);
        }
    }
}

