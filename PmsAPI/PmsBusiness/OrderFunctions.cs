﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class OrderFunctions
    {
        public List<OrderTableVm> GetAllOrders()
        {
            var data = new OrderDataFn();
            return data.GetAllOrders();
        }

        public ApiFunctionResponseVm AddOrders(OrderTableVm order)
        {
            var data = new OrderDataFn();
            return data.AddOrders(order);
        }
    }
}
