using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;
using Azure.Core;
using PmsCommon;

namespace PmsBusiness
{
    public class ReportFunctions
    {
        public GlobalDataEntity GlobalData;
        public ReportFunctions(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<YieldReportVm> yieldReport(YieldReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.YieldReport(request);
        }
        public List<ProductionPlanningReportResponseVm> ProductionPlanningReport(ProductionPlanningReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.ProductionPlanningReport(request);
        }
        public List<ProductionPlanningReportResponseVm> ProductionStatusReport(ProductionPlanningReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.ProductionStatusReport(request);
        }
        public List<PasteConsumptionReportResponseVm> PasteConsumptionReport(ProductionPlanningReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData  );
            return data.PasteConsumptionReport(request);
        }
        public List<SalesReportResponseVm> SalesReport(SalesReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.SalesReport(request);
        }
        public List<PurchaseOrderReportVm> PurchaseReport(PurchaseReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.PurchaseReport(request);
        }
        public List<SaleOrderPostProcessReportVm> GetSaleOrderPostProcessReport(PostProcessReportRequestVm filter)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetSaleOrderPostProcessReport(filter);
        }
        public List<ProductStockSummaryForJumboGradeVm> GetProductStockSummaryForJumboGrade(ProductStockSummaryFilters filter)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetProductStockSummaryForJumboGrade(filter);
        }
        public List<WastageReportVm> WastageReport(ReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.WastageReport(request);
        }
        public ApiFunctionResponseVm SendProductionPlanningReportEmailOnDemand(ProductionPlanningReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.SendProductionPlanningReportEmailOnDemand(request);
        }
        public ApiFunctionResponseVm SendYieldReportEmailOnDemand(YieldReportRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.SendYieldReportEmailOnDemand(request);
        }
        public List<SaleOrderTimelineTableVm> SaleOrderTimelineReport(SaleOrderTimelineRequestFiltervm filter)
        {
            var data = new ReportDataFn(GlobalData);
            return data.SaleOrderTimelineReport(filter);
        }
        public TopSellingProductsResponseVm GetTopSellingProducts(TopSellingProductsRequestVm filter)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetTopSellingProducts(filter);
        }
        public List<ConsumeStockProductReportResponseVm> GetAllConsumeStockProductsWithFilters(ConsumeStockProductRequestVm filters)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetAllConsumeStockProductsWithFilters(filters);
        }
        public ReceivedOrderResponseVm GetReceivedOrderReport(ChartReportsRequestVm filters)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetReceivedOrderReport(filters);
        }
        public MfgHearbeatReportResponseVm GetMfgHearbeatReport(MfgHearbeatReportRequestVm filter)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetMfgHearbeatReport(filter);
        }

        public GateDashboardVm GetGateDashboardMetrics(GateDashboardRequestVm request)
        {
            var data = new ReportDataFn(GlobalData);
            return data.GetGateDashboardMetrics(request);
        }
    }
}
