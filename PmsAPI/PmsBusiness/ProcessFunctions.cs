﻿using PmsEntity.ViewModel;
using PmsData.DataFn;
using PmsData;
using System;
using System.Collections.Generic;
using System.Text;

namespace PmsBusiness
{
    public class ProcessFunctions
    {
        public List<ProcessMasterVm> GetAllProcess()
        {
            var data = new ProcessDataFn();
            return data.GetAllProcess();
        }

        public ApiFunctionResponseVm AddProcess(ProcessMasterVm Process)
        {
            var data = new ProcessDataFn();
            return data.AddProcess(Process);
        }

        public ApiFunctionResponseVm UpdateProcess(ProcessMasterVm Process)
        {
            var data = new ProcessDataFn();
            return data.UpdateProcess(Process);
        }
    }
}
