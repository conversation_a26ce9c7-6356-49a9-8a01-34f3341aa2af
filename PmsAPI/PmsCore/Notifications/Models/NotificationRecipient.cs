
using System.Collections.Generic;

namespace PmsCore.Notifications.Models
{
    public class NotificationRecipient
    {
        public long Id { get; set; }
        public string Name { get; set; }
        public string EmailId { get; set; }
        public List<string> MobileNumbers { get; set; }
        public string MobileNumberString { get; set; }
        public bool EnableToEmail { get; set; }
        public bool EnableCCEmail { get; set; }
        public bool EnableBCCEmail { get; set; }
        public bool IsWhatsAppNotificationEnabled { get; set; }
        public string UserType { get; set; }  // "Internal" or "External"
        public long? UserMasterId { get; set; }
        public string NotificationType { get; set; }
        public string ReportType { get; set; }
        public long WhatsAppTemplateMasterId { get; set; }
    }

    public class NotificationTemplate
    {
        public string NotificationType { get; set; }
        public string ReportType { get; set; }
        public string Subject { get; set; }
        public long? WhatsAppProviderTemplateId { get; set; }
        public long WhatsAppTemplateMasterId { get; set; }
    }
}