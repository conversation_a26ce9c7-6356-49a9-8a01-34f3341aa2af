using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace PmsCore.Notifications.Interfaces
{
    public interface INotificationService
    {
        // Event-based notifications
        Task SendSaleOrderStatusNotification(long saleOrderId, long stageId);
        Task SendLowStockNotification(long productId);

        // Schedule-based notifications
        Task SendScheduledReport(string reportType);
        Task SendYieldReportSummaryWhatsApp(DateTime fromDate, DateTime toDate, string TriggerType);

        // On-demand notifications
        Task SendNotification(string notificationType, long recipientId, Dictionary<string, string> parameters, string messageType, long templateId);
        Task TriggerOnDemandNotification(long notificationGroupId, Dictionary<string, string> parameters);
    }
}
