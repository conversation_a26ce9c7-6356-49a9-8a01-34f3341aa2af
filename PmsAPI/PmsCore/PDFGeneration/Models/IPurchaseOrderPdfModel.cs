using System;
using System.Collections.Generic;

namespace PmsCore.PDFGeneration.Models
{
    public interface IPdfDocumentData
    {
        string DocumentType { get; }
    }

    public interface IPurchaseOrderPdfData : IPdfDocumentData
    {
        string Ponumber { get; }
        DateTime? PocreationDate { get; }
        DateTime? DeliveryDate { get; }
        string PaymentTerm { get; }
        string Status { get; }
        decimal PototalAmount { get; }
        decimal PototalTax { get; }
        decimal Pograndtotal { get; }
        string Remarks { get; }
        string SupplierName { get; }
        string SupplierAddress { get; }
        string SupplierContactNumber { get; }
        string SupplierEmail { get; }
        string SupplierGSTIN { get; }
        string DepartmentName { get; }
        string TransportName { get; }
        string DeliveryTerm { get; }
        string Reference { get; }
        string TotalInWords { get; }
        string ContactPersonName { get; }
        string ContactPersonNumber { get; }
        string ApprovedByName { get; }
        string AddedByName { get; }
        IEnumerable<IPurchaseOrderProductData> PurchaseOrderProduct { get; }
        string AmountInWords { get; }
    }

    public interface IPurchaseOrderProductData
    {
        string ProductName { get; }
        decimal? Quantity { get; }
        string Unit { get; }
        decimal? Rate { get; }
        decimal? Amount { get; }
        string Igst { get; }
        string Currency { get; }
    }
}
