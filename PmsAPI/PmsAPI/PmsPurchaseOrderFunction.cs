using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using HttpMultipartParser;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsCore.PDFGeneration.Interfaces;
using PmsData.Models;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public class PmsPurchaseOrderFunction
    {
        private readonly IPdfService _pdfService;
        private readonly ILogger _logger;

        public PmsPurchaseOrderFunction(IPdfService pdfService, ILogger<PmsPurchaseOrderFunction> logger)
        {
            _pdfService = pdfService;
            _logger = logger;
        }

        [Function("PmsPurchaseOrderFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsPurchaseOrderFunction_dataGetItems", tags: new[] { "PurchaseOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "purchaseorder/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsPurchaseOrderFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsPurchaseOrderFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            //logger.LogInformation("KeyURL :" + PmsCommon.KeyVault.KeyVaultUrl);
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getallpurchaseorders"))
                {
                    var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllPurchaseOrders());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallpurchaseordersforlist"))
                {
                    var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllPurchaseOrdersForList());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getalldemands"))
                {
                    var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllDemands());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsPurchaseOrderFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsPurchaseOrderFunction_dataUpdateItems", tags: new[] { "PurchaseOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "purchaseorder/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsPurchaseOrderFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsPurchaseOrderFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallpurchaseorderswithfilters"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    try
                    {
                        var filters = JsonConvert.DeserializeObject<PurchaseReportRequestVm>(reqbody);
                        var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetAllPurchaseOrdersWithFilters(filters));
                        return response;
                    }
                    catch (Exception ex)
                    {
                        logger.LogError("Exception Message:" + ex.Message);
                        logger.LogError("Exception StackTrace:" + ex.StackTrace);
                        logger.LogError("Exception InnerException:" + ex.InnerException);
                        var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                        await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("sendpoemail"))
                {
                    var parsedFormBody = MultipartFormDataParser.ParseAsync(req.Body);
                    var file = parsedFormBody.Result.Files[0];
                    var fg = file.Data;
                    var filename = file.Name;
                    var poid = parsedFormBody.Result.Parameters.FirstOrDefault(x => x.Name == "POid").Data;
                    var emailJson = parsedFormBody.Result.Parameters.FirstOrDefault(x => x.Name == "EmailList").Data;
                    string[] EmailList = JsonConvert.DeserializeObject<string[]>(emailJson);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.SendPOInEmail(fg, Convert.ToInt64(poid), EmailList);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("PurchaseOrder already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdatePurchaseOrder");
                        return response;
                    }

                }

                if (entity.ToLowerInvariant().Equals("addpurchaseorder"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var PurchaseOrder = JsonConvert.DeserializeObject<PurchaseOrderVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.AddPurchaseOrder(PurchaseOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("PurchaseOrder already added");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        logger.LogError("Exception Message:" + res.ResponseBody);
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddUpdatePurchaseOrder");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("adddemand"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var paramobj = JsonConvert.DeserializeObject<DemandTableVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.AddDemand(paramobj);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error Occured");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in AddDemand");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("changedemandstatus"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var paramobj = JsonConvert.DeserializeObject<DemandTableVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.ChangeDemandStatus(paramobj);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in ChangeDemandStatus");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in ChangeDemandStatus");
                        return response;
                    }
                }

                if (entity.ToLowerInvariant().Equals("updatepurchaseorder"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var PurchaseOrder = JsonConvert.DeserializeObject<PurchaseOrderVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.UpdatePurchaseOrder(PurchaseOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in UpdatePurchaseOrder");
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in UpdatePurchaseOrder");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("purchaseordereventactions"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var action = JsonConvert.DeserializeObject<POActionVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.PurchaseOrderEventActions(action);

                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    if (res.StatusCode == HttpStatusCode.BadRequest)
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Error occurred in Purchase Order Event Action");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getpoproductpricehistory"))
                {
                    var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                    var request = JsonConvert.DeserializeObject<POProductPriceHistoryRequestVm>(reqbody);
                    var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                    var res = pf.GetPOProductPriceHistory(request);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res);
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

        [Function("PmsPurchaseOrderFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsPurchaseOrderFunction_dataGetItemById", tags: new[] { "PurchaseOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "purchaseorder/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsPurchaseOrderFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsPurchaseOrderFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("cancelpurchaseorder"))
                {
                    long poid;
                    if (long.TryParse(id, out poid))
                    {
                        var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.CancelPurchaseOrder(poid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("ApprovePurchaseOrder").ToLower()))
                {
                    long poid;
                    if (long.TryParse(id, out poid))
                    {
                        var pf = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var res = pf.ApprovePurchaseOrder(poid);

                        if (res.StatusCode == HttpStatusCode.OK)
                        {
                            var response = req.CreateResponse(HttpStatusCode.OK);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                        else
                        {
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync(res.ResponseBody);
                            return response;
                        }
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetPurchaseOrderById").ToLower()))
                {
                    long poid;
                    if (long.TryParse(id, out poid))
                    {
                        var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetPurchaseOrderById(poid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetPurchaseOrderTimelineById").ToLower()))
                {
                    long poid;
                    if (long.TryParse(id, out poid))
                    {
                        var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetPurchaseOrderTimelineById(poid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("getpurchaseorderpdf").ToLower()))
                {
                    long poid;
                    if (long.TryParse(id, out poid))
                    {
                        var res = new PurchaseOrderFunctions(GlobalData, _pdfService);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetPurchaseOrderPdf(poid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

    }
}
