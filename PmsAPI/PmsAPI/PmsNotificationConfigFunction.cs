using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public class PmsNotificationConfigFunction
    {
        private readonly ILogger<PmsNotificationConfigFunction> _logger;

        public PmsNotificationConfigFunction(
            ILogger<PmsNotificationConfigFunction> logger)
        {
            _logger = logger;
        }
        [Function("PmsNotificationConfigFunction_dataAddItems")]
        [OpenApiOperation(operationId: "PmsNotificationConfigFunction_dataAddItems", tags: new[] { "NotificationConfig" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> AddItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "notificationconfig/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsNotificationConfigFunction_dataAddItems");
            // _logger = logger;
            logger.LogInformation("C# HTTP PmsNotificationConfigFunction_dataAddItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var _globalData = new GlobalDataEntity
            {
                loggedInUser = ""
            }; _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }
            if (entity.ToLowerInvariant().Equals("addstages"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Stages = JsonConvert.DeserializeObject<NotificationSaleOrderStagesTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddStages(Stages);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Stages added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("stagesedit"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Stages = JsonConvert.DeserializeObject<NotificationSaleOrderStagesTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.StagesEdit(Stages);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Stages Updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addnotificationgroup"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<NotificationGroupsTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddNotificationGroup(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Notification group added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("notificationgroupupdate"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<NotificationGroupsTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.NotificationGroupUpdate(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Notification group updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addemailconfiguration"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Mail = JsonConvert.DeserializeObject<EmailConfigTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddNotificationConfig(Mail);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("emailconfigurationedit"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Mail = JsonConvert.DeserializeObject<EmailConfigTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.NotificationConfigEdit(Mail);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addwhatsapptemplate"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<WhatsAppTemplateMasterVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddWhatsappTemplateMaster(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("whatsapp template added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("whatsapptemplateupdate"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<WhatsAppTemplateMasterVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.WhatsappTemplateMasterEdit(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Whatsapp template updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("notificationtemplateparameteraddupdate"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<NotificationTemplateParameterTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.NotificationTemplateParameterAddUpdate(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("notificationtemplateparameterdelete"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var parameterId = JsonConvert.DeserializeObject<long>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.NotificationTemplateParameterDelete(parameterId);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("copywhatsappparameter"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<WhatsAppTemplateMasterVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.CopyWhatsappTemplateParameter(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addwhatsappconfig"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<WhatsAppConfigTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddWhatsappConfig(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Whatsapp config added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("whatsappconfigedit"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<WhatsAppConfigTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.WhatsappconfigEdit(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addemailmappings"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<EmailGroupMappingTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.AddEmailGroup(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("EmailGroup Mapping added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("emailgroupedit"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var Notification = JsonConvert.DeserializeObject<EmailGroupMappingTableVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.EmailGroupEdit(Notification);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("EmailGroup Mapping updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("updatenotificationschedule"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var updateScheduleVm = JsonConvert.DeserializeObject<UpdateNotificationScheduleVm>(reqbody);

                    var pf = new NotificationConfigFunction(_globalData, _logger);
                    var res = pf.UpdateNotificationSchedule(updateScheduleVm);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Schedule updated successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(res.StatusCode);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                response.StatusCode = HttpStatusCode.BadRequest;
                return response;
            }

        }

        [Function("PmsNotificationConfigFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsNotificationConfigFunction_dataGetItems", tags: new[] { "NotificationConfig" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> GetItem([HttpTrigger(AuthorizationLevel.Function, "get", Route = "notificationconfig/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsNotificationConfigFunction_dataGetItems");
            // _logger = logger;
            logger.LogInformation("C# HTTP PmsNotificationConfigFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var _globalData = new GlobalDataEntity
            {
                loggedInUser = ""
            }; _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            _globalData.loggedInUserName = tokenS.Claims.First(claim => claim.Type == "name").Value;

            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getstages"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetStages());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallnotificationgroup"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllNotificationGroup());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getnotificationconfig"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetNotificationConfig());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getwhatsapptemplate"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetWhatsappTemplateMaster());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getwhatsappconfig"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetWhatsappConfig());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getemailgroupmapping"))
                {
                    var res = new NotificationConfigFunction(_globalData, _logger);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetEmailGroupMappings());
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                response.StatusCode = HttpStatusCode.InternalServerError;
                return response;
            }
        }

        [Function("PmsNotificationConfigFunction_getNotificationGroupDetails")]
        [OpenApiOperation(operationId: "PmsNotificationConfigFunction_getNotificationGroupDetails", tags: new[] { "NotificationConfig" })]
        [OpenApiParameter(name: "notificationGroupUserId", In = ParameterLocation.Path, Required = true, Type = typeof(long))]
        public async Task<HttpResponseData> GetNotificationGroupDetails([HttpTrigger(AuthorizationLevel.Function, "get", Route = "notificationconfig/details/{notificationGroupUserId}")] HttpRequestData req, long notificationGroupUserId,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsNotificationConfigFunction_getNotificationGroupDetails");
            logger.LogInformation("C# HTTP PmsNotificationConfigFunction_getNotificationGroupDetails processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");

            var _globalData = new GlobalDataEntity();
            _globalData.loggedInUser = headerValues.FirstOrDefault()?.Replace("Bearer ", "");

            try
            {
                if (notificationGroupUserId <= 0)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Invalid notification group user ID");
                    return response;
                }

                var res = new NotificationConfigFunction(_globalData, _logger);
                var details = res.GetNotificationGroupDetails(notificationGroupUserId);

                if (details == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.NotFound);
                    await response.WriteAsJsonAsync("Notification group not found");
                    return response;
                }

                var successResponse = req.CreateResponse(HttpStatusCode.OK);
                await successResponse.WriteAsJsonAsync(details);
                return successResponse;
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error occurred while processing the request");
                return response;
            }
        }
    }
}
