using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsData.DataFn;
using PmsEntity.ViewModel;
using HttpMultipartParser;
using static Microsoft.EntityFrameworkCore.DbLoggerCategory;

namespace PmsAPI
{
    public static class PmsSaleOrderFunction
    {
        [Function("PmsSaleOrderFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsSaleOrderFunction_dataGetItems", tags: new[] { "SaleOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "saleorder/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsSaleOrderFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsSaleOrderFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            GlobalData.loggedInUserName = tokenS.Claims.First(claim => claim.Type == "name").Value;
            try
            {

                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }

                if (entity.ToLowerInvariant().Equals("getpackingnumbers"))
                {
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetPackingNumbers());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallsaleorders"))
                {
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllSaleOrderData());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetAllSaleOrderDataForLinking").ToLower()))
                {
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllSaleOrderDataForLinking());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetPostProcessCosting").ToLower()))
                {
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetPostProcessCosting());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallformulationcodes"))
                {

                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetSaleFormulationCodeList());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getallformulationcodesforfilter"))
                {

                    var res = new FormulationCodeDataFn(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetSaleFormulationCodeForFilter());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetJumboInspectionList").ToLower()))
                {

                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetJumboInspectionList());
                    return response;
                }
                if (entity.ToLowerInvariant().Equals(("GetJumboDispatchList").ToLower()))
                {

                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetJumboDispatchList());
                    return response;
                }
                // if (entity.ToLowerInvariant().Equals("updatesaleorderstatus"))
                // {
                //     var res = new SaleOrderFunctions(GlobalData);
                //     var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                //     string querysaleorderid = query.Get("saleorderid");
                //     PmsCommon.ESalesOrderStatus querystatus = (PmsCommon.ESalesOrderStatus)Convert.ToInt64(query.Get("status"));
                //     long saleorderid;
                //     if (querystatus > 0 && long.TryParse(querysaleorderid, out saleorderid))
                //     {
                //         var response = req.CreateResponse(HttpStatusCode.OK);
                //         await response.WriteAsJsonAsync(res.UpdateSaleOrderStatus(saleorderid, querystatus));
                //         return response;
                //     }
                //     else
                //     {
                //         var response = req.CreateResponse(HttpStatusCode.BadRequest);
                //         await response.WriteAsJsonAsync($"Unable to parse id {querysaleorderid}");
                //         return response;
                //     }

                // }
                if (entity.ToLowerInvariant().Equals(("UpdateManufacturingQuantity").ToLower()))
                {
                    var res = new SaleOrderFunctions(GlobalData);
                    var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                    string querysaleorderid = query.Get("saleorderid");
                    string queryquantity = query.Get("quantity");
                    long saleorderid;
                    decimal quantity;
                    if (decimal.TryParse(queryquantity, out quantity) && long.TryParse(querysaleorderid, out saleorderid))
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.UpdateManufacturingQuantity(saleorderid, quantity));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {querysaleorderid}");
                        return response;
                    }

                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsSaleOrderFunction_dataGetItemById")]
        [OpenApiOperation(operationId: "PmsSaleOrderFunction_dataGetItemById", tags: new[] { "SaleOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        [OpenApiParameter(name: "id", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItemById([HttpTrigger(AuthorizationLevel.Function, "get", Route = "saleorder/{entity}/{id}")] HttpRequestData req, string entity, string id,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsSaleOrderFunction_dataGetItemById");
            logger.LogInformation("C# HTTP PmsSaleOrderFunction_dataGetItemById processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;
            try
            {
                if (entity == null)
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Not all required parameters provided");
                    return response;
                }
                if (entity.ToLowerInvariant().Equals("getsaleorderdatabyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderDataById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getsaleordercostingviewdatabyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderCostingViewDataById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getsaleorderdatabyidforgradeprint"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderDataByIdForGradePrint(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getsaleorderdataforviewbyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderDataForViewById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("deletesaleorder"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.DeleteSaleOrder(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("disableformulationcode"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.DisableFormulationCode(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to read id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getsaleorderdispatchdetailbyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderDispatchDetailById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getallsaleorderdatabystatus"))
                {

                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllSaleOrderDataByStatus(id));
                    return response;


                }
                if (entity.ToLowerInvariant().Equals("getsaleorderpostprocessdatabyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderPostProcessDataById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("getsaleordercostingdatabyid"))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderCostingDataById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetJumboDispatchListByDispatchId").ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetJumboDispatchListByDispatchId(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals(("GetFormulationCodeById").ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetFormulationCodeById(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to parse id {id}");
                        return response;
                    }

                }
                if (entity.ToLowerInvariant().Equals("HoldSaleOrder".ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.HoldSaleOrder(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to read id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("ApproveSaleOrder".ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ApproveSaleOrder(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to read id {id}");
                        return response;
                    }
                }
                if (entity.ToLowerInvariant().Equals("getsaleorderformulationmixing".ToLower()))
                {
                    long pid;
                    if (long.TryParse(id, out pid))
                    {
                        var res = new SaleOrderFunctions(GlobalData);
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.GetSaleOrderFormulationMixing(pid));
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync($"Unable to read id {id}");
                        return response;
                    }
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Entity not found");
                    return response;
                }
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        [Function("PmsSaleOrderFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsSaleOrderFunction_dataUpdateItems", tags: new[] { "SaleOrder" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "saleorder/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsSaleOrderFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP PmsSaleOrderFunction_dataUpdateItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }

            if (entity.ToLowerInvariant().Equals("addsaleorder"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddSaleOrder(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("SaleOrder added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("editpostprocess"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.EditPostProcess(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getallsaleorderswithfilter"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var recfilter = JsonConvert.DeserializeObject<SaleOrderRequestFilter>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllSaleOrderWithFilter(recfilter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getallsaleordersforfilterbystatus"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var data = JsonConvert.DeserializeObject<List<string>>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllSaleOrderForFilterByStatus(data));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("getjumbodispatchlistwithfilter").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var filter = JsonConvert.DeserializeObject<JumboDispatchListFilterVm>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetJumboDispatchListWithFilter(filter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("updatesaleorder"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.UpdateSaleOrder(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("SaleOrder added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("updatesaleordergsm"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var request = JsonConvert.DeserializeObject<SaleOrderProductionTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.UpdateSaleOrderGSM(request);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addupdatesaleordercosting"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddUpdateSaleOrderCosting(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("SaleOrder added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("linksaleorder"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<List<LinkedSaleOrderTableVm>>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.LinkSaleOrder(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("removelinksaleorder"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrderItem = JsonConvert.DeserializeObject<LinkedSaleOrderTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.RemoveLinkSaleOrder(SaleOrderItem);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addformulationcode"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleFormulationCodeMasterVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddFormulationCode(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("FormulationCode added successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }

            if (entity.ToLowerInvariant().Equals("updateformulationcode"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleFormulationCodeMasterVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.UpdateFormulationCode(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody.ToString());
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getformulationcodecalculatepastereqquantity"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<FormulationProductCalculatePasteReqRequest>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.GetFormulationProductCalculatePasteReqQuantity(SaleOrder);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res);
                    return response;

                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getgsmfrompastequantity"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<FormulationProductCalculatePasteReqRequest>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.GetGSMFromPasteQuantity(SaleOrder);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    return response;

                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("startsaleorderproduction"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<List<SaleOrderTableVm>>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.StartSaleOrderProduction(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Sale order production started successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("removesaleorderjumbo"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var items = JsonConvert.DeserializeObject<SaleOrderJumboRemoveVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    var res = pf.RemoveSaleOrderJumbo(items);
                    if (res.Result.Succeeded == true)
                    {
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.Result.Message);
                        return response;
                    }

                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("completesaleorderproduction"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var item = JsonConvert.DeserializeObject<SaleOrderCompleteProductionVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.CompleteSaleOrderProduction(item);
                    if (res.Result.Succeeded == true)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Sale order production started successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addpostprocessprint"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderPostProcessPrintTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddPostProcessPrint(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Request submitted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addpostprocessembossing"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderPostProcessEmbossingTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddPostProcessEmbossing(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Request submitted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addpostprocessvacuum"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderPostProcessVacuumTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddPostProcessVacuum(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Request submitted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addpostprocesslacquer"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderPostProcessLacquerTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddPostProcessLacquer(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Request submitted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }

            if (entity.ToLowerInvariant().Equals("addpostprocesstumbling"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<SaleOrderPostProcessTumblingTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddPostProcessTumbling(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Request submitted successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            // if (entity.ToLowerInvariant().Equals("addsaleorderdispatchready"))
            // {
            //     var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
            //     try
            //     {
            //         var SaleOrder = JsonConvert.DeserializeObject<SaleOrderDispatchReadyTableVm>(reqbody);

            //         var pf = new SaleOrderFunctions(GlobalData);
            //         var res = pf.AddSaleOrderDispatchReady(SaleOrder);
            //         if (res.StatusCode == HttpStatusCode.OK)
            //         {
            //             var response = req.CreateResponse(HttpStatusCode.OK);
            //             await response.WriteAsJsonAsync("FormulationCode added successfully");
            //             return response;
            //         }
            //         else
            //         {
            //             var response = req.CreateResponse(HttpStatusCode.BadRequest);
            //             await response.WriteAsJsonAsync(res.ResponseBody);
            //             return response;
            //         }
            //     }
            //     catch (Exception ex)
            //     {
            //         logger.LogError("Exception Message:" + ex.Message);
            //         logger.LogError("Exception StackTrace:" + ex.StackTrace);
            //         logger.LogError("Exception InnerException:" + ex.InnerException);
            //         var response = req.CreateResponse(HttpStatusCode.InternalServerError);
            //         await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
            //         return response;
            //     }
            // }
            if (entity.ToLowerInvariant().Equals("addsaleorderdispatch"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<List<SaleOrderDispatchTableVm>>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddSaleOrderDispatch(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Sale order items dispatched successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("AddJumboDispatch").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var jm = JsonConvert.DeserializeObject<JumboDispatchTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddJumboDispatch(jm);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }

            if (entity.ToLowerInvariant().Equals("jumbodispatchedit"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var JumboDispatch = JsonConvert.DeserializeObject<JumboDispatchTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.JumboDispatchEdit(JumboDispatch);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator. " + ex);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("jumbodispatchremoveroll"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var JumboDispatch = JsonConvert.DeserializeObject<JumboDispatchTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.JumboDispatchRemoveRoll(JumboDispatch);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        response.StatusCode = HttpStatusCode.BadRequest;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator. " + ex);
                    return response;
                }
            }
            // if (entity.ToLowerInvariant().Equals("addjumboinspection"))
            // {
            //     var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
            //     try
            //     {
            //         var SaleOrder = JsonConvert.DeserializeObject<WorkPlanJumboMasterVm>(reqbody);

            //         var pf = new SaleOrderFunctions(GlobalData);
            //         var res = pf.AddJumboInspection(SaleOrder);
            //         if (res.StatusCode == HttpStatusCode.OK)
            //         {
            //             var response = req.CreateResponse(HttpStatusCode.OK);
            //             await response.WriteAsJsonAsync("Function ran successfully");
            //             return response;
            //         }
            //         else
            //         {
            //             var response = req.CreateResponse(HttpStatusCode.BadRequest);
            //             await response.WriteAsJsonAsync(res.ResponseBody);
            //             return response;
            //         }
            //     }
            //     catch (Exception ex)
            //     {
            //         logger.LogError("Exception Message:" + ex.Message);
            //         logger.LogError("Exception StackTrace:" + ex.StackTrace);
            //         logger.LogError("Exception InnerException:" + ex.InnerException);
            //         var response = req.CreateResponse(HttpStatusCode.InternalServerError);
            //         await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
            //         return response;
            //     }
            // }
            if (entity.ToLowerInvariant().Equals(("AddJumboInspectionSingleObj").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<JumboInspectionTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddJumboInspectionSingleObj(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(res.StatusCode);
                        await response.WriteAsJsonAsync(res);
                        response.StatusCode = res.StatusCode;
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("RemoveJumboInspection").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<JumboInspectionTableVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.RemoveJumboInspection(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("CompleteJumboInspection").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<WorkPlanJumboMasterVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.CompleteJumboInspection(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("reopenjumboinspection").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var SaleOrder = JsonConvert.DeserializeObject<WorkPlanJumboMasterVm>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.ReOpenJumboInspection(SaleOrder);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addjumboinspectiondispatchedquantity"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var recJI = JsonConvert.DeserializeObject<List<JumboInspectionTableVm>>(reqbody);

                    var pf = new SaleOrderFunctions(GlobalData);
                    var res = pf.AddJumboInspectionDispatchedQuantity(recJI);
                    if (res.StatusCode == HttpStatusCode.OK)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        await response.WriteAsJsonAsync("Function ran successfully");
                        return response;
                    }
                    else
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync(res.ResponseBody);
                        return response;
                    }
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals(("GetJumboInspectionListWithFilter").ToLower()))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var recJI = JsonConvert.DeserializeObject<JumboInspectionFilterVm>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetJumboInspectionListWithFilter(recJI));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("senddispatchpackagingemail"))
            {
                var parsedFormBody = MultipartFormDataParser.ParseAsync(req.Body);
                var file = parsedFormBody.Result.Files[0];
                var fg = file.Data;
                var filename = file.Name;
                var DispatchId = parsedFormBody.Result.Parameters.FirstOrDefault(x => x.Name == "DispatchId").Data;
                var emailJson = parsedFormBody.Result.Parameters.FirstOrDefault(x => x.Name == "EmailList").Data;
                string[] EmailList = JsonConvert.DeserializeObject<string[]>(emailJson);
                var pf = new SaleOrderDataFn(GlobalData);
                var res = pf.SendDispatchPackagingEmail(fg, Convert.ToInt64(DispatchId), EmailList);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    return response;
                }
                if (res.StatusCode == HttpStatusCode.InternalServerError)
                {
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    response.StatusCode = HttpStatusCode.InternalServerError;
                    return response;
                }

            }
            if (entity.ToLowerInvariant().Equals("getpostprocesslistwithfilters"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var recfilter = JsonConvert.DeserializeObject<SaleOrderRequestFilter>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetPostProcessListWithFilters(recfilter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getsalepricebyproduct"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var recfilter = JsonConvert.DeserializeObject<SaleOrderRequestFilter>(reqbody);
                    var res = new SaleOrderFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetSalePriceByProduct(recfilter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                return response;
            }
        }

    }
}
