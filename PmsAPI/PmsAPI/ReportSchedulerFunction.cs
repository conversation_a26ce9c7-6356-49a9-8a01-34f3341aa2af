using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using Cronos;
using PmsCore.Notifications.Interfaces;
using PmsData.Models;
using PmsEntity.ViewModel;

namespace PmsAPI.PmsAPI
{
    public class ReportSchedulerFunction
    {
        private readonly ILogger _logger;
        private readonly INotificationService _notificationService;
        private readonly pmsdbContext _dbContext;

        public ReportSchedulerFunction(
            ILoggerFactory loggerFactory,
            INotificationService notificationService,
            pmsdbContext dbContext)
        {
            _logger = loggerFactory.CreateLogger<ReportSchedulerFunction>();
            _notificationService = notificationService;
            _dbContext = dbContext;
        }

        [Function("ReportSchedulerFunction")]
        public async Task Run([TimerTrigger("0 */5 * * * *")] SchedulerVm myTimer)
        {
            try
            {
                _logger.LogInformation("Report scheduler executing at: {time}", DateTime.Now);

                // Process scheduled notifications
                // await ProcessScheduledNotifications();

                // Process yield report notifications
                await ProcessYieldReportNotifications();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Report scheduler failed");
                throw;
            }
        }

        // private async Task ProcessScheduledNotifications()
        // {
        //     try
        //     {
        //         _logger.LogInformation("Processing scheduled notifications");

        //         var schedules = await _dbContext.NotificationReportScheduleMappingTables
        //             .Where(s => s.IsActive && !s.Disabled)
        //             .ToListAsync();

        //         foreach (var schedule in schedules)
        //         {
        //             try
        //             {
        //                 var cronExpression = CronExpression.Parse(schedule.CronExpression);
        //                 var timeZone = TimeZoneInfo.FindSystemTimeZoneById(schedule.TimeZone);
        //                 var utcNow = DateTime.UtcNow;

        //                 // Convert UTC to local timezone for comparison only
        //                 var currentTimeInTimeZone = TimeZoneInfo.ConvertTime(utcNow, timeZone);

        //                 if (!schedule.NextRunTime.HasValue)
        //                 {
        //                     // Calculate next occurrence in the specified timezone, then convert back to UTC
        //                     var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
        //                         DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
        //                     schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
        //                         DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
        //                         timeZone);
        //                     continue;
        //                 }

        //                 // Compare in UTC to avoid timezone conversion issues
        //                 if (utcNow >= schedule.NextRunTime)
        //                 {
        //                     _logger.LogInformation("Executing scheduled report {ReportType} (ID: {ReportId})",
        //                         schedule.ReportType, schedule.ReportId);

        //                     // Send the scheduled report
        //                     await _notificationService.SendScheduledReport(schedule.ReportType);

        //                     // Update the schedule - store times in UTC
        //                     schedule.LastRunTime = utcNow;

        //                     // Calculate next occurrence in the specified timezone, then convert back to UTC
        //                     var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
        //                         DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
        //                     schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
        //                         DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
        //                         timeZone);

        //                     _logger.LogInformation("Next run for report {ReportType} scheduled for {NextRunTime} UTC",
        //                         schedule.ReportType, schedule.NextRunTime);
        //                 }
        //             }
        //             catch (Exception ex)
        //             {
        //                 _logger.LogError(ex, "Failed to process schedule {ScheduleId} for report type {ReportType}",
        //                     schedule.ReportId, schedule.ReportType);
        //             }
        //         }

        //         await _dbContext.SaveChangesAsync();
        //     }
        //     catch (Exception ex)
        //     {
        //         _logger.LogError(ex, "Failed to process scheduled notifications");
        //         throw;
        //     }
        // }

        private async Task ProcessYieldReportNotifications()
        {
            try
            {
                _logger.LogInformation("Processing yield report notifications");

                // Get all yield report schedules (including those with null NextRunTime for initialization)
                var yieldReportSchedules = await _dbContext.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled && s.ReportType == "Scheduled" && s.ReportName == "YieldReportSummary")
                    .ToListAsync();

                foreach (var schedule in yieldReportSchedules)
                {
                    try
                    {
                        var cronExpression = CronExpression.Parse(schedule.CronExpression);
                        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(schedule.TimeZone);
                        var utcNow = DateTime.UtcNow;

                        // Convert UTC to local timezone for comparison only
                        var currentTimeInTimeZone = TimeZoneInfo.ConvertTime(utcNow, timeZone);

                        // Initialize NextRunTime for new schedules
                        if (!schedule.NextRunTime.HasValue)
                        {
                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);
                            _logger.LogInformation("Initialized NextRunTime for new yield report schedule {ScheduleId} to {NextRunTime} UTC",
                                schedule.ReportId, schedule.NextRunTime);
                            continue; // Skip execution this time, just initialize
                        }

                        // Process schedules that are due to run - compare in UTC
                        if (utcNow >= schedule.NextRunTime)
                        {
                            _logger.LogInformation("Processing yield report schedule {ScheduleId}", schedule.ReportId);

                            // Calculate the date range for the report
                            // By default, use the last 24 hours
                            var toDate = DateTime.UtcNow;
                            var fromDate = toDate.AddDays(-900);

                            // Send the yield report
                            await _notificationService.SendYieldReportSummaryWhatsApp(fromDate, toDate, "Scheduled");

                            // Update the schedule - store times in UTC
                            schedule.LastRunTime = utcNow;

                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);

                            _logger.LogInformation("Next run for yield report scheduled for {NextRunTime} UTC",
                                schedule.NextRunTime);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process yield report schedule {ScheduleId}",
                            schedule.ReportId);
                    }
                }

                await _dbContext.SaveChangesAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process yield report notifications");
                throw;
            }
        }
    }
}