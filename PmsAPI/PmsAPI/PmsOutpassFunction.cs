using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsAPI
{
    public static class PmsOutpassFunction
    {
        [Function("PmsOutpassFunction_dataGetItems")]
        [OpenApiOperation(operationId: "PmsOutpassFunction_dataGetItems", tags: new[] { "Outpass" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetItems([HttpTrigger(AuthorizationLevel.Function, "get", Route = "outpass/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsOutpassFunction_dataGetItems");
            logger.LogInformation("C# HTTP PmsOutpassFunction_dataGetItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }
            if (entity.ToLowerInvariant().Equals("getoutpassbyid"))
            {
                var res = new OutpassFunctions(GlobalData);
                var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                string queryoutpassid = query.Get("outpassid");

                if (long.TryParse(queryoutpassid, out long outpassid))
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetOutpassById(outpassid));
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    response.StatusCode = HttpStatusCode.BadRequest;
                    await response.WriteAsJsonAsync($"Unable to parse id {queryoutpassid}");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getoutpasstimeline"))
            {
                var res = new OutpassFunctions(GlobalData);
                var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                string queryoutpassid = query.Get("outpassid");

                if (long.TryParse(queryoutpassid, out long outpassid))
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetOutpassTimeline(outpassid));
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    response.StatusCode = HttpStatusCode.BadRequest;
                    await response.WriteAsJsonAsync($"Unable to parse id {queryoutpassid}");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getalloutpasspurpose"))
            {
                var res = new OutpassFunctions(GlobalData);
                var response = req.CreateResponse(HttpStatusCode.OK);
                await response.WriteAsJsonAsync(res.GetAllOutPassPurposes());
                return response;
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                return response;
            }
        }

        [Function("PmsOutpassFunction_dataUpdateItems")]
        [OpenApiOperation(operationId: "PmsOutpassFunction_dataUpdateItems", tags: new[] { "Outpass" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> UpdateItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "outpass/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsOutpassFunction_dataUpdateItems");
            logger.LogInformation("C# HTTP trigger function processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var GlobalData = new GlobalDataEntity(); GlobalData.loggedInUser = "";
            GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }

            if (entity.ToLowerInvariant().Equals("addoutpass"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                var Outpass = JsonConvert.DeserializeObject<OutpassMasterVm>(reqbody);
                Outpass.AddedBy = GlobalData.loggedInUser;
                var pf = new OutpassFunctions(GlobalData);
                var res = pf.AddOutpasss(Outpass);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync("Outpass added successfully");
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Error occurred in Add Outpass");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("modifyoutpass"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                var Outpass = JsonConvert.DeserializeObject<OutpassMasterVm>(reqbody);
                Outpass.AddedBy = GlobalData.loggedInUser;
                var pf = new OutpassFunctions(GlobalData);
                var res = pf.ModifyOutpass(Outpass);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("addupdateoutpasspurpose"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                var Outpass = JsonConvert.DeserializeObject<OutPassPurposeMasterVm>(reqbody);
                var pf = new OutpassFunctions(GlobalData);
                var res = pf.AddUpdateOutPassPurpose(Outpass);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync("Outpass Purpose Added Successfully");
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Error occurred in Add Outpass");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("deleteoutpasspurpose"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                var Outpass = JsonConvert.DeserializeObject<OutPassPurposeMasterVm>(reqbody);
                var pf = new OutpassFunctions(GlobalData);
                var res = pf.DeleteOutPassPurpose(Outpass.PurposeId);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync("Outpass Purpose Deleted Successfully");
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync("Error occurred in Add Outpass");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("inoutpass"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                var Outpass = JsonConvert.DeserializeObject<OutpassMasterVm>(reqbody);
                var pf = new OutpassFunctions(GlobalData);
                var res = pf.InOutpass(Outpass);
                if (res.StatusCode == HttpStatusCode.OK)
                {
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    return response;
                }
                else
                {
                    var response = req.CreateResponse(HttpStatusCode.BadRequest);
                    await response.WriteAsJsonAsync(res.ResponseBody);
                    response.StatusCode = HttpStatusCode.BadRequest;
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("getalloutpasswithfilters"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var filter = JsonConvert.DeserializeObject<OutpassFilterVm>(reqbody);
                    var res = new OutpassFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.GetAllOutpasswithfilters(filter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            if (entity.ToLowerInvariant().Equals("outpassstatusactions"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var filter = JsonConvert.DeserializeObject<OutpassStatusActionVm>(reqbody);
                    var res = new OutpassFunctions(GlobalData);
                    var response = req.CreateResponse(HttpStatusCode.OK);
                    await response.WriteAsJsonAsync(res.OutpassStatusActions(filter));
                    return response;
                }
                catch (Exception ex)
                {
                    logger.LogError("Exception Message:" + ex.Message);
                    logger.LogError("Exception StackTrace:" + ex.StackTrace);
                    logger.LogError("Exception InnerException:" + ex.InnerException);
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                    return response;
                }
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                return response;
            }
        }

    }
}
