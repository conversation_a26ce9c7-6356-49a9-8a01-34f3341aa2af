using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using PmsCore.PDFGeneration.Interfaces;

namespace PmsAPI
{
    public class PmsNotificationProcessorFunction
    {
        private readonly ILogger<PmsNotificationProcessorFunction> _logger;
        private readonly INotificationService _notificationService;
        private readonly IPdfService _pdfService;

        public PmsNotificationProcessorFunction(
            ILogger<PmsNotificationProcessorFunction> logger,
            INotificationService notificationService = null,
            IPdfService pdfService = null)
        {
            _logger = logger;
            _notificationService = notificationService;
            _pdfService = pdfService;
        }
        [Function("PmsNotificationFunction_dataAddItems")]
        [OpenApiOperation(operationId: "PmsNotificationFunction_dataAddItems", tags: new[] { "NotificationProcessor" })]
        [OpenApiParameter(name: "entity", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public async Task<HttpResponseData> AddItem([HttpTrigger(AuthorizationLevel.Function, "post", Route = "notificationprocessor/{entity}")] HttpRequestData req, string entity,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsNotificationFunction_dataAddItems");
            // _logger = logger;
            logger.LogInformation("C# HTTP PmsNotificationFunction_dataAddItems processed a request.");
            IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
            var authHeader = headerValues.FirstOrDefault();
            var handler = new JwtSecurityTokenHandler();
            authHeader = authHeader.Replace("Bearer ", "");
            var jsonToken = handler.ReadToken(authHeader);
            var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
            var _globalData = new GlobalDataEntity
            {
                loggedInUser = ""
            }; _globalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

            if (entity == null)
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Not all required parameters provided");
                return response;
            }
            if (entity.ToLowerInvariant().Equals("triggerondemandnotification"))
            {
                var reqbody = await new StreamReader(req.Body).ReadToEndAsync();
                try
                {
                    var request = JsonConvert.DeserializeObject<OnDemandNotificationRequest>(reqbody);

                    if (request == null || string.IsNullOrEmpty(request.NotificationType) || string.IsNullOrEmpty(request.ReportName))
                    {
                        var response = req.CreateResponse(HttpStatusCode.BadRequest);
                        await response.WriteAsJsonAsync("Invalid request. NotificationType and ReportName are required.");
                        return response;
                    }

                    // Initialize Parameters dictionary if it's null
                    request.Parameters ??= new Dictionary<string, string>();

                    // Log the parameters for debugging
                    logger.LogInformation("Request parameters: {Parameters}",
                        string.Join(", ", request.Parameters.Select(p => $"{p.Key}={p.Value}")));

                    // Validate parameters based on report type
                    if (request.ReportName.Equals("YieldReportSummary", StringComparison.OrdinalIgnoreCase))
                    {
                        if (!request.Parameters.ContainsKey("fromDate") || !request.Parameters.ContainsKey("toDate"))
                        {
                            var response = req.CreateResponse(HttpStatusCode.BadRequest);
                            await response.WriteAsJsonAsync("YieldReportSummary requires fromDate and toDate parameters.");
                            return response;
                        }
                    }

                    // Call the business layer to process the notification
                    var pf = new NotificationProcessorFunction(_globalData, _logger, _notificationService, _pdfService);
                    var result = pf.TriggerOnDemandNotification(request);

                    if (result.StatusCode == HttpStatusCode.OK)
                    {
                        // Return success response
                        var successResponse = req.CreateResponse(HttpStatusCode.OK);
                        await successResponse.WriteAsJsonAsync(new
                        {
                            Success = true,
                            Message = result.ResponseBody
                        });
                        return successResponse;
                    }
                    else
                    {
                        // Return error response
                        var errorResponse = req.CreateResponse(result.StatusCode);
                        await errorResponse.WriteAsJsonAsync(new
                        {
                            Success = false,
                            Message = result.ResponseBody
                        });
                        return errorResponse;
                    }
                }
                catch (Exception ex)
                {
                    // Handle all errors
                    logger.LogError(ex, "Failed to trigger on-demand notification");
                    var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                    await response.WriteAsJsonAsync(new
                    {
                        Success = false,
                        Message = "An error occurred while triggering the notification. Please contact administrator."
                    });
                    return response;
                }
            }
            else
            {
                var response = req.CreateResponse(HttpStatusCode.BadRequest);
                await response.WriteAsJsonAsync("Entity not found");
                response.StatusCode = HttpStatusCode.BadRequest;
                return response;
            }

        }
    }
}
