using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Azure.WebJobs;
using Microsoft.Azure.WebJobs.Extensions.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;
using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace PmsReportingAPI
{
    /// <summary>
    /// Dashboard Configuration API Functions
    /// Following PMS Azure Function naming pattern
    /// </summary>
    public class PmsDashboardConfigFunction
    {
        /// <summary>
        /// Get user's default dashboard configuration
        /// </summary>
        [FunctionName("GetUserDashboardConfig")]
        public async Task<IActionResult> GetUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "dashboard/config/{dashboardType}")] HttpRequest req,
            string dashboardType,
            ILogger log)
        {
            try
            {
                log.LogInformation($"GetUserDashboardConfig function processed a request for dashboard type: {dashboardType}");

                // Get user information from headers (following PMS pattern)
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.GetUserDashboardConfig(userInfo.loggedInUser, dashboardType);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in GetUserDashboardConfig");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Get all user dashboard configurations
        /// </summary>
        [FunctionName("GetAllUserDashboardConfigs")]
        public async Task<IActionResult> GetAllUserDashboardConfigs(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "dashboard/configs/{dashboardType}")] HttpRequest req,
            string dashboardType,
            ILogger log)
        {
            try
            {
                log.LogInformation($"GetAllUserDashboardConfigs function processed a request for dashboard type: {dashboardType}");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.GetAllUserDashboardConfigs(userInfo.loggedInUser, dashboardType);

                return new OkObjectResult(response);
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in GetAllUserDashboardConfigs");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Save new dashboard configuration
        /// </summary>
        [FunctionName("SaveUserDashboardConfig")]
        public async Task<IActionResult> SaveUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "dashboard/config")] HttpRequest req,
            ILogger log)
        {
            try
            {
                log.LogInformation("SaveUserDashboardConfig function processed a request");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                // Read request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                var request = JsonConvert.DeserializeObject<SaveDashboardConfigRequest>(requestBody);

                if (request == null)
                {
                    return new BadRequestObjectResult(new { success = false, message = "Invalid request body" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.SaveUserDashboardConfig(request, userInfo.loggedInUser);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in SaveUserDashboardConfig");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Update existing dashboard configuration
        /// </summary>
        [FunctionName("UpdateUserDashboardConfig")]
        public async Task<IActionResult> UpdateUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "dashboard/config/{configId}")] HttpRequest req,
            long configId,
            ILogger log)
        {
            try
            {
                log.LogInformation($"UpdateUserDashboardConfig function processed a request for config ID: {configId}");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                // Read request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                var request = JsonConvert.DeserializeObject<UpdateDashboardConfigRequest>(requestBody);

                if (request == null)
                {
                    return new BadRequestObjectResult(new { success = false, message = "Invalid request body" });
                }

                // Set the config ID from route parameter
                request.ConfigId = configId;

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.UpdateUserDashboardConfig(request, userInfo.loggedInUser);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in UpdateUserDashboardConfig");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Delete dashboard configuration
        /// </summary>
        [FunctionName("DeleteUserDashboardConfig")]
        public async Task<IActionResult> DeleteUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "delete", Route = "dashboard/config/{configId}")] HttpRequest req,
            long configId,
            ILogger log)
        {
            try
            {
                log.LogInformation($"DeleteUserDashboardConfig function processed a request for config ID: {configId}");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.DeleteUserDashboardConfig(configId, userInfo.loggedInUser);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in DeleteUserDashboardConfig");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Set configuration as default
        /// </summary>
        [FunctionName("SetDefaultDashboardConfig")]
        public async Task<IActionResult> SetDefaultDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "dashboard/config/{configId}/setdefault")] HttpRequest req,
            long configId,
            ILogger log)
        {
            try
            {
                log.LogInformation($"SetDefaultDashboardConfig function processed a request for config ID: {configId}");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                // Get dashboard type from query parameters
                string dashboardType = req.Query["dashboardType"];
                if (string.IsNullOrEmpty(dashboardType))
                {
                    return new BadRequestObjectResult(new { success = false, message = "Dashboard type is required" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.SetDefaultConfig(configId, userInfo.loggedInUser, dashboardType);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in SetDefaultDashboardConfig");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Reset to system default configuration
        /// </summary>
        [FunctionName("ResetToSystemDefault")]
        public async Task<IActionResult> ResetToSystemDefault(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "dashboard/config/{dashboardType}/reset")] HttpRequest req,
            string dashboardType,
            ILogger log)
        {
            try
            {
                log.LogInformation($"ResetToSystemDefault function processed a request for dashboard type: {dashboardType}");

                // Get user information from headers
                var userInfo = GetUserInfoFromHeaders(req.Headers);
                if (userInfo == null || string.IsNullOrEmpty(userInfo.loggedInUser))
                {
                    return new BadRequestObjectResult(new { success = false, message = "User authentication required" });
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(userInfo);
                var response = dashboardConfigFunctions.ResetToSystemDefault(userInfo.loggedInUser, dashboardType);

                if (response.Success)
                {
                    return new OkObjectResult(response);
                }
                else
                {
                    return new BadRequestObjectResult(response);
                }
            }
            catch (Exception ex)
            {
                log.LogError(ex, "Error in ResetToSystemDefault");
                return new StatusCodeResult(500);
            }
        }

        /// <summary>
        /// Extract user information from request headers (following PMS pattern)
        /// </summary>
        private GlobalDataEntity GetUserInfoFromHeaders(IHeaderDictionary headers)
        {
            try
            {
                // Simple header-based user identification (no Identity framework)
                var userId = headers["X-User-Id"].FirstOrDefault() ?? 
                           headers["User-Id"].FirstOrDefault() ?? 
                           "anonymous";

                var userName = headers["X-User-Name"].FirstOrDefault() ?? 
                             headers["User-Name"].FirstOrDefault() ?? 
                             userId;

                return new GlobalDataEntity
                {
                    loggedInUser = userId,
                    loggedInUserName = userName
                };
            }
            catch (Exception)
            {
                // Return default if extraction fails
                return new GlobalDataEntity
                {
                    loggedInUser = "anonymous",
                    loggedInUserName = "Anonymous User"
                };
            }
        }
    }
}
