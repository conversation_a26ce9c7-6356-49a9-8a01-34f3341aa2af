using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Attributes;
using Microsoft.Azure.WebJobs.Extensions.OpenApi.Core.Enums;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using Newtonsoft.Json;
using PmsBusiness;
using PmsCommon;
using PmsEntity.ViewModel;

namespace PmsReportingAPI
{
    /// <summary>
    /// Dashboard Configuration API Functions
    /// Following PMS Azure Function naming pattern
    /// </summary>
    public static class PmsDashboardConfigFunction
    {
        /// <summary>
        /// Get user's default dashboard configuration
        /// </summary>
        [Function("PmsDashboardConfigFunction_GetUserDashboardConfig")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_GetUserDashboardConfig", tags: new[] { "DashboardConfig" })]
        [OpenApiParameter(name: "dashboardType", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "dashboardconfig/{dashboardType}")] HttpRequestData req,
            string dashboardType,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_GetUserDashboardConfig");
            logger.LogInformation($"GetUserDashboardConfig function processed a request for dashboard type: {dashboardType}");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
                var authHeader = headerValues.FirstOrDefault();
                var handler = new JwtSecurityTokenHandler();
                authHeader = authHeader.Replace("Bearer ", "");
                var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
                var GlobalData = new GlobalDataEntity();
                GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

                if (string.IsNullOrEmpty(dashboardType))
                {
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteAsJsonAsync("Dashboard type is required");
                    return badResponse;
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                var response = dashboardConfigFunctions.GetUserDashboardConfig(GlobalData.loggedInUser, dashboardType);

                var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                await httpResponse.WriteAsJsonAsync(response);
                return httpResponse;
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        /// <summary>
        /// Get all user dashboard configurations
        /// </summary>
        [Function("PmsDashboardConfigFunction_GetAllUserDashboardConfigs")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_GetAllUserDashboardConfigs", tags: new[] { "DashboardConfig" })]
        [OpenApiParameter(name: "dashboardType", In = ParameterLocation.Path, Required = true, Type = typeof(string))]
        public static async Task<HttpResponseData> GetAllUserDashboardConfigs(
            [HttpTrigger(AuthorizationLevel.Function, "get", Route = "dashboardconfig/all/{dashboardType}")] HttpRequestData req,
            string dashboardType,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_GetAllUserDashboardConfigs");
            logger.LogInformation($"GetAllUserDashboardConfigs function processed a request for dashboard type: {dashboardType}");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
                var authHeader = headerValues.FirstOrDefault();
                var handler = new JwtSecurityTokenHandler();
                authHeader = authHeader.Replace("Bearer ", "");
                var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
                var GlobalData = new GlobalDataEntity();
                GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

                if (string.IsNullOrEmpty(dashboardType))
                {
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteAsJsonAsync("Dashboard type is required");
                    return badResponse;
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                var response = dashboardConfigFunctions.GetAllUserDashboardConfigs(GlobalData.loggedInUser, dashboardType);

                var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                await httpResponse.WriteAsJsonAsync(response);
                return httpResponse;
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        /// <summary>
        /// Save new dashboard configuration
        /// </summary>
        [Function("PmsDashboardConfigFunction_SaveUserDashboardConfig")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_SaveUserDashboardConfig", tags: new[] { "DashboardConfig" })]
        public static async Task<HttpResponseData> SaveUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "post", Route = "dashboardconfig")] HttpRequestData req,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_SaveUserDashboardConfig");
            logger.LogInformation("SaveUserDashboardConfig function processed a request");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
                var authHeader = headerValues.FirstOrDefault();
                var handler = new JwtSecurityTokenHandler();
                authHeader = authHeader.Replace("Bearer ", "");
                var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
                var GlobalData = new GlobalDataEntity();
                GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

                // Read request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                var request = JsonConvert.DeserializeObject<SaveDashboardConfigRequest>(requestBody);

                if (request == null)
                {
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteAsJsonAsync("Invalid request body");
                    return badResponse;
                }

                var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                var response = dashboardConfigFunctions.SaveUserDashboardConfig(request, GlobalData.loggedInUser);

                var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                await httpResponse.WriteAsJsonAsync(response);
                return httpResponse;
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }

        /// <summary>
        /// Update existing dashboard configuration
        /// </summary>
        [Function("PmsDashboardConfigFunction_UpdateUserDashboardConfig")]
        [OpenApiOperation(operationId: "PmsDashboardConfigFunction_UpdateUserDashboardConfig", tags: new[] { "DashboardConfig" })]
        [OpenApiParameter(name: "configId", In = ParameterLocation.Path, Required = true, Type = typeof(long))]
        public static async Task<HttpResponseData> UpdateUserDashboardConfig(
            [HttpTrigger(AuthorizationLevel.Function, "put", Route = "dashboardconfig/{configId}")] HttpRequestData req,
            long configId,
            FunctionContext executionContext)
        {
            var logger = executionContext.GetLogger("PmsDashboardConfigFunction_UpdateUserDashboardConfig");
            logger.LogInformation($"UpdateUserDashboardConfig function processed a request for config ID: {configId}");

            try
            {
                // Extract JWT token and user information (following PMS pattern)
                IEnumerable<string> headerValues = req.Headers.GetValues("Authorization");
                var authHeader = headerValues.FirstOrDefault();
                var handler = new JwtSecurityTokenHandler();
                authHeader = authHeader.Replace("Bearer ", "");
                var tokenS = handler.ReadToken(authHeader) as JwtSecurityToken;
                var GlobalData = new GlobalDataEntity();
                GlobalData.loggedInUser = tokenS.Claims.First(claim => claim.Type == "unique_name").Value;

                // Read request body
                string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                var request = JsonConvert.DeserializeObject<UpdateDashboardConfigRequest>(requestBody);

                if (request == null)
                {
                    var badResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await badResponse.WriteAsJsonAsync("Invalid request body");
                    return badResponse;
                }

                // Set the config ID from route parameter
                request.ConfigId = configId;

                var dashboardConfigFunctions = new DashboardConfigFunctions(GlobalData);
                var response = dashboardConfigFunctions.UpdateUserDashboardConfig(request, GlobalData.loggedInUser);

                var httpResponse = req.CreateResponse(HttpStatusCode.OK);
                await httpResponse.WriteAsJsonAsync(response);
                return httpResponse;
            }
            catch (Exception ex)
            {
                logger.LogError("Exception Message:" + ex.Message);
                logger.LogError("Exception StackTrace:" + ex.StackTrace);
                logger.LogError("Exception InnerException:" + ex.InnerException);
                var response = req.CreateResponse(HttpStatusCode.InternalServerError);
                await response.WriteAsJsonAsync("An error has occured. Please contact administrator");
                return response;
            }
        }


    }
}
