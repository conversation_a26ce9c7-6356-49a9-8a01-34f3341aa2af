﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class ProcessDataFn
    {
        public List<ProcessMasterVm> GetAllProcess()
        {
            List<ProcessMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ProcessMasters
                       select new ProcessMasterVm
                       {
                           ProcessId = a.ProcessId,
                           Temperature = a.Temperature,
                           ProcessName = a.ProcessName,
                           WeightGsm = a.WeightGsm,
                           WeightPressure = a.WeightPressure,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           ProcessRawMaterial = (from op in db.ProcessRawMaterialTables
                                                 join p in db.ProductMasters on op.ProductId equals p.ProductId
                                                 where op.ProcessId == a.ProcessId
                                                 select new ProcessRawMaterialTableVm
                                                 {
                                                     ProcessRawMaterialId = op.ProcessRawMaterialId,
                                                     ProcessId = op.ProcessId,
                                                     ProductId = op.ProductId,
                                                     ProductCode = p.ProductCode,
                                                     ProductName = p.ProductName,
                                                     AvgGsm = p.AvgGsm,
                                                     Quantity = op.Quantity,
                                                     Unit = op.Unit,
                                                     Price = op.Price
                                                 }).ToList()
                       }).OrderByDescending(x => x.ProcessId).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddProcess(ProcessMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                ProcessMaster mm = new ProcessMaster();
                mm.ProcessName = mix.ProcessName;
                mm.Temperature = mix.Temperature;
                mm.ProcessName = mix.ProcessName;
                mm.WeightGsm = mix.WeightGsm;
                mm.WeightPressure = mix.WeightPressure;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                db.ProcessMasters.Add(mm);
                db.SaveChanges();
                foreach (var item in mix.ProcessRawMaterial)
                {
                    ProcessRawMaterialTable spt = new ProcessRawMaterialTable();
                    spt.ProcessId = mm.ProcessId;
                    spt.ProductId = item.ProductId;
                    spt.Quantity = item.Quantity;
                    spt.Unit = item.Unit;
                    spt.Price = item.Price;
                    db.ProcessRawMaterialTables.Add(spt);
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm UpdateProcess(ProcessMasterVm mix)
        {
            using (var db = new Models.pmsdbContext())
            {
                ProcessMaster mm = db.ProcessMasters.FirstOrDefault(x => x.ProcessId == mix.ProcessId);
                mm.ProcessName = mix.ProcessName;
                mm.Temperature = mix.Temperature;
                mm.ProcessName = mix.ProcessName;
                mm.WeightGsm = mix.WeightGsm;
                mm.WeightPressure = mix.WeightPressure;
                mm.AddedBy = mix.AddedBy;
                mm.AddedDate = System.DateTime.Now;
                db.SaveChanges();
                List<ProcessRawMaterialTable> stAllList = db.ProcessRawMaterialTables.Where(x => x.ProcessId == mix.ProcessId).ToList();
                var deleteRecords = stAllList.Except(stAllList.Where(o => mix.ProcessRawMaterial.Select(s => s.ProcessRawMaterialId).ToList().Contains(o.ProcessRawMaterialId))).ToList();
                var AddRecords = mix.ProcessRawMaterial.Where(x => x.ProcessRawMaterialId == 0).ToList();
                if (AddRecords.Count > 0)
                {
                    foreach (var item in AddRecords)
                    {
                        ProcessRawMaterialTable spt = new ProcessRawMaterialTable();
                        spt.ProcessId = mm.ProcessId;
                        spt.ProductId = item.ProductId;
                        spt.Quantity = item.Quantity;
                        spt.Unit = item.Unit;
                        spt.Price = item.Price;
                        db.ProcessRawMaterialTables.Add(spt);
                    }
                }
                if (deleteRecords.Count > 0)
                {
                    foreach (var item in deleteRecords)
                    {
                        var dr = db.ProcessRawMaterialTables.SingleOrDefault(x => x.ProcessRawMaterialId == item.ProcessRawMaterialId);
                        if (dr != null)
                            db.ProcessRawMaterialTables.Remove(dr);
                    }
                }

                var resrec = mix.ProcessRawMaterial.Where(x => x.ProcessRawMaterialId > 0).ToList();
                foreach (var itm in resrec)
                {
                    var rec = db.ProcessRawMaterialTables.Where(x => x.ProcessRawMaterialId == itm.ProcessRawMaterialId).FirstOrDefault();
                    if (rec != null)
                    {
                        rec.ProcessId = itm.ProcessId;
                        rec.ProductId = itm.ProductId;
                        rec.Quantity = itm.Quantity;
                        rec.Unit = itm.Unit;
                        rec.Price = itm.Price;
                    }
                }
                db.SaveChanges();

                if (deleteRecords.Count > 0)
                    db.ProcessRawMaterialTables.RemoveRange(deleteRecords);
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
    }
}
