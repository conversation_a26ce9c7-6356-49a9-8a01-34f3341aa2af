﻿using PmsEntity.ViewModel;
using System;
using System.Linq;
using PmsCommon;
using PmsData.Models;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace PmsData.DataFn
{
    public class StorageDataFn
    {
        public GlobalDataEntity GlobalData;
        public StorageDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public StorageTokenVm GetStorageContainerToken(string containername)
        {
            var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
            var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
            var storageContainerName = containername;
            string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
            Uri res = CommonFunctions.GetServiceSasUriForContainer(storageAccountName, storageAccountKey, storageContainerName);
            return new StorageTokenVm
            {
                StorageAccountHost = string.Format("https://{0}.blob.core.windows.net", storageAccountName),
                StorageAccountName = storageAccountName,
                StorageContainerName = storageContainerName,
                StorageAccountToken = res.Query
            };
        }
        public StorageBlobSasResponseVm GetStorageBlobSASTokenByUploadId(long fileUploadId)
        {
            try
            {
                var storageAccountName = KeyVault.GetKeyValue("StorageAccountName");
                var storageAccountKey = KeyVault.GetKeyValue("StorageAccountKey");
                using var db = new Models.pmsdbContext();
                var fileUpload = db.FileUploadTables
                    .Include(x => x.UploadedByNavigation)
                    .FirstOrDefault(x => x.FileUploadId == fileUploadId);
                if (fileUpload == null)
                {
                    throw new Exception("File upload not found");
                }
                var storageContainerName = fileUpload.ContainerName;

                var filePath = fileUpload.FilePath["qualityinspection/".Length..];
                string storageAccountHost = string.Format("https://{0}.blob.core.windows.net/{1}", storageAccountName, storageContainerName);
                Uri res = CommonFunctions.GetServiceSasUriForBlob(storageAccountName, storageAccountKey, storageContainerName, filePath);
                var UriwithSas = new Uri(storageAccountHost + "/" + filePath + "?" + res.Query);
                var fileType = CommonFunctions.FindFileType(fileUpload.FileName);
                return new StorageBlobSasResponseVm
                {
                    StorageBlobSasToken = UriwithSas.ToString(),
                    FileData = new FileUploadTableVm
                    {
                        FileUploadId = fileUpload.FileUploadId,
                        FilePath = fileUpload.FilePath,
                        ContainerName = fileUpload.ContainerName,
                        FileName = fileUpload.FileName,
                        FileType = fileType,
                        UploadedBy = new UserMasterBasicVm
                        {
                            Name = fileUpload.UploadedByNavigation.Name,
                            Email = fileUpload.UploadedByNavigation.Email
                        },
                        UploadedDate = fileUpload.UploadedDate
                    }
                };
            }
            catch (Exception ex)
            {
                throw new Exception("Error getting storage blob sas token by upload id", ex);
            }
        }

        public async Task<long> AddFileUploadData(FileUploadTableVm fileUploadTableVm)
        {
            using var db = new pmsdbContext();
            var fileUpload = new FileUploadTable
            {
                FileUploadId = fileUploadTableVm.FileUploadId,
                FilePath = fileUploadTableVm.FilePath,
                ContainerName = fileUploadTableVm.ContainerName,
                FileName = fileUploadTableVm.FileName,
                UploadedBy = db.UserMasters.FirstOrDefault(x => x.Email == GlobalData.loggedInUser).UserId,
                UploadedDate = fileUploadTableVm.UploadedDate
            };
            db.FileUploadTables.Add(fileUpload);
            await db.SaveChangesAsync();
            return fileUpload.FileUploadId;
        }
    }
}
