﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Newtonsoft.Json.Linq;
using System.Collections;

namespace PmsData.DataFn
{
    public class GateDataFn
    {
        public GlobalDataEntity GlobalData;
        public GateDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<GateInVm> GetGateInRecords()
        {
            List<GateInVm> res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.GateInTables
                       join t in db.TransportVehicleTables on a.VehicleId equals t.VehicleId
                       join tc in db.TransportCompanyMasters on t.TransportId equals tc.TransportId
                       join um in db.UserMasters on a.GatePassAddedBy equals um.Email into uma
                       from um in uma.DefaultIfEmpty()
                       select new GateInVm
                       {
                           GateInId = a.GateInId,
                           VehicleId = a.VehicleId,
                           TransportCompanyName = tc.TransportCompanyName,
                           VehicleNumber = t.VehicleNumber,
                           GateInDate = a.GateInDate,
                           GateInPerson = a.GateIn<PERSON>erson,
                           GateInPersonContact = a.GateInPersonContact,
                           GateIn = a.GateIn,
                           GatePassIssue = a.GatePassIssue,
                           GatePassIssueDate = a.GatePassIssueDate,
                           GateOut = a.GateOut,
                           GateOutDate = a.GateOutDate,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           GateOutAddedBy = a.GateOutAddedBy,
                           GatePassAddedBy = a.GatePassAddedBy,
                           GatePassAddedByName = um.Name,
                           Type = a.Type,
                           InWeight = a.InWeight > 0 ? a.InWeight.ToString() + " " + "Kgs" : "",
                           OutWeight = a.OutWeight > 0 ? a.OutWeight.ToString() + " " + "Kgs" : "",
                           Invoice = (from inv in db.InvoiceMasters
                                      join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId
                                      join gtmap in db.GateInInvoiceMappingTables on inv.InvoiceId equals gtmap.InvoiceId
                                      where gtmap.GateInId == a.GateInId
                                      select new InvoiceMasterVm
                                      {
                                          InvoiceId = inv.InvoiceId,
                                          InvoiceNumber = inv.InvoiceNumber,
                                          InvoiceDate = inv.InvoiceDate,
                                          SupplierId = inv.SupplierId,
                                          SupplierName = s.SupplierName,
                                          PONumber = String.IsNullOrEmpty(inv.Poid.ToString()) ? "" : db.PurchaseOrderTables.Where(x => x.Poid == inv.Poid).FirstOrDefault().Ponumber,
                                          Poid = inv.Poid,
                                      }).ToList(),
                       }).OrderByDescending(x => x.GateInId).ToList();
            }
            return res;
        }
        public GateInVm GetGateInRecordById(long GateInId)
        {
            GateInVm res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.GateInTables
                       join t in db.TransportVehicleTables on a.VehicleId equals t.VehicleId
                       join tc in db.TransportCompanyMasters on t.TransportId equals tc.TransportId
                       join um in db.UserMasters on a.GatePassAddedBy equals um.Email into uma
                       from um in uma.DefaultIfEmpty()
                       where a.GateInId == GateInId
                       select new GateInVm
                       {
                           GateInId = a.GateInId,
                           VehicleId = a.VehicleId,
                           TransportCompanyName = tc.TransportCompanyName,
                           VehicleNumber = t.VehicleNumber,
                           VehicleType = t.VehicleType,
                           GateInDate = a.GateInDate,
                           GateInPerson = a.GateInPerson,
                           GateInPersonContact = a.GateInPersonContact,
                           GateIn = a.GateIn,
                           GatePassIssue = a.GatePassIssue,
                           GatePassIssueDate = a.GatePassIssueDate,
                           GateOut = a.GateOut,
                           GateOutDate = a.GateOutDate,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           GateOutAddedBy = a.GateOutAddedBy,
                           GatePassAddedBy = a.GatePassAddedBy,
                           GatePassAddedByName = um.Name,
                           Type = a.Type,
                           InWeight = a.InWeight > 0 ? a.InWeight.ToString() + " " + "Kgs" : "",
                           OutWeight = a.OutWeight > 0 ? a.OutWeight.ToString() + " " + "Kgs" : "",
                           IsOutpassRequired = db.GateInInvoiceMappingTables.Any(x => x.GateInId == GateInId) ? "YES" : "NO"
                       }).FirstOrDefault();
            }
            return res;
        }
        public List<GateInVm> GetGateInRecordsWithFilter(GateInFilter filter)
        {
            List<GateInVm> res = null;
            using (var db = new pmsdbContext())
            {
                var query = from a in db.GateInTables
                            join t in db.TransportVehicleTables on a.VehicleId equals t.VehicleId
                            join tc in db.TransportCompanyMasters on t.TransportId equals tc.TransportId
                            join um in db.UserMasters on a.GatePassAddedBy equals um.Email into uma
                            from um in uma.DefaultIfEmpty()
                            where ((((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gatein") && (filter.FromDate == null || a.AddedDate >= filter.FromDate))
                            && ((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gatein") && (filter.ToDate == null || a.AddedDate <= filter.ToDate)))
                            || (((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gateout") && (filter.FromDate == null || a.GateOutDate >= filter.FromDate))
                            && ((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gateout") && (filter.ToDate == null || a.GateOutDate <= filter.ToDate)))
                            || (((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gatepass") && (filter.FromDate == null || a.GatePassIssueDate >= filter.FromDate))
                            && ((string.IsNullOrEmpty(filter.DateType) || filter.DateType.ToLowerInvariant() == "gatepass") && (filter.ToDate == null || a.GatePassIssueDate <= filter.ToDate))))
                            && (filter.VehicleId == 0 || a.VehicleId == filter.VehicleId)
                            && (filter.TransportId == 0 || t.TransportId == filter.TransportId)
                            && (filter.IsGatePassIssued == null || (filter.IsGatePassIssued == a.GatePassIssue || a.GatePassIssue == null))
                            && (filter.IsGateOutCompleted == null || filter.IsGateOutCompleted == a.GateOut)
                            && (string.IsNullOrEmpty(filter.Type) || a.Type == filter.Type)
                            select new GateInVm
                            {
                                GateInId = a.GateInId,
                                VehicleId = a.VehicleId,
                                TransportCompanyName = tc.TransportCompanyName,
                                VehicleNumber = t.VehicleNumber,
                                GateInDate = a.GateInDate,
                                GateInPerson = a.GateInPerson,
                                GateInPersonContact = a.GateInPersonContact,
                                GateIn = a.GateIn,
                                GatePassIssue = a.GatePassIssue,
                                GatePassIssueDate = a.GatePassIssueDate,
                                GateOut = a.GateOut,
                                GateOutDate = a.GateOutDate,
                                AddedBy = a.AddedBy,
                                AddedDate = a.AddedDate,
                                GateOutAddedBy = a.GateOutAddedBy,
                                GatePassAddedBy = a.GatePassAddedBy,
                                GatePassAddedByName = um.Name,
                                Type = a.Type,
                                InWeight = a.InWeight > 0 ? a.InWeight.ToString() + " " + "Kgs" : "",
                                OutWeight = a.OutWeight > 0 ? a.OutWeight.ToString() + " " + "Kgs" : "",
                            };

                // Apply invoice-related filters
                if (filter.SupplierId != 0 || !string.IsNullOrEmpty(filter.PONumber) || !string.IsNullOrEmpty(filter.InvoiceNumber))
                {
                    query = query.Where(g => db.GateInInvoiceMappingTables
                        .Where(gim => gim.GateInId == g.GateInId)
                        .Join(db.InvoiceMasters, gim => gim.InvoiceId, inv => inv.InvoiceId, (gim, inv) => new { gim, inv })
                        .Join(db.PurchaseOrderTables, x => x.inv.Poid, po => po.Poid, (x, po) => new { x.gim, x.inv, po })
                        .Any(x =>
                            (filter.SupplierId == 0 || x.inv.SupplierId == filter.SupplierId) &&
                            (string.IsNullOrEmpty(filter.PONumber) || x.po.Ponumber == filter.PONumber) &&
                            (string.IsNullOrEmpty(filter.InvoiceNumber) || x.inv.InvoiceNumber == filter.InvoiceNumber)
                        ));
                }

                res = query.OrderByDescending(x => x.GateInId).ToList();

                // Fetch invoice details separately to avoid duplication
                foreach (var gateIn in res)
                {
                    gateIn.Invoice = (from inv in db.InvoiceMasters
                                      join s in db.SupplierMasters on inv.SupplierId equals s.SupplierId
                                      join gtmap in db.GateInInvoiceMappingTables on inv.InvoiceId equals gtmap.InvoiceId
                                      where gtmap.GateInId == gateIn.GateInId
                                      select new InvoiceMasterVm
                                      {
                                          InvoiceId = inv.InvoiceId,
                                          InvoiceNumber = inv.InvoiceNumber,
                                          InvoiceDate = inv.InvoiceDate,
                                          SupplierId = inv.SupplierId,
                                          SupplierName = s.SupplierName,
                                          PONumber = String.IsNullOrEmpty(inv.Poid.ToString()) ? "" : db.PurchaseOrderTables.Where(x => x.Poid == inv.Poid).FirstOrDefault().Ponumber,
                                          Poid = inv.Poid,
                                      }).ToList();
                }
            }
            return res;
        }

        public GateInVm GetVehicleStatus(long vehicleId)
        {
            GateInVm res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.GateInTables
                       join t in db.TransportVehicleTables on a.VehicleId equals t.VehicleId
                       join tc in db.TransportCompanyMasters on t.TransportId equals tc.TransportId
                       where a.VehicleId == vehicleId && a.GateOut == null
                       select new GateInVm
                       {
                           GateInId = a.GateInId,
                           VehicleId = a.VehicleId,
                           TransportCompanyName = tc.TransportCompanyName,
                           VehicleNumber = t.VehicleNumber,
                           GateInDate = a.GateInDate,
                           GateInPerson = a.GateInPerson,
                           GateInPersonContact = a.GateInPersonContact,
                           GateIn = a.GateIn,
                           GatePassIssue = a.GatePassIssue,
                           GatePassIssueDate = a.GatePassIssueDate,
                           GateOut = a.GateOut,
                           GateOutDate = a.GateOutDate,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           GateOutAddedBy = a.GateOutAddedBy,
                           GatePassAddedBy = a.GatePassAddedBy,
                       }).OrderByDescending(x => x.GateInId).FirstOrDefault();
            }
            return res;
        }

        public ApiFunctionResponseVm AddGateInRecord(GateInVm gatein)
        {
            GateInVm gm = new GateInVm();
            using (var db = new Models.pmsdbContext())
            {
                GateInTable sm = new GateInTable();
                sm.VehicleId = gatein.VehicleId;
                sm.GateInDate = System.DateTime.Now;
                sm.GateInPerson = gatein.GateInPerson;
                sm.GateInPersonContact = gatein.GateInPersonContact;
                sm.GatePassIssue = false;
                sm.GateIn = true;
                sm.GateOut = false;
                sm.AddedBy = GlobalData.loggedInUser;
                sm.AddedDate = System.DateTime.Now;
                sm.Type = "Delivery";
                db.GateInTables.Add(sm);
                db.SaveChanges();
                foreach (var inv in gatein.Invoice)
                {
                    string newgrn;
                    long supplier;
                    if (inv.Poid < 1)
                    {
                        supplier = inv.SupplierId.Value;
                        var gw = db.Grnmasters.OrderByDescending(x => x.AddedDate).FirstOrDefault();
                        if (gw == null)
                        {
                            newgrn = CommonFunctions.GRNFormat + "1";
                        }
                        else
                        {
                            newgrn = CommonFunctions.GRNFormat + (gw.Grnid + 1);
                        }
                        Grnmaster grn = new Grnmaster();
                        grn.Grn = newgrn;
                        grn.AddedBy = GlobalData.loggedInUser;   //GlobalData.loggedInUser;
                        grn.AddedDate = System.DateTime.Now;
                        db.Grnmasters.Add(grn);
                        db.SaveChanges();
                    }
                    else
                    {
                        var res = db.PurchaseOrderTables.FirstOrDefault(x => x.Poid == inv.Poid);
                        newgrn = res.Grn;
                        supplier = res.SupplierId.Value;

                    }

                    InvoiceMaster im = new InvoiceMaster();
                    im.InvoiceNumber = inv.InvoiceNumber;
                    //im.InvoiceTotalPrice = inv.InvoiceTotalPrice;
                    //im.InvoiceDate = inv.InvoiceDate;
                    //im.EwayBill = inv.EwayBill;
                    //im.EwayBillDate = inv.EwayBillDate;
                    im.SupplierId = supplier;
                    im.Poid = inv.Poid;
                    im.Grn = newgrn;
                    db.InvoiceMasters.Add(im);
                    db.SaveChanges();
                    
                    if (inv.Poid > 0 && inv.InvoiceNumber != null)
                    {
                        db.PurchaseOrderTimelineTables.Add(new PurchaseOrderTimelineTable
                        {
                            Poid = inv.Poid.Value,
                            Status = PMSPurchaseOrderStatus.GateInCompleted,
                            Remark = "Gate In Completed for Invoice No. - " + inv.InvoiceNumber,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = sm.AddedDate.Value
                        });
                    }

                    GateInInvoiceMappingTable gi = new GateInInvoiceMappingTable();
                    gi.InvoiceId = im.InvoiceId;
                    gi.GateInId = sm.GateInId;
                    db.GateInInvoiceMappingTables.Add(gi);
                    db.SaveChanges();

                }
                gm = new GateInVm
                {
                    GateInId = sm.GateInId,
                    VehicleId = sm.VehicleId,
                    GateInDate = sm.GateInDate,
                    GateInPerson = sm.GateInPerson,
                    GateInPersonContact = sm.GateInPersonContact,
                    GateIn = sm.GateIn
                };
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, gm);
        }
        public ApiFunctionResponseVm AddGateInRecordForSaleOrderDispatch(GateInVm gatein)
        {
            GateInVm gm = new GateInVm();
            using (var db = new Models.pmsdbContext())
            {
                GateInTable sm = new GateInTable();
                sm.VehicleId = gatein.VehicleId;
                sm.GateInDate = System.DateTime.Now;
                sm.GateInPerson = gatein.GateInPerson;
                sm.GateInPersonContact = gatein.GateInPersonContact;
                sm.GateIn = true;
                sm.AddedBy = GlobalData.loggedInUser;
                sm.AddedDate = System.DateTime.Now;
                db.GateInTables.Add(sm);
                db.SaveChanges();
                foreach (var saleorderid in gatein.SaleOrderId)
                {

                    GateInInvoiceMappingTable gi = new GateInInvoiceMappingTable();
                    gi.SaleOrderId = saleorderid;
                    gi.GateInId = sm.GateInId;
                    db.GateInInvoiceMappingTables.Add(gi);
                    db.SaveChanges();
                    //var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                    //if (so != null)
                    //{
                    //    so.Status = (int)ESalesOrderStatus.DispatchedGateIn;
                    //    db.SaveChanges();
                    //    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == saleorderid && x.Status == (int)ESalesOrderStatus.DispatchedGateIn))
                    //    {
                    //        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                    //        {
                    //            SaleOrderId = so.SaleOrderId,
                    //            Status = (int)ESalesOrderStatus.DispatchedGateIn,
                    //            AddedBy = GlobalData.loggedInUser,
                    //            AddedDate = System.DateTime.Now
                    //        });
                    //        db.SaveChanges();
                    //    }
                    //}

                }
                gm = new GateInVm
                {
                    GateInId = sm.GateInId,
                    VehicleId = sm.VehicleId,
                    GateInDate = sm.GateInDate,
                    GateInPerson = sm.GateInPerson,
                    GateInPersonContact = sm.GateInPersonContact,
                    GateIn = sm.GateIn
                };
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, gm);
        }
        public ApiFunctionResponseVm IssueGatePass(GateInVm gatein)
        {
            using (var db = new Models.pmsdbContext())
            {

                var res = db.GateInTables.Where(x => x.GateInId == gatein.GateInId).FirstOrDefault();
                {
                    res.GateInPerson = String.IsNullOrEmpty(gatein.GateInPerson) ? "System" : gatein.GateInPerson;
                    res.GateInPersonContact = String.IsNullOrEmpty(gatein.GateInPersonContact) ? "System" : gatein.GateInPersonContact;
                }
                res.GatePassIssue = true;
                res.InWeight = Convert.ToDecimal(gatein.InWeight);
                res.OutWeight = Convert.ToDecimal(gatein.OutWeight);
                res.GatePassIssueDate = System.DateTime.Now;
                res.GatePassAddedBy = gatein.GatePassAddedBy;
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm GateOut(GateInVm gatein)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.GateInTables.Where(x => x.GateInId == gatein.GateInId).FirstOrDefault();
                        res.GateOut = true;
                        // if (res.GateInPerson == "System")
                        // {
                        //     res.GateInPerson = String.IsNullOrEmpty(gatein.GateInPerson) ? "System" : gatein.GateInPerson;
                        //     res.GateInPersonContact = String.IsNullOrEmpty(gatein.GateInPersonContact) ? "System" : gatein.GateInPersonContact;
                        // }
                        res.GateOutDate = System.DateTime.Now;
                        res.GateOutAddedBy = GlobalData.loggedInUser;
                        db.SaveChanges();
                        var gateinSales = db.GateInInvoiceMappingTables.Where(x => x.GateInId == gatein.GateInId).ToList();
                        if (gateinSales != null)
                        {
                            var OutpassIdList = gateinSales.Select(x => x.OutpassId).Distinct().ToList();
                            var OutpassGateOut = db.OutpassMasters.Where(x => OutpassIdList.Contains(x.OutpassId)).ToList();
                            if (OutpassGateOut != null)
                            {
                                var vehicleInfo = (from a in db.GateInTables
                                                   join t in db.TransportVehicleTables on a.VehicleId equals t.VehicleId
                                                   join tc in db.TransportCompanyMasters on t.TransportId equals tc.TransportId
                                                   where a.GateInId == gatein.GateInId
                                                   select new
                                                   {
                                                       a.VehicleId,
                                                       tc.TransportCompanyName,
                                                       t.VehicleNumber,
                                                   }).FirstOrDefault();

                                foreach (var outpass in OutpassGateOut)
                                {
                                    outpass.Status = OutpassStatus.GateOutCompleted;

                                    db.OutpassStatusHistories.Add(
                                        new OutpassStatusHistory
                                        {
                                            OutpassId = outpass.OutpassId,
                                            Status = OutpassStatus.GateOutCompleted,
                                            AddedBy = GlobalData.loggedInUser,
                                            AddedDate = DateTime.Now,
                                            Remark = "Material Sent from Vehicle No.: " + vehicleInfo.VehicleNumber.ToUpper() + " of Transport: " + vehicleInfo.TransportCompanyName.ToUpper()
                                        }
                                    );
                                }
                            }

                            var dispatchorderidlist = gateinSales.Select(x => x.JumboDispatchId).Distinct().ToList();
                            db.JumboDispatchTables.Where(x => dispatchorderidlist.Contains(x.JumboDispatchId)).ToList().ForEach(x => x.IsGateOut = true);
                            var saleorderlist = gateinSales.Select(x => x.SaleOrderId).Distinct().ToList();

                            //var jumDispatchGateIn = gateinSales.Where(x => x.GateInId == gatein.GateInId).ToList();

                            foreach (var saleorderid in saleorderlist)
                            {
                                var so = db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == saleorderid);
                                if (so != null && so.Status != (int)ESalesOrderStatus.PartialDispatchCompleted && !db.WorkPlanJumboMasters.Any(x => x.SaleOrderId == saleorderid && x.IsInspectionCompleted != true))
                                {
                                    so.Status = (int)ESalesOrderStatus.PartialDispatchCompleted;
                                    db.SaveChanges();
                                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.PartialDispatchCompleted))
                                    {
                                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                        {
                                            SaleOrderId = so.SaleOrderId,
                                            Status = (int)ESalesOrderStatus.PartialDispatchCompleted,
                                            AddedBy = GlobalData.loggedInUser,
                                            AddedDate = System.DateTime.Now
                                        });
                                        db.SaveChanges();
                                    }
                                }
                                var JumboInspectionList = (from jum in db.JumboInspectionTables
                                                           join wj in db.WorkPlanJumboMasters on jum.WorkPlanJumboMasterId equals wj.WorkPlanJumboMasterId
                                                           where wj.SaleOrderId == saleorderid
                                                           select jum).ToList();
                                var JumboInspectionListHavingDispatchId = JumboInspectionList.Where(x => x.JumboDispatchId == null).ToList();
                                if (JumboInspectionListHavingDispatchId == null)
                                {
                                    bool isAllJumboInspected = db.WorkPlanJumboMasters.Any(x => x.SaleOrderId == saleorderid && x.IsInspectionCompleted != true);
                                    if (!isAllJumboInspected)
                                    {
                                        var saleOrderjumDispatchGatein = gateinSales.Where(x => x.SaleOrderId == saleorderid).Select(x => x.JumboDispatchId).OrderBy(j => j).ToList();
                                        var saleOrderjumDispatchIdFromInspection = JumboInspectionList.Select(x => x.JumboDispatchId).OrderBy(j => j).ToList();
                                        if (StructuralComparisons.StructuralEqualityComparer.Equals(saleOrderjumDispatchIdFromInspection, saleOrderjumDispatchGatein))
                                        {
                                            var gateinlist = db.GateInInvoiceMappingTables.Where(x => x.GateInId == gatein.GateInId).Select(x => x.GateInId).ToList();
                                            if (!db.GateInTables.Any(x => gateinlist.Contains(x.GateInId) && x.GateOut != true))
                                            {
                                                if (so != null)
                                                {
                                                    so.Status = (int)ESalesOrderStatus.DispatchCompleted;
                                                    db.SaveChanges();
                                                    if (!db.SaleOrderTimelineTables.Any(x => x.SaleOrderId == so.SaleOrderId && x.Status == (int)ESalesOrderStatus.DispatchCompleted))
                                                    {
                                                        db.SaleOrderTimelineTables.Add(new SaleOrderTimelineTable
                                                        {
                                                            SaleOrderId = so.SaleOrderId,
                                                            Status = (int)ESalesOrderStatus.DispatchCompleted,
                                                            AddedBy = GlobalData.loggedInUser,
                                                            AddedDate = System.DateTime.Now
                                                        });
                                                        db.SaveChanges();
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (System.Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }
    }
}
