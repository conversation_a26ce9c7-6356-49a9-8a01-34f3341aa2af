﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using PmsData.Models;
using PmsCommon;
using Microsoft.Extensions.Logging;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Text.RegularExpressions;

namespace PmsData.DataFn
{
    public class NotificationConfigDataFn
    {
        private readonly ILogger _logger;
        public GlobalDataEntity GlobalData;

        public NotificationConfigDataFn(GlobalDataEntity gd, ILogger logger)
        {
            GlobalData = gd;
            _logger = logger;
        }

        /// <summary>
        /// Validates if a mobile number is in the correct format (91 followed by 10 digits)
        /// </summary>
        /// <param name="mobileNumber">The mobile number to validate</param>
        /// <returns>True if the mobile number is valid or empty, false otherwise</returns>
        private bool IsValidMobileNumber(string mobileNumber)
        {
            if (string.IsNullOrEmpty(mobileNumber))
                return true; // Allow empty for non-WhatsApp notifications

            // Check if mobile number starts with 91 and has 12 digits total
            return Regex.IsMatch(mobileNumber, @"^91\d{10}$");
        }

        /// <summary>
        /// Validates if the notification type is valid for the selected trigger types
        /// </summary>
        /// <param name="triggerTypes">List of selected trigger types</param>
        /// <param name="notificationType">The selected notification type</param>
        /// <returns>True if the notification type is valid for all trigger types, false otherwise</returns>
        private bool IsValidNotificationTypeForTriggerTypes(List<string> triggerTypes, string notificationType)
        {
            if (triggerTypes == null || triggerTypes.Count == 0 || string.IsNullOrEmpty(notificationType))
                return false;

            // Define notification types for each trigger type
            var eventNotificationTypes = new[] { "LowStock", "SaleOrderStatus" };
            var scheduleNotificationTypes = new[] { "Reports", "ReturnableOutPass", "YieldReportSummary" };
            var onDemandNotificationTypes = new[] { "LowStock", "SaleOrderStatus", "Reports", "ReturnableOutPass", "YieldReportSummary" };

            // Check if notification type is valid for all selected trigger types
            foreach (var triggerType in triggerTypes)
            {
                bool isValid = false;

                if (triggerType == "Event")
                    isValid = eventNotificationTypes.Contains(notificationType);
                else if (triggerType == "Scheduled")
                    isValid = scheduleNotificationTypes.Contains(notificationType);
                else if (triggerType == "OnDemand")
                    isValid = onDemandNotificationTypes.Contains(notificationType);

                if (!isValid)
                    return false;
            }

            return true;
        }
        public ApiFunctionResponseVm AddStages(NotificationSaleOrderStagesTableVm Stages)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        NotificationSaleOrderStagesTable objStages = new NotificationSaleOrderStagesTable();
                        {
                            objStages.SaleOrderStages = Stages.SaleOrderStages;
                            objStages.OnlyInternal = Stages.OnlyInternal;
                            objStages.WhatsappTemplateId = Stages.WhatsappTemplateId;
                            objStages.OnlyCustomer = Stages.OnlyCustomer;
                            objStages.AddedBy = GlobalData.loggedInUser;
                            objStages.AddedDate = System.DateTime.Now;
                            objStages.Disabled = Stages.Disabled;
                            objStages.DisabledBy = Stages.DisabledBy;
                            objStages.DisabledDate = Stages.DisabledDate;

                            db.NotificationSaleOrderStagesTables.Add(objStages);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Stage Added Successfully");
        }
        public List<NotificationSaleOrderStagesTableVm> GetStages()
        {
            List<NotificationSaleOrderStagesTableVm> GetStages = null;

            using (var db = new Models.pmsdbContext())
            {
                GetStages = (from stages in db.NotificationSaleOrderStagesTables
                             where stages.Disabled != true
                             select new NotificationSaleOrderStagesTableVm
                             {
                                 StageId = stages.StageId,
                                 SaleOrderStages = stages.SaleOrderStages,
                                 OnlyInternal = stages.OnlyInternal,
                                 OnlyCustomer = stages.OnlyCustomer,
                                 WhatsappTemplateId = stages.WhatsappTemplateId,
                                 AddedBy = stages.AddedBy,
                                 AddedDate = stages.AddedDate,
                                 Disabled = stages.Disabled,
                                 DisabledBy = stages.DisabledBy,
                                 DisabledDate = stages.DisabledDate
                             }).OrderByDescending(s => s.StageId).ToList();
            }

            return GetStages;
        }
        public ApiFunctionResponseVm StagesEdit(NotificationSaleOrderStagesTableVm Stages)
        {
            if (Stages.StageId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (Stages.Disabled == true)
                            {
                                NotificationSaleOrderStagesTable StageDelete = db.NotificationSaleOrderStagesTables.FirstOrDefault(S => S.StageId == Stages.StageId);

                                StageDelete.StageId = Stages.StageId;
                                StageDelete.SaleOrderStages = Stages.SaleOrderStages;
                                StageDelete.WhatsappTemplateId = Stages.WhatsappTemplateId;
                                StageDelete.OnlyInternal = Stages.OnlyInternal;
                                StageDelete.OnlyCustomer = Stages.OnlyCustomer;
                                StageDelete.AddedBy = Stages.AddedBy;
                                StageDelete.AddedDate = Stages.AddedDate;
                                StageDelete.Disabled = true;
                                StageDelete.DisabledBy = GlobalData.loggedInUser;
                                StageDelete.DisabledDate = System.DateTime.Now;

                                db.SaveChanges();

                                var UpdateMap = db.EmailGroupMappingTables.Where(map => map.StageId == Stages.StageId).ToList();

                                foreach (var item in UpdateMap)
                                {
                                    EmailGroupMappingTable UpdateRec = db.EmailGroupMappingTables.FirstOrDefault(x => x.EmailGroupMappingId == item.EmailGroupMappingId);

                                    UpdateRec.Enabled = false;
                                    UpdateRec.Disabled = true;
                                    UpdateRec.DisabledBy = GlobalData.loggedInUser;
                                    UpdateRec.DisabledDate = System.DateTime.Now;

                                    db.SaveChanges();
                                }
                            }
                            else
                            {
                                NotificationSaleOrderStagesTable StageUpdate = db.NotificationSaleOrderStagesTables.FirstOrDefault(S => S.StageId == Stages.StageId);

                                StageUpdate.StageId = Stages.StageId;
                                StageUpdate.SaleOrderStages = Stages.SaleOrderStages;
                                StageUpdate.WhatsappTemplateId = Stages.WhatsappTemplateId;
                                StageUpdate.OnlyInternal = Stages.OnlyInternal;
                                StageUpdate.OnlyCustomer = Stages.OnlyCustomer;
                                StageUpdate.AddedBy = Stages.AddedBy;
                                StageUpdate.AddedDate = Stages.AddedDate;
                                StageUpdate.Disabled = Stages.Disabled;
                                StageUpdate.DisabledBy = Stages.DisabledBy;
                                StageUpdate.DisabledDate = Stages.DisabledDate;

                                db.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm AddNotificationGroup(NotificationGroupsTableVm Notification)
        {
            // Validate mobile number if WhatsApp notifications are enabled
            if (Notification.IsWhatsAppNotificationEnabled == true && !IsValidMobileNumber(Notification.MobileNumber))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Mobile number must start with '91' followed by 10 digits");
            }

            // Validate TriggerType and NotificationType relationship
            if (!IsValidNotificationTypeForTriggerTypes(Notification.TriggerType, Notification.NotificationType))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "The selected notification type is not valid for the selected trigger types");
            }

            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        NotificationGroupsTable objNotification = new NotificationGroupsTable();
                        {
                            objNotification.NotificationType = Notification.NotificationType;
                            objNotification.UserType = Notification.UserType;
                            objNotification.MobileNumber = Notification.MobileNumber;
                            objNotification.IsWhatsAppNotificationEnabled = Notification.IsWhatsAppNotificationEnabled;
                            objNotification.UserMasterId = Notification.UserMasterId;
                            objNotification.Name = Notification.Name;
                            objNotification.Email = Notification.Email;
                            objNotification.WhatsAppTemplateMasterId = Notification.WhatsAppTemplateMasterId;
                            objNotification.TriggerType = JsonConvert.SerializeObject(Notification.TriggerType);
                            objNotification.ReportName = Notification.ReportName;
                            objNotification.AddedBy = GlobalData.loggedInUser;

                            if (!string.IsNullOrEmpty(Notification.CronScheduleExpression))
                            {
                                NotificationReportScheduleMappingTable objReportSchedule = new NotificationReportScheduleMappingTable();
                                {
                                    objReportSchedule.ReportType = "Scheduled";
                                    objReportSchedule.ReportName = Notification.ReportName;
                                    objReportSchedule.NotificationGroupUserId = Notification.NotificationGroupUserId;
                                    objReportSchedule.TemplateMasterId = Notification.WhatsAppTemplateMasterId;
                                    objReportSchedule.CronExpression = Notification.CronScheduleExpression;
                                    objReportSchedule.IsActive = true;
                                    objReportSchedule.TimeZone = "IST";
                                    objReportSchedule.AddedBy = GlobalData.loggedInUser;
                                    objReportSchedule.AddedDate = System.DateTime.Now;
                                    objReportSchedule.Disabled = false;
                                }
                            }
                            if (Notification.EnableToEmail == true)
                            {
                                objNotification.EnableToEmail = true;
                                objNotification.EnableCcemail = false;
                                objNotification.EnableBccemail = false;
                            }
                            if (Notification.EnableCcemail == true)
                            {
                                objNotification.EnableToEmail = false;
                                objNotification.EnableCcemail = true;
                                objNotification.EnableBccemail = false;
                            }
                            if (Notification.EnableBccemail == true)
                            {
                                objNotification.EnableToEmail = false;
                                objNotification.EnableCcemail = false;
                                objNotification.EnableBccemail = true;
                            }
                            objNotification.AddedDate = DateTime.Now;
                            objNotification.Disabled = false;
                        }

                        db.NotificationGroupsTables.Add(objNotification);
                        db.SaveChanges();

                        foreach (var map in Notification.EmailGroupMappings)
                        {
                            EmailGroupMappingTable objMapping = new EmailGroupMappingTable();
                            {
                                objMapping.NotificationGroupUserId = map.NotificationGroupUserId;
                                objMapping.StageId = map.StageId;
                                objMapping.Enabled = map.Enabled;
                                objMapping.AddedBy = GlobalData.loggedInUser;
                                objMapping.AddedDate = System.DateTime.Now;
                                objMapping.Disabled = false;
                            }

                            db.EmailGroupMappingTables.Add(objMapping);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        /// <summary>
        /// Lightweight method for listing notification groups - optimized for performance
        /// Excludes heavy child collections (EmailGroupMappings, NotificationReportScheduleMappings)
        /// Handles different user types correctly: External, Internal, Customer
        /// </summary>
        public List<NotificationGroupsTableVm> GetAllNotificationGroup()
        {
            List<NotificationGroupsTableVm> Notification = null;
            // Define the default trigger type list outside the query to avoid memory leaks
            var defaultTriggerType = new List<string> { "Event" };

            using (var db = new Models.pmsdbContext())
            {
                Notification = (from n in db.NotificationGroupsTables
                                    // Left join with UserMasters for Internal users
                                join um in db.UserMasters on n.UserMasterId equals um.UserId into umd
                                from um in umd.DefaultIfEmpty()
                                    // Left join with CustomerMaster for Customer users
                                join cm in db.CustomerMasters on n.UserMasterId equals cm.CustomerId into cmd
                                from cm in cmd.DefaultIfEmpty()
                                where n.Disabled != true
                                select new NotificationGroupsTableVm
                                {
                                    NotificationGroupUserId = n.NotificationGroupUserId,
                                    NotificationType = n.NotificationType,
                                    // Use a string property to pass the TriggerType JSON
                                    // We'll deserialize it after the query
                                    TriggerType = new List<string>(),
                                    TriggerTypeJson = n.TriggerType,
                                    ReportName = n.ReportName,
                                    UserType = n.UserType,
                                    UserMasterId = n.UserMasterId,
                                    IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled,
                                    WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId,
                                    // Conditional user information based on UserType
                                    Name = n.UserType == "External" ? n.Name :
                                          n.UserType == "Internal" ? um.Name :
                                          n.UserType == "Customer" ? cm.CustomerName : n.Name,
                                    Email = n.UserType == "External" ? n.Email :
                                           n.UserType == "Internal" ? um.Email :
                                           n.UserType == "Customer" ? cm.Email : n.Email,
                                    MobileNumber = n.UserType == "External" ? n.MobileNumber :
                                                  n.UserType == "Internal" ? um.Contact :
                                                  n.UserType == "Customer" ? cm.CustomerContactNumber : n.MobileNumber,
                                    EnableToEmail = n.EnableToEmail,
                                    EnableCcemail = n.EnableCcemail,
                                    EnableBccemail = n.EnableBccemail,
                                    LastTriggeredBy = n.LastTriggeredBy,
                                    LastTriggeredDate = n.LastTriggeredDate,
                                    AddedBy = n.AddedBy,
                                    AddedDate = n.AddedDate,
                                    // Exclude heavy child collections for performance
                                    EmailGroupMappings = new List<EmailGroupMappingTableVm>(),
                                    NotificationReportScheduleMappings = new List<NotificationReportScheduleMappingTableVm>()
                                }).OrderByDescending(x => x.NotificationGroupUserId).ToList();

                // Process TriggerTypeJson after the query to avoid memory leaks
                foreach (var item in Notification)
                {
                    // Deserialize TriggerType from JSON
                    if (string.IsNullOrEmpty(item.TriggerTypeJson))
                    {
                        item.TriggerType = defaultTriggerType; // Use the default value
                    }
                    else
                    {
                        try
                        {
                            item.TriggerType = JsonConvert.DeserializeObject<List<string>>(item.TriggerTypeJson);
                        }
                        catch
                        {
                            // If deserialization fails, treat as a single value
                            item.TriggerType = new List<string> { item.TriggerTypeJson };
                        }
                    }
                }
            }
            return Notification;
        }

        /// <summary>
        /// Comprehensive method for getting detailed notification group information
        /// Includes all related data for detailed drawer view
        /// Handles different user types correctly: External, Internal, Customer
        /// </summary>
        public NotificationGroupDetailsVm GetNotificationGroupDetails(long notificationGroupUserId)
        {
            NotificationGroupDetailsVm details = null;
            var defaultTriggerType = new List<string> { "Event" };

            using (var db = new Models.pmsdbContext())
            {
                // Get the main notification group record with conditional joins based on UserType
                var notificationGroup = (from n in db.NotificationGroupsTables
                                             // Left join with UserMasters for Internal users
                                         join um in db.UserMasters on n.UserMasterId equals um.UserId into umd
                                         from um in umd.DefaultIfEmpty()
                                             // Left join with CustomerMaster for Customer users
                                         join cm in db.CustomerMasters on n.UserMasterId equals cm.CustomerId into cmd
                                         from cm in cmd.DefaultIfEmpty()
                                         where n.NotificationGroupUserId == notificationGroupUserId && n.Disabled != true
                                         select new
                                         {
                                             n.NotificationGroupUserId,
                                             n.NotificationType,
                                             n.TriggerType,
                                             n.ReportName,
                                             n.UserType,
                                             n.UserMasterId,
                                             n.IsWhatsAppNotificationEnabled,
                                             n.WhatsAppTemplateMasterId,
                                             // Conditional user information based on UserType
                                             Name = n.UserType == "External" ? n.Name :
                                                   n.UserType == "Internal" ? um.Name :
                                                   n.UserType == "Customer" ? cm.CustomerName : n.Name,
                                             Email = n.UserType == "External" ? n.Email :
                                                    n.UserType == "Internal" ? um.Email :
                                                    n.UserType == "Customer" ? cm.Email : n.Email,
                                             MobileNumber = n.UserType == "External" ? n.MobileNumber :
                                                           n.UserType == "Internal" ? um.Contact :
                                                           n.UserType == "Customer" ? cm.CustomerContactNumber : n.MobileNumber,
                                             n.EnableToEmail,
                                             n.EnableCcemail,
                                             n.EnableBccemail,
                                             n.LastTriggeredBy,
                                             n.LastTriggeredDate,
                                             n.AddedBy,
                                             n.AddedDate,
                                             n.Disabled,
                                             n.DisabledBy,
                                             n.DisabledDate
                                         }).FirstOrDefault();

                if (notificationGroup == null)
                {
                    return null; // Notification group not found
                }

                details = new NotificationGroupDetailsVm
                {
                    NotificationGroupUserId = notificationGroup.NotificationGroupUserId,
                    NotificationType = notificationGroup.NotificationType,
                    ReportName = notificationGroup.ReportName,
                    UserType = notificationGroup.UserType,
                    UserMasterId = notificationGroup.UserMasterId,
                    IsWhatsAppNotificationEnabled = notificationGroup.IsWhatsAppNotificationEnabled,
                    WhatsAppTemplateMasterId = notificationGroup.WhatsAppTemplateMasterId,
                    MobileNumber = notificationGroup.MobileNumber,
                    Name = notificationGroup.Name,
                    Email = notificationGroup.Email,
                    EnableToEmail = notificationGroup.EnableToEmail,
                    EnableCcemail = notificationGroup.EnableCcemail,
                    EnableBccemail = notificationGroup.EnableBccemail,
                    LastTriggeredBy = notificationGroup.LastTriggeredBy,
                    LastTriggeredDate = notificationGroup.LastTriggeredDate,
                    AddedBy = notificationGroup.AddedBy,
                    AddedDate = notificationGroup.AddedDate,
                    Disabled = notificationGroup.Disabled,
                    DisabledBy = notificationGroup.DisabledBy,
                    DisabledDate = notificationGroup.DisabledDate
                };

                // Process TriggerType JSON
                if (string.IsNullOrEmpty(notificationGroup.TriggerType))
                {
                    details.TriggerType = defaultTriggerType;
                }
                else
                {
                    try
                    {
                        details.TriggerType = JsonConvert.DeserializeObject<List<string>>(notificationGroup.TriggerType);
                    }
                    catch
                    {
                        details.TriggerType = new List<string> { notificationGroup.TriggerType };
                    }
                }

                // Get WhatsApp template details if enabled
                if (details.IsWhatsAppNotificationEnabled == true && details.WhatsAppTemplateMasterId.HasValue)
                {
                    details.WhatsAppTemplate = GetWhatsAppTemplateDetails(db, details.WhatsAppTemplateMasterId.Value);
                    details.WhatsAppConfig = GetWhatsAppConfigDetails(db, details.WhatsAppTemplate?.ProviderName);
                }

                // Get email configuration details
                details.EmailConfig = GetEmailConfigDetails(db);

                // Get email group mappings with stage details
                details.EmailGroupMappings = GetEmailGroupMappingDetails(db, notificationGroupUserId);

                // Get notification report schedule mappings
                details.NotificationReportScheduleMappings = GetNotificationReportScheduleMappingDetails(db, notificationGroupUserId);
            }

            return details;
        }

        /// <summary>
        /// Helper method to get WhatsApp template details
        /// </summary>
        private WhatsAppTemplateDetailsVm GetWhatsAppTemplateDetails(Models.pmsdbContext db, long templateId)
        {
            var template = (from t in db.WhatsAppTemplateMasters
                            where t.WhatsAppTemplateMasterId == templateId && t.Disabled != true
                            select new WhatsAppTemplateDetailsVm
                            {
                                WhatsAppTemplateMasterId = t.WhatsAppTemplateMasterId,
                                ProviderName = t.ProviderName,
                                ProviderTemplateId = t.ProviderTemplateId.HasValue ? t.ProviderTemplateId.Value.ToString() : null,
                                ProviderTemplateName = t.ProviderTemplateName,
                                ProviderTemplateDescription = t.ProviderTemplateDescription,
                                Parameters = (from p in db.NotificationTemplateParameterTables
                                              where p.TemplateMasterId == t.WhatsAppTemplateMasterId
                                              select new NotificationTemplateParameterTableVm
                                              {
                                                  ParameterId = p.ParameterId,
                                                  TemplateMasterId = p.TemplateMasterId,
                                                  ParameterName = p.ParameterName,
                                                  ParameterType = p.ParameterType,
                                                  IsRequired = p.IsRequired,
                                                  DefaultValue = p.DefaultValue,
                                                  ValidationRegex = p.ValidationRegex,
                                                  Sequence = p.Sequence
                                              }).OrderBy(s => s.Sequence ?? int.MaxValue).ThenBy(s => s.ParameterName).ToList()
                            }).FirstOrDefault();

            return template;
        }

        /// <summary>
        /// Helper method to get WhatsApp configuration details
        /// </summary>
        private WhatsAppConfigDetailsVm GetWhatsAppConfigDetails(Models.pmsdbContext db, string providerName)
        {
            if (string.IsNullOrEmpty(providerName))
                return null;

            var config = (from c in db.WhatsAppConfigTables
                          where c.ProviderName == providerName && c.Disabled != true
                          select new WhatsAppConfigDetailsVm
                          {
                              WhatsAppConfigId = c.WhatsAppConfigId,
                              ConfigName = c.ConfigName,
                              ProviderName = c.ProviderName,
                              RegisteredSenderNumber = c.RegisteredSenderNumber,
                              ApiEndpoint = c.ApiEndpoint,
                              MaxDailyMessages = c.MaxDailyMessages,
                              MaxMonthlyMessages = c.MaxMonthlyMessages
                          }).FirstOrDefault();

            return config;
        }

        /// <summary>
        /// Helper method to get email configuration details
        /// </summary>
        private EmailConfigDetailsVm GetEmailConfigDetails(Models.pmsdbContext db)
        {
            var config = (from c in db.EmailConfigTables
                          where c.Disabled != true
                          select new EmailConfigDetailsVm
                          {
                              EmailConfigId = c.EmailConfigId,
                              EmailConfigName = c.EmailConfigName,
                              EmailConfigSmtp = c.EmailConfigSmtp,
                              EmailConfigFromEmailId = c.EmailConfigFromEmailId,
                              EmailConfigFromEmailDisplayName = c.EmailConfigFromEmailDisplayName,
                              EmailConfigPort = string.IsNullOrEmpty(c.EmailConfigPort) ? (int?)null : int.Parse(c.EmailConfigPort)
                          }).FirstOrDefault();

            return config;
        }

        /// <summary>
        /// Helper method to get email group mapping details with stage information
        /// </summary>
        private List<EmailGroupMappingDetailsVm> GetEmailGroupMappingDetails(Models.pmsdbContext db, long notificationGroupUserId)
        {
            var mappings = (from map in db.EmailGroupMappingTables
                            join stage in db.NotificationSaleOrderStagesTables on map.StageId equals stage.StageId
                            where map.NotificationGroupUserId == notificationGroupUserId && map.Disabled != true
                            select new EmailGroupMappingDetailsVm
                            {
                                EmailGroupMappingId = map.EmailGroupMappingId,
                                NotificationGroupUserId = map.NotificationGroupUserId,
                                StageId = map.StageId,
                                StageName = stage.SaleOrderStages,
                                Enabled = map.Enabled,
                                OnlyInternal = stage.OnlyInternal,
                                OnlyCustomer = stage.OnlyCustomer,
                                AddedBy = map.AddedBy,
                                AddedDate = map.AddedDate,
                                Disabled = map.Disabled,
                                DisabledBy = map.DisabledBy,
                                DisabledDate = map.DisabledDate
                            }).ToList();

            return mappings;
        }

        /// <summary>
        /// Helper method to get notification report schedule mapping details
        /// </summary>
        private List<NotificationReportScheduleMappingDetailsVm> GetNotificationReportScheduleMappingDetails(Models.pmsdbContext db, long notificationGroupUserId)
        {
            var mappings = (from rsm in db.NotificationReportScheduleMappingTables
                            where rsm.NotificationGroupUserId == notificationGroupUserId
                            select new NotificationReportScheduleMappingDetailsVm
                            {
                                ReportId = rsm.ReportId,
                                ReportType = rsm.ReportType,
                                ReportName = rsm.ReportName,
                                NotificationGroupUserId = rsm.NotificationGroupUserId,
                                TemplateMasterId = rsm.TemplateMasterId,
                                TemplateName = "", // Will be populated if needed
                                CronExpression = rsm.CronExpression,
                                LastRunTime = rsm.LastRunTime,
                                NextRunTime = rsm.NextRunTime,
                                IsActive = rsm.IsActive,
                                TimeZone = rsm.TimeZone,
                                AddedBy = rsm.AddedBy,
                                AddedDate = rsm.AddedDate,
                                Disabled = rsm.Disabled,
                                DisabledBy = rsm.DisabledBy,
                                DisabledDate = rsm.DisabledDate
                            }).ToList();

            return mappings;
        }

        public ApiFunctionResponseVm NotificationGroupUpdate(NotificationGroupsTableVm Notification)
        {
            // Validate mobile number if WhatsApp notifications are enabled
            if (Notification.IsWhatsAppNotificationEnabled == true && !IsValidMobileNumber(Notification.MobileNumber))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Mobile number must start with '91' followed by 10 digits");
            }

            // Validate TriggerType and NotificationType relationship
            if (!IsValidNotificationTypeForTriggerTypes(Notification.TriggerType, Notification.NotificationType))
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "The selected notification type is not valid for the selected trigger types");
            }

            // Debug logging to track the NotificationGroupUserId
            System.Diagnostics.Debug.WriteLine($"NotificationGroupUpdate - NotificationGroupUserId: {Notification.NotificationGroupUserId}");

            if (Notification.NotificationGroupUserId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (Notification.Disabled == true)
                            {
                                NotificationGroupsTable UpdateNotification = db.NotificationGroupsTables.FirstOrDefault(N => N.NotificationGroupUserId == Notification.NotificationGroupUserId);
                                {
                                    UpdateNotification.Disabled = true;
                                    UpdateNotification.DisabledBy = GlobalData.loggedInUser;
                                    UpdateNotification.DisabledDate = System.DateTime.Now;

                                    db.SaveChanges();
                                }

                                var RecordList = db.EmailGroupMappingTables.Where(S => S.NotificationGroupUserId == Notification.NotificationGroupUserId).ToList();

                                foreach (var item in RecordList)
                                {
                                    EmailGroupMappingTable map = db.EmailGroupMappingTables.FirstOrDefault(M => M.EmailGroupMappingId == item.EmailGroupMappingId);
                                    if (map != null)
                                    {
                                        map.Enabled = false;
                                        map.Disabled = true;
                                        map.DisabledBy = GlobalData.loggedInUser;
                                        map.DisabledDate = System.DateTime.Now;
                                    }

                                    db.SaveChanges();
                                }
                            }
                            else
                            {
                                NotificationGroupsTable UpdateNotification = db.NotificationGroupsTables.FirstOrDefault(N => N.NotificationGroupUserId == Notification.NotificationGroupUserId);

                                // Critical check: Ensure the record exists
                                if (UpdateNotification == null)
                                {
                                    System.Diagnostics.Debug.WriteLine($"ERROR: No notification group found with ID {Notification.NotificationGroupUserId}");
                                    transaction.Rollback();
                                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, $"Notification group with ID {Notification.NotificationGroupUserId} not found");
                                }

                                {
                                    // CRITICAL: Handle trigger type changes BEFORE updating the database record
                                    // This ensures we can compare the OLD trigger types with the NEW ones
                                    HandleTriggerTypeChanges(db, Notification);

                                    // Now update the notification group record
                                    UpdateNotification.NotificationType = Notification.NotificationType;
                                    UpdateNotification.UserType = Notification.UserType;
                                    UpdateNotification.UserMasterId = Notification.UserMasterId;
                                    UpdateNotification.IsWhatsAppNotificationEnabled = Notification.IsWhatsAppNotificationEnabled;
                                    UpdateNotification.WhatsAppTemplateMasterId = Notification.WhatsAppTemplateMasterId;
                                    UpdateNotification.MobileNumber = Notification.MobileNumber;
                                    UpdateNotification.Name = Notification.Name;
                                    UpdateNotification.Email = Notification.Email;
                                    UpdateNotification.TriggerType = JsonConvert.SerializeObject(Notification.TriggerType);
                                    UpdateNotification.ReportName = Notification.ReportName;
                                    UpdateNotification.AddedBy = Notification.AddedBy;

                                    // Handle schedule mapping updates
                                    HandleScheduleMappingUpdates(db, Notification);
                                    if (Notification.EnableToEmail == true)
                                    {
                                        UpdateNotification.EnableToEmail = true;
                                        UpdateNotification.EnableCcemail = false;
                                        UpdateNotification.EnableBccemail = false;
                                    }
                                    if (Notification.EnableCcemail == true)
                                    {
                                        UpdateNotification.EnableToEmail = false;
                                        UpdateNotification.EnableCcemail = true;
                                        UpdateNotification.EnableBccemail = false;
                                    }
                                    if (Notification.EnableBccemail == true)
                                    {
                                        UpdateNotification.EnableToEmail = false;
                                        UpdateNotification.EnableCcemail = false;
                                        UpdateNotification.EnableBccemail = true;
                                    }
                                    UpdateNotification.AddedDate = DateTime.Now;
                                }

                                db.SaveChanges();

                                if (Notification.EmailGroupMappings.Count > 0)
                                {
                                    var RecordList = db.EmailGroupMappingTables.Where(S => S.NotificationGroupUserId == Notification.NotificationGroupUserId).ToList();

                                    foreach (var item in Notification.EmailGroupMappings)
                                    {
                                        foreach (var Stage in RecordList)
                                        {
                                            EmailGroupMappingTable map = db.EmailGroupMappingTables.FirstOrDefault(M => M.EmailGroupMappingId == Stage.EmailGroupMappingId);
                                            if (map != null)
                                            {
                                                map.Enabled = item.Enabled;
                                            }
                                        }

                                        db.SaveChanges();
                                    }
                                }
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            else
            {
                // If NotificationGroupUserId is 0 or null, this should be treated as an add operation
                System.Diagnostics.Debug.WriteLine($"ERROR: NotificationGroupUpdate called with invalid NotificationGroupUserId: {Notification.NotificationGroupUserId}");
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Invalid NotificationGroupUserId for update operation. Use AddNotificationGroup for new records.");
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm AddNotificationConfig(EmailConfigTableVm Mail)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        EmailConfigTable objConfig = new EmailConfigTable();
                        {
                            objConfig.EmailConfigName = Mail.EmailConfigName;
                            objConfig.EmailConfigSmtp = Mail.EmailConfigSmtp;
                            objConfig.EmailConfigFromEmailId = Mail.EmailConfigFromEmailId;
                            objConfig.EmailConfigFromEmailDisplayName = Mail.EmailConfigFromEmailDisplayName;
                            objConfig.EmailConfigAccountId = Mail.EmailConfigAccountId;
                            objConfig.EmailConfigPassword = Mail.EmailConfigPassword;
                            objConfig.EmailConfigPort = Mail.EmailConfigPort;
                            objConfig.AddedBy = GlobalData.loggedInUser;
                            objConfig.AddedDate = System.DateTime.Now;
                            objConfig.Disabled = false;

                            db.EmailConfigTables.Add(objConfig);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Email Configuration Added Successfully");
        }
        public List<EmailConfigTableVm> GetNotificationConfig()
        {
            List<EmailConfigTableVm> GetConfiguration = null;

            using (var db = new Models.pmsdbContext())
            {
                GetConfiguration = (from config in db.EmailConfigTables
                                    where config.Disabled != true
                                    select new EmailConfigTableVm
                                    {
                                        EmailConfigId = config.EmailConfigId,
                                        EmailConfigName = config.EmailConfigName,
                                        EmailConfigSmtp = config.EmailConfigSmtp,
                                        EmailConfigFromEmailId = config.EmailConfigFromEmailId,
                                        EmailConfigFromEmailDisplayName = config.EmailConfigFromEmailDisplayName,
                                        EmailConfigAccountId = config.EmailConfigAccountId,
                                        EmailConfigPassword = config.EmailConfigPassword,
                                        EmailConfigPort = config.EmailConfigPort,
                                        AddedBy = config.AddedBy,
                                        AddedDate = config.AddedDate,
                                    }).OrderBy(e => e.EmailConfigId).ToList();
            }

            return GetConfiguration;
        }
        public ApiFunctionResponseVm NotificationConfigEdit(EmailConfigTableVm Mail)
        {
            if (Mail.EmailConfigId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (Mail.Disabled == true)
                            {
                                EmailConfigTable ConfigDelete = db.EmailConfigTables.FirstOrDefault(C => C.EmailConfigId == Mail.EmailConfigId);

                                ConfigDelete.Disabled = true;
                                ConfigDelete.DisabledBy = GlobalData.loggedInUser;
                                ConfigDelete.DisabledDate = System.DateTime.Now;

                                db.SaveChanges();
                            }
                            else
                            {
                                EmailConfigTable ConfigUpdate = db.EmailConfigTables.FirstOrDefault(C => C.EmailConfigId == Mail.EmailConfigId);

                                ConfigUpdate.EmailConfigName = Mail.EmailConfigName;
                                ConfigUpdate.EmailConfigSmtp = Mail.EmailConfigSmtp;
                                ConfigUpdate.EmailConfigFromEmailId = Mail.EmailConfigFromEmailId;
                                ConfigUpdate.EmailConfigFromEmailDisplayName = Mail.EmailConfigFromEmailDisplayName;
                                ConfigUpdate.EmailConfigAccountId = Mail.EmailConfigAccountId;
                                ConfigUpdate.EmailConfigPassword = Mail.EmailConfigPassword;
                                ConfigUpdate.EmailConfigPort = Mail.EmailConfigPort;
                                ConfigUpdate.AddedBy = Mail.AddedBy;
                                ConfigUpdate.AddedDate = Mail.AddedDate;

                                db.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, ex.Message);
                            throw;
                        }
                    }
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        public ApiFunctionResponseVm AddWhatsappTemplateMaster(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        WhatsAppTemplateMaster objStages = new WhatsAppTemplateMaster();
                        {
                            objStages.WhatsAppTemplateMasterId = whatsAppTemplateMaster.WhatsAppTemplateMasterId;
                            objStages.ProviderName = whatsAppTemplateMaster.ProviderName;
                            objStages.ProviderTemplateId = whatsAppTemplateMaster.ProviderTemplateId;
                            objStages.ProviderTemplateName = whatsAppTemplateMaster.ProviderTemplateName;
                            objStages.ProviderTemplateDescription = whatsAppTemplateMaster.ProviderTemplateDescription;
                            objStages.AddedBy = GlobalData.loggedInUser;
                            objStages.AddedDate = System.DateTime.Now;
                            objStages.Disabled = false;

                            db.WhatsAppTemplateMasters.Add(objStages);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "WhatsappTemplate Added Successfully");
        }
        public ApiFunctionResponseVm CopyWhatsappTemplateParameter(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var checkProviderId = db.WhatsAppTemplateMasters.FirstOrDefault(s => s.ProviderTemplateId == whatsAppTemplateMaster.ProviderTemplateId && s.Disabled != true);
                if (checkProviderId != null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Provider Template ID needs to be unique. Check the provider template ID again.");
                }
                var checkExists = db.WhatsAppTemplateMasters.FirstOrDefault(s => s.ProviderTemplateName == whatsAppTemplateMaster.ProviderTemplateName && s.Disabled != true);
                if (checkExists != null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "WhatsApp Template Name needs to be unique. Check the provider template name again.");
                }

                WhatsAppTemplateMaster template = new WhatsAppTemplateMaster();
                {
                    template.ProviderName = whatsAppTemplateMaster.ProviderName;
                    template.ProviderTemplateId = whatsAppTemplateMaster.ProviderTemplateId;
                    template.ProviderTemplateName = whatsAppTemplateMaster.ProviderTemplateName;
                    template.ProviderTemplateDescription = whatsAppTemplateMaster.ProviderTemplateDescription;
                    template.AddedBy = GlobalData.loggedInUser;
                    template.AddedDate = System.DateTime.Now;
                    template.Disabled = false;

                    db.WhatsAppTemplateMasters.Add(template);
                    db.SaveChanges();
                }
                foreach (var parameter in whatsAppTemplateMaster.Parameters)
                {
                    NotificationTemplateParameterTable objNotificationTemplateParameterTable = new();
                    {
                        objNotificationTemplateParameterTable.TemplateMasterId = template.WhatsAppTemplateMasterId;
                        objNotificationTemplateParameterTable.ParameterName = parameter.ParameterName;
                        objNotificationTemplateParameterTable.ParameterType = parameter.ParameterType;
                        objNotificationTemplateParameterTable.IsRequired = parameter.IsRequired;
                        objNotificationTemplateParameterTable.DefaultValue = parameter.DefaultValue;
                        objNotificationTemplateParameterTable.ValidationRegex = parameter.ValidationRegex;
                        objNotificationTemplateParameterTable.Sequence = parameter.Sequence;
                    }

                    db.NotificationTemplateParameterTables.Add(objNotificationTemplateParameterTable);
                }

                db.SaveChanges();

                transaction.Commit();
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                throw;
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "WhatsApp Template Copied Successfully");
        }

        public List<WhatsAppTemplateMasterVm> GetWhatsappTemplateMaster()
        {
            List<WhatsAppTemplateMasterVm> GetWhatsappTemplateMaster = null;

            using var db = new Models.pmsdbContext();

            GetWhatsappTemplateMaster = db.WhatsAppTemplateMasters.Where(s => s.Disabled != true)
                .Include(s => s.NotificationTemplateParameterTables)
                .Select(WhatsAppTemplateMaster => new WhatsAppTemplateMasterVm
                {
                    WhatsAppTemplateMasterId = WhatsAppTemplateMaster.WhatsAppTemplateMasterId,
                    ProviderName = WhatsAppTemplateMaster.ProviderName,
                    ProviderTemplateId = WhatsAppTemplateMaster.ProviderTemplateId,
                    ProviderTemplateName = WhatsAppTemplateMaster.ProviderTemplateName,
                    ProviderTemplateDescription = WhatsAppTemplateMaster.ProviderTemplateDescription,
                    AddedBy = WhatsAppTemplateMaster.AddedBy,
                    AddedDate = WhatsAppTemplateMaster.AddedDate,
                    Parameters = WhatsAppTemplateMaster.NotificationTemplateParameterTables.Select(tp => new NotificationTemplateParameterTableVm
                    {
                        ParameterId = tp.ParameterId,
                        TemplateMasterId = tp.TemplateMasterId,
                        ParameterName = tp.ParameterName,
                        ParameterType = tp.ParameterType,
                        IsRequired = tp.IsRequired,
                        DefaultValue = tp.DefaultValue,
                        ValidationRegex = tp.ValidationRegex,
                        Sequence = tp.Sequence
                    }).OrderBy(s => s.Sequence ?? int.MaxValue).ThenBy(s => s.ParameterName).ToList()
                }).OrderByDescending(s => s.WhatsAppTemplateMasterId).ToList();

            return GetWhatsappTemplateMaster;
        }


        public ApiFunctionResponseVm WhatsappTemplateMasterEdit(WhatsAppTemplateMasterVm whatsAppTemplateMaster)
        {
            if (whatsAppTemplateMaster.WhatsAppTemplateMasterId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (whatsAppTemplateMaster.Disabled == true)
                            {
                                WhatsAppTemplateMaster whatsAppTemplateDelete = db.WhatsAppTemplateMasters.FirstOrDefault(S => S.WhatsAppTemplateMasterId == whatsAppTemplateMaster.WhatsAppTemplateMasterId);
                                whatsAppTemplateDelete.Disabled = true;
                                whatsAppTemplateDelete.DisabledBy = GlobalData.loggedInUser;
                                whatsAppTemplateDelete.DisabledDate = System.DateTime.Now;

                                db.SaveChanges();

                            }
                            else
                            {
                                WhatsAppTemplateMaster whatsAppTemplateDelete = db.WhatsAppTemplateMasters.FirstOrDefault(S => S.WhatsAppTemplateMasterId == whatsAppTemplateMaster.WhatsAppTemplateMasterId);

                                whatsAppTemplateDelete.WhatsAppTemplateMasterId = whatsAppTemplateMaster.WhatsAppTemplateMasterId;
                                whatsAppTemplateDelete.ProviderName = whatsAppTemplateMaster.ProviderName;
                                whatsAppTemplateDelete.ProviderTemplateId = whatsAppTemplateMaster.ProviderTemplateId;
                                whatsAppTemplateDelete.ProviderTemplateName = whatsAppTemplateMaster.ProviderTemplateName;
                                whatsAppTemplateDelete.ProviderTemplateDescription = whatsAppTemplateMaster.ProviderTemplateDescription;
                                whatsAppTemplateDelete.AddedBy = whatsAppTemplateMaster.AddedBy;
                                whatsAppTemplateDelete.AddedDate = whatsAppTemplateMaster.AddedDate;

                                db.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }
            else
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Bad request. Please check with administrator.");
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm NotificationTemplateParameterAddUpdate(NotificationTemplateParameterTableVm notificationTemplateParameterTableVm)
        {
            using var db = new Models.pmsdbContext();

            try
            {
                if (notificationTemplateParameterTableVm.ParameterId == 0)
                {
                    // Auto-assign sequence if not provided
                    if (!notificationTemplateParameterTableVm.Sequence.HasValue)
                    {
                        var maxSequence = db.NotificationTemplateParameterTables
                            .Where(p => p.TemplateMasterId == notificationTemplateParameterTableVm.TemplateMasterId)
                            .Max(p => (int?)p.Sequence) ?? 0;
                        notificationTemplateParameterTableVm.Sequence = maxSequence + 1;
                    }

                    NotificationTemplateParameterTable objNotificationTemplateParameterTable = new();
                    {
                        objNotificationTemplateParameterTable.ParameterId = notificationTemplateParameterTableVm.ParameterId;
                        objNotificationTemplateParameterTable.TemplateMasterId = notificationTemplateParameterTableVm.TemplateMasterId;
                        objNotificationTemplateParameterTable.ParameterName = notificationTemplateParameterTableVm.ParameterName;
                        objNotificationTemplateParameterTable.ParameterType = notificationTemplateParameterTableVm.ParameterType;
                        objNotificationTemplateParameterTable.IsRequired = notificationTemplateParameterTableVm.IsRequired;
                        objNotificationTemplateParameterTable.DefaultValue = notificationTemplateParameterTableVm.DefaultValue;
                        objNotificationTemplateParameterTable.ValidationRegex = notificationTemplateParameterTableVm.ValidationRegex;
                        objNotificationTemplateParameterTable.Sequence = notificationTemplateParameterTableVm.Sequence;

                        db.NotificationTemplateParameterTables.Add(objNotificationTemplateParameterTable);
                        db.SaveChanges();
                    }
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Notification Template Parameter Added Successfully");
                }
                else
                {
                    NotificationTemplateParameterTable objNotificationTemplateParameterTable = db.NotificationTemplateParameterTables.FirstOrDefault(s => s.ParameterId == notificationTemplateParameterTableVm.ParameterId && s.TemplateMasterId == notificationTemplateParameterTableVm.TemplateMasterId);

                    if (objNotificationTemplateParameterTable == null)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Notification Template Parameter not found");
                    }

                    objNotificationTemplateParameterTable.TemplateMasterId = notificationTemplateParameterTableVm.TemplateMasterId;
                    objNotificationTemplateParameterTable.ParameterName = notificationTemplateParameterTableVm.ParameterName;
                    objNotificationTemplateParameterTable.ParameterType = notificationTemplateParameterTableVm.ParameterType;
                    objNotificationTemplateParameterTable.IsRequired = notificationTemplateParameterTableVm.IsRequired;
                    objNotificationTemplateParameterTable.DefaultValue = notificationTemplateParameterTableVm.DefaultValue;
                    objNotificationTemplateParameterTable.ValidationRegex = notificationTemplateParameterTableVm.ValidationRegex;
                    objNotificationTemplateParameterTable.Sequence = notificationTemplateParameterTableVm.Sequence;

                    db.SaveChanges();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Notification Template Parameter Updated Successfully");
                }
            }
            catch (Exception ex)
            {
                throw;
            }

        }
        public ApiFunctionResponseVm NotificationTemplateParameterDelete(long parameterId)
        {
            using var db = new Models.pmsdbContext();

            try
            {
                NotificationTemplateParameterTable objNotificationTemplateParameterTable = db.NotificationTemplateParameterTables.FirstOrDefault(s => s.ParameterId == parameterId);
                db.NotificationTemplateParameterTables.Remove(objNotificationTemplateParameterTable);
                db.SaveChanges();

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Notification Template Parameter Deleted Successfully");
            }
            catch (Exception ex)
            {
                throw;
            }
        }


        public ApiFunctionResponseVm AddWhatsappConfig(WhatsAppConfigTableVm whatsAppConfigs)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        WhatsAppConfigTable whatsAppConfig = new WhatsAppConfigTable();
                        {
                            whatsAppConfig.WhatsAppConfigId = whatsAppConfigs.WhatsAppConfigId;
                            whatsAppConfig.ConfigName = whatsAppConfigs.ConfigName;
                            whatsAppConfig.ProviderName = whatsAppConfigs.ProviderName;
                            whatsAppConfig.RegisteredSenderNumber = whatsAppConfigs.RegisteredSenderNumber;
                            whatsAppConfig.ProviderKey = whatsAppConfigs.ProviderKey;
                            whatsAppConfig.AddedBy = GlobalData.loggedInUser;
                            whatsAppConfig.AddedDate = System.DateTime.Now;
                            whatsAppConfig.MaxDailyMessages = whatsAppConfigs.MaxDailyMessages;
                            whatsAppConfig.MaxMonthlyMessages = whatsAppConfigs.MaxMonthlyMessages;
                            whatsAppConfig.ApiEndpoint = whatsAppConfigs.ApiEndpoint;
                            whatsAppConfig.WebhookUrl = whatsAppConfigs.WebhookUrl;
                            whatsAppConfig.Disabled = false;

                            db.WhatsAppConfigTables.Add(whatsAppConfig);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "WhatsappConfig Added Successfully");
        }

        public List<WhatsAppConfigTableVm> GetWhatsappConfig()
        {
            List<WhatsAppConfigTableVm> getWhatsappConfig = null;

            using (var db = new Models.pmsdbContext())
            {
                getWhatsappConfig = (from WhatsAppConfigTable in db.WhatsAppConfigTables
                                     where WhatsAppConfigTable.Disabled != true
                                     select new WhatsAppConfigTableVm
                                     {
                                         WhatsAppConfigId = WhatsAppConfigTable.WhatsAppConfigId,
                                         ConfigName = WhatsAppConfigTable.ConfigName,
                                         ProviderName = WhatsAppConfigTable.ProviderName,
                                         RegisteredSenderNumber = WhatsAppConfigTable.RegisteredSenderNumber,
                                         ProviderKey = "nochange",
                                         AddedBy = WhatsAppConfigTable.AddedBy,
                                         AddedDate = WhatsAppConfigTable.AddedDate,
                                         MaxDailyMessages = WhatsAppConfigTable.MaxDailyMessages,
                                         MaxMonthlyMessages = WhatsAppConfigTable.MaxMonthlyMessages,
                                         ApiEndpoint = WhatsAppConfigTable.ApiEndpoint,
                                         WebhookUrl = WhatsAppConfigTable.WebhookUrl
                                     }).OrderByDescending(s => s.WhatsAppConfigId).ToList();
            }

            return getWhatsappConfig;
        }


        public ApiFunctionResponseVm WhatsappconfigEdit(WhatsAppConfigTableVm whatsAppConfigTableVm)
        {
            if (whatsAppConfigTableVm.WhatsAppConfigId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (whatsAppConfigTableVm.Disabled == true)
                            {
                                WhatsAppConfigTable whatsAppConfig = db.WhatsAppConfigTables.FirstOrDefault(S => S.WhatsAppConfigId == whatsAppConfigTableVm.WhatsAppConfigId);
                                whatsAppConfig.Disabled = true;
                                whatsAppConfig.DisabledBy = GlobalData.loggedInUser;
                                whatsAppConfig.DisabledDate = System.DateTime.Now;
                                db.SaveChanges();
                            }
                            else
                            {
                                WhatsAppConfigTable whatsAppConfig = db.WhatsAppConfigTables.FirstOrDefault(S => S.WhatsAppConfigId == whatsAppConfigTableVm.WhatsAppConfigId);

                                whatsAppConfig.WhatsAppConfigId = whatsAppConfigTableVm.WhatsAppConfigId;
                                whatsAppConfig.ConfigName = whatsAppConfigTableVm.ConfigName;
                                whatsAppConfig.ProviderKey = whatsAppConfigTableVm.ProviderKey == "nochange" ? whatsAppConfig.ProviderKey : whatsAppConfigTableVm.ProviderKey;
                                whatsAppConfig.ProviderName = whatsAppConfigTableVm.ProviderName;
                                whatsAppConfig.RegisteredSenderNumber = whatsAppConfigTableVm.RegisteredSenderNumber;
                                whatsAppConfig.AddedBy = GlobalData.loggedInUser;
                                whatsAppConfig.AddedDate = System.DateTime.Now;
                                whatsAppConfig.MaxDailyMessages = whatsAppConfigTableVm.MaxDailyMessages;
                                whatsAppConfig.MaxMonthlyMessages = whatsAppConfigTableVm.MaxMonthlyMessages;
                                whatsAppConfig.ApiEndpoint = whatsAppConfigTableVm.ApiEndpoint;
                                whatsAppConfig.WebhookUrl = whatsAppConfigTableVm.WebhookUrl;
                                db.SaveChanges();
                            }

                            transaction.Commit();
                            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occurred. Please contact administrator.");
                            throw;
                        }
                    }
                }
            }
            else
            {
                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, "Bad request. Please check with administrator.");
            }


        }
        public ApiFunctionResponseVm AddEmailGroupMapping(EmailGroupMappingTableVm emailGroupMappingTableVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        EmailGroupMappingTable objStages = new EmailGroupMappingTable();
                        {
                            objStages.EmailGroupMappingId = emailGroupMappingTableVm.EmailGroupMappingId;
                            objStages.NotificationGroupUserId = emailGroupMappingTableVm.NotificationGroupUserId;
                            objStages.StageId = emailGroupMappingTableVm.StageId;
                            objStages.AddedBy = GlobalData.loggedInUser;
                            objStages.AddedDate = System.DateTime.Now;
                            objStages.Disabled = false;

                            db.EmailGroupMappingTables.Add(objStages);
                            db.SaveChanges();
                        }

                        transaction.Commit();
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw;
                    }
                }
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "WhatsappTemplate Added Successfully");
        }


        public List<EmailGroupMappingTableVm> GetEmailGroupMappings()
        {
            List<EmailGroupMappingTableVm> emailGroups = null;

            using (var db = new Models.pmsdbContext())
            {
                emailGroups = (from EmailGroupMappingTable in db.EmailGroupMappingTables
                               join st in db.NotificationSaleOrderStagesTables on EmailGroupMappingTable.StageId equals st.StageId
                               join ng in db.NotificationGroupsTables on EmailGroupMappingTable.NotificationGroupUserId equals ng.NotificationGroupUserId
                               where EmailGroupMappingTable.Disabled != true
                               select new EmailGroupMappingTableVm
                               {
                                   EmailGroupMappingId = EmailGroupMappingTable.EmailGroupMappingId,
                                   NotificationGroupUserId = EmailGroupMappingTable.NotificationGroupUserId,
                                   NotificationGroupUserName = ng.Name,
                                   StageId = EmailGroupMappingTable.StageId,
                                   StageName = st.SaleOrderStages,
                                   AddedBy = EmailGroupMappingTable.AddedBy,
                                   AddedDate = EmailGroupMappingTable.AddedDate
                               }).OrderByDescending(s => s.EmailGroupMappingId).ToList();
            }

            return emailGroups;
        }

        public ApiFunctionResponseVm EmailGropMappingEdit(EmailGroupMappingTableVm emailGroupMappingTableVm)
        {
            if (emailGroupMappingTableVm.EmailGroupMappingId > 0)
            {
                using (var db = new Models.pmsdbContext())
                {
                    using (var transaction = db.Database.BeginTransaction())
                    {
                        try
                        {
                            if (emailGroupMappingTableVm.Disabled == true)
                            {
                                EmailGroupMappingTable emailGroupMappingTable = db.EmailGroupMappingTables.FirstOrDefault(S => S.EmailGroupMappingId == emailGroupMappingTableVm.EmailGroupMappingId);
                                emailGroupMappingTable.Disabled = true;
                                emailGroupMappingTable.DisabledBy = GlobalData.loggedInUser;
                                emailGroupMappingTable.DisabledDate = System.DateTime.Now;

                                db.SaveChanges();

                            }
                            else
                            {
                                EmailGroupMappingTable emailGroupMapping = db.EmailGroupMappingTables.FirstOrDefault(S => S.EmailGroupMappingId == emailGroupMappingTableVm.EmailGroupMappingId);

                                emailGroupMapping.EmailGroupMappingId = emailGroupMappingTableVm.EmailGroupMappingId;
                                emailGroupMapping.NotificationGroupUserId = emailGroupMappingTableVm.NotificationGroupUserId;
                                emailGroupMapping.StageId = emailGroupMappingTableVm.StageId;
                                emailGroupMapping.AddedBy = emailGroupMappingTableVm.AddedBy;
                                emailGroupMapping.AddedDate = emailGroupMappingTableVm.AddedDate;

                                db.SaveChanges();
                            }

                            transaction.Commit();
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw;
                        }
                    }
                }
            }

            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }

        // Helper method to handle schedule mapping updates
        private void HandleScheduleMappingUpdates(Models.pmsdbContext db, NotificationGroupsTableVm notification)
        {
            // Check if the notification has "Scheduled" trigger type
            bool hasScheduledTrigger = notification.TriggerType != null &&
                                     notification.TriggerType.Contains("Scheduled");

            if (hasScheduledTrigger && !string.IsNullOrEmpty(notification.CronScheduleExpression))
            {
                // Check if there's an existing schedule mapping
                var existingSchedule = db.NotificationReportScheduleMappingTables
                    .FirstOrDefault(s => s.NotificationGroupUserId == notification.NotificationGroupUserId &&
                                        s.Disabled != true);

                if (existingSchedule != null)
                {
                    // Update existing schedule
                    existingSchedule.CronExpression = notification.CronScheduleExpression;
                    existingSchedule.ReportName = notification.ReportName;
                    existingSchedule.TemplateMasterId = notification.WhatsAppTemplateMasterId;
                    existingSchedule.IsActive = true;
                    existingSchedule.AddedBy = GlobalData.loggedInUser;
                    existingSchedule.AddedDate = DateTime.Now;
                }
                else
                {
                    // Create new schedule mapping
                    var objReportSchedule = new NotificationReportScheduleMappingTable
                    {
                        ReportType = "Scheduled",
                        ReportName = notification.ReportName,
                        NotificationGroupUserId = notification.NotificationGroupUserId,
                        TemplateMasterId = notification.WhatsAppTemplateMasterId,
                        CronExpression = notification.CronScheduleExpression,
                        IsActive = true,
                        TimeZone = "IST",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        Disabled = false
                    };
                    db.NotificationReportScheduleMappingTables.Add(objReportSchedule);
                }
                db.SaveChanges();
            }
        }

        // Helper method to handle trigger type changes
        private void HandleTriggerTypeChanges(Models.pmsdbContext db, NotificationGroupsTableVm notification)
        {
            // Get the current trigger types from database (BEFORE any updates)
            var currentNotification = db.NotificationGroupsTables
                .FirstOrDefault(n => n.NotificationGroupUserId == notification.NotificationGroupUserId);

            if (currentNotification != null)
            {
                List<string> currentTriggerTypes = new List<string>();
                try
                {
                    if (!string.IsNullOrEmpty(currentNotification.TriggerType))
                    {
                        if (currentNotification.TriggerType.StartsWith("[") && currentNotification.TriggerType.EndsWith("]"))
                        {
                            currentTriggerTypes = JsonConvert.DeserializeObject<List<string>>(currentNotification.TriggerType);
                        }
                        else
                        {
                            currentTriggerTypes = new List<string> { currentNotification.TriggerType };
                        }
                    }
                }
                catch
                {
                    currentTriggerTypes = new List<string> { "Event" }; // Default
                }

                // Debug logging to verify we're comparing the correct values
                System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - OLD trigger types: [{string.Join(", ", currentTriggerTypes)}]");
                System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - NEW trigger types: [{string.Join(", ", notification.TriggerType ?? new List<string>())}]");

                // Check if "Scheduled" was removed
                bool hadScheduled = currentTriggerTypes.Contains("Scheduled");
                bool hasScheduled = notification.TriggerType != null && notification.TriggerType.Contains("Scheduled");

                System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - hadScheduled: {hadScheduled}, hasScheduled: {hasScheduled}");

                if (hadScheduled && !hasScheduled)
                {
                    // Disable all schedule mappings for this notification group
                    System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - DISABLING schedule mappings for NotificationGroupUserId: {notification.NotificationGroupUserId}");
                    var scheduleMappings = db.NotificationReportScheduleMappingTables
                        .Where(s => s.NotificationGroupUserId == notification.NotificationGroupUserId &&
                                   s.Disabled != true)
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - Found {scheduleMappings.Count} active schedule mappings to disable");

                    foreach (var mapping in scheduleMappings)
                    {
                        mapping.Disabled = true;
                        // Note: DisabledBy should be a user ID (long), but we only have username (string)
                        // For now, we'll leave it null and add a comment
                        mapping.DisabledBy = db.UserMasters.FirstOrDefault(u => u.Email == GlobalData.loggedInUser)?.UserId; // TODO: Get user ID from username
                        mapping.DisabledDate = DateTime.Now;
                        mapping.IsActive = false;
                    }
                    db.SaveChanges();
                }
                else if (!hadScheduled && hasScheduled)
                {
                    // Re-enable previously disabled schedule mappings if they exist
                    System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - RE-ENABLING schedule mappings for NotificationGroupUserId: {notification.NotificationGroupUserId}");
                    var disabledScheduleMappings = db.NotificationReportScheduleMappingTables
                        .Where(s => s.NotificationGroupUserId == notification.NotificationGroupUserId &&
                                   s.Disabled == true)
                        .ToList();

                    System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - Found {disabledScheduleMappings.Count} disabled schedule mappings to re-enable");

                    foreach (var mapping in disabledScheduleMappings)
                    {
                        mapping.Disabled = false;
                        mapping.DisabledBy = null;
                        mapping.DisabledDate = null;
                        mapping.IsActive = true;
                        mapping.AddedBy = GlobalData.loggedInUser;
                        mapping.AddedDate = DateTime.Now;
                    }
                    db.SaveChanges();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"HandleTriggerTypeChanges - No trigger type changes detected for scheduled mappings");
                }
            }
        }

        public ApiFunctionResponseVm UpdateNotificationSchedule(UpdateNotificationScheduleVm updateScheduleVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        // Find the schedule mapping to update
                        var scheduleMapping = db.NotificationReportScheduleMappingTables
                            .FirstOrDefault(s => s.ReportId == updateScheduleVm.ReportId &&
                                                s.NotificationGroupUserId == updateScheduleVm.NotificationGroupUserId);

                        if (scheduleMapping == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Schedule mapping not found");
                        }

                        // Update the schedule status
                        scheduleMapping.IsActive = updateScheduleVm.IsActive;
                        scheduleMapping.Disabled = updateScheduleVm.Disabled;

                        if (updateScheduleVm.Disabled)
                        {
                            scheduleMapping.DisabledBy = db.UserMasters.FirstOrDefault(u => u.Email == GlobalData.loggedInUser)?.UserId;
                            scheduleMapping.DisabledDate = DateTime.Now;
                        }
                        else
                        {
                            scheduleMapping.DisabledBy = null;
                            scheduleMapping.DisabledDate = null;
                            scheduleMapping.AddedBy = GlobalData.loggedInUser;
                            scheduleMapping.AddedDate = DateTime.Now;
                        }

                        db.SaveChanges();

                        // Check if we need to update the notification group's trigger types
                        var notificationGroup = db.NotificationGroupsTables
                            .FirstOrDefault(n => n.NotificationGroupUserId == updateScheduleVm.NotificationGroupUserId);

                        if (notificationGroup != null)
                        {
                            // Get all active schedules for this notification group
                            var activeSchedules = db.NotificationReportScheduleMappingTables
                                .Where(s => s.NotificationGroupUserId == updateScheduleVm.NotificationGroupUserId &&
                                           s.IsActive && !s.Disabled)
                                .ToList();

                            // Parse current trigger types
                            List<string> currentTriggerTypes = new List<string>();
                            try
                            {
                                if (!string.IsNullOrEmpty(notificationGroup.TriggerType))
                                {
                                    if (notificationGroup.TriggerType.StartsWith("[") && notificationGroup.TriggerType.EndsWith("]"))
                                    {
                                        currentTriggerTypes = JsonConvert.DeserializeObject<List<string>>(notificationGroup.TriggerType);
                                    }
                                    else
                                    {
                                        currentTriggerTypes = new List<string> { notificationGroup.TriggerType };
                                    }
                                }
                            }
                            catch
                            {
                                currentTriggerTypes = new List<string> { "Event" }; // Default
                            }

                            bool hasScheduledTrigger = currentTriggerTypes.Contains("Scheduled");
                            bool hasActiveSchedules = activeSchedules.Any();

                            // Update trigger types if needed
                            if (!hasActiveSchedules && hasScheduledTrigger)
                            {
                                // Remove "Scheduled" trigger type if no active schedules
                                currentTriggerTypes = currentTriggerTypes.Where(t => t != "Scheduled").ToList();
                                notificationGroup.TriggerType = JsonConvert.SerializeObject(currentTriggerTypes);
                                db.SaveChanges();
                            }
                            else if (hasActiveSchedules && !hasScheduledTrigger)
                            {
                                // Add "Scheduled" trigger type if there are active schedules
                                currentTriggerTypes.Add("Scheduled");
                                notificationGroup.TriggerType = JsonConvert.SerializeObject(currentTriggerTypes);
                                db.SaveChanges();
                            }
                        }

                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Schedule updated successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        System.Diagnostics.Debug.WriteLine($"Error updating notification schedule: {ex.Message}");
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error occurred while updating the schedule");
                    }
                }
            }
        }
    }
}
