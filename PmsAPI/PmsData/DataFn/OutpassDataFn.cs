﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Microsoft.EntityFrameworkCore;

namespace PmsData.DataFn
{
    public class OutpassDataFn
    {
        public GlobalDataEntity GlobalData;
        public OutpassDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public PaginatedResult<OutpassMasterVm> GetAllOutpasswithfilters(OutpassFilterVm filters)
{
    using (var db = new Models.pmsdbContext())
    {
        try
        {
            // Step 1: Create a more efficient base query
            var baseQuery = db.OutpassMasters.AsNoTracking();

            // Step 2: Apply all filters to the base query
            baseQuery = ApplyFilters(baseQuery, filters);

            // Step 3: Get total count from the filtered query
            var totalCount = baseQuery.Count();

            // Step 4: Apply ordering to the base query
            var orderedQuery = baseQuery.OrderByDescending(x => x.OutpassId);

            // Step 5: Apply pagination to the ordered query
            var paginatedQuery = orderedQuery
                .Skip((filters.PageNumber - 1) * filters.PageSize)
                .Take(filters.PageSize);

            // Step 6: Apply joins and projections to the paginated query
            var pagedResults = ApplyJoinsAndProjections(paginatedQuery, db).ToList();

            // Step 7: Conditionally load related data if needed
            if (pagedResults.Any())
            {
                LoadRelatedItems(pagedResults, db, filters);
            }

            // Step 8: Return paginated result
            return new PaginatedResult<OutpassMasterVm>
            {
                Items = pagedResults,
                TotalCount = totalCount,
                PageNumber = filters.PageNumber,
                PageSize = filters.PageSize
            };
        }
        catch (Exception ex)
        {
            // Log the exception
            Console.WriteLine($"Error in GetAllOutpasswithfilters: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");

            return new PaginatedResult<OutpassMasterVm>
            {
                Items = new List<OutpassMasterVm>(),
                TotalCount = 0,
                PageNumber = filters.PageNumber,
                PageSize = filters.PageSize
            };
        }
    }
}

// Helper method to apply filters
private IQueryable<OutpassMaster> ApplyFilters(IQueryable<OutpassMaster> query, OutpassFilterVm filters)
{
    if (filters.OutpassToCustomerId.HasValue && filters.OutpassToCustomerId > 0)
        query = query.Where(x => x.OutpassToCustomerId == filters.OutpassToCustomerId);

    if (!string.IsNullOrEmpty(filters.OutpassNumber))
        query = query.Where(x => x.OutpassNumber.Contains(filters.OutpassNumber));

    if (!string.IsNullOrEmpty(filters.OutpassType))
        query = query.Where(x => x.OutpassType == filters.OutpassType);

    if (!string.IsNullOrEmpty(filters.OutpassTo))
        query = query.Where(x => x.OutpassTo.Contains(filters.OutpassTo));

    if (filters.PurposeId.HasValue && filters.PurposeId > 0)
        query = query.Where(x => x.PurposeId == filters.PurposeId);

    // Date range filter
    if (filters.FromDate.HasValue)
        query = query.Where(x => x.OutpassDate >= filters.FromDate);

    if (filters.ToDate.HasValue)
        query = query.Where(x => x.OutpassDate <= filters.ToDate);

    if (filters.IsOutpassIn.HasValue)
        query = query.Where(x => x.OutpassType == "Returnable" && x.IsOutpassIn == filters.IsOutpassIn);

    return query;
}

// Helper method to apply joins and projections
private IQueryable<OutpassMasterVm> ApplyJoinsAndProjections(IQueryable<OutpassMaster> query, pmsdbContext db)
{
    return query
        .GroupJoin(
            db.UserMasters,
            a => a.AddedBy,
            um => um.Email,
            (a, umGroup) => new { Outpass = a, UserGroup = umGroup })
        .SelectMany(
            x => x.UserGroup.DefaultIfEmpty(),
            (x, um) => new { x.Outpass, User = um })
        .GroupJoin(
            db.TransportCompanyMasters,
            x => x.Outpass.TransportId,
            tc => tc.TransportId,
            (x, tcGroup) => new { x.Outpass, x.User, TransportGroup = tcGroup })
        .SelectMany(
            x => x.TransportGroup.DefaultIfEmpty(),
            (x, tc) => new { x.Outpass, x.User, Transport = tc })
        .GroupJoin(
            db.TransportVehicleTables,
            x => x.Outpass.VehicleId,
            vm => vm.VehicleId,
            (x, vmGroup) => new { x.Outpass, x.User, x.Transport, VehicleGroup = vmGroup })
        .SelectMany(
            x => x.VehicleGroup.DefaultIfEmpty(),
            (x, vm) => new { x.Outpass, x.User, x.Transport, Vehicle = vm })
        .Select(x => new OutpassMasterVm
        {
            OutpassId = x.Outpass.OutpassId,
            OutpassTo = x.Outpass.OutpassTo,
            OutpassDate = x.Outpass.OutpassDate,
            OutpassNumber = x.Outpass.OutpassNumber,
            OutpassType = x.Outpass.OutpassType,
            Purpose = x.Outpass.Purpose,
            PurposeId = x.Outpass.PurposeId,
            Remark = x.Outpass.Remark,
            IsOutpassIn = x.Outpass.IsOutpassIn,
            AddedBy = x.Outpass.AddedBy,
            AddedDate = x.Outpass.AddedDate,
            OutpassToCustomerId = x.Outpass.OutpassToCustomerId,
            ExpectedReturnDate = x.Outpass.ExpectedReturnDate,
            Status = x.Outpass.Status,
            TransportId = x.Outpass.TransportId,
            TransportName = x.Transport != null ? x.Transport.TransportCompanyName : null,
            VehicleId = x.Outpass.VehicleId,
            VehicleNumber = x.Vehicle != null ? x.Vehicle.VehicleNumber : null,
            IsGateIn = x.Outpass.IsGateIn,
            OutpassItems = new List<OutpassItemTableVm>()
        });
}

// Helper method to load related items
private void LoadRelatedItems(List<OutpassMasterVm> pagedResults, pmsdbContext db, OutpassFilterVm filters)
{
    var outpassIds = pagedResults.Select(x => x.OutpassId).ToList();
    
    // Create a base query for items
    var itemsBaseQuery = db.OutpassItemTables
        .AsNoTracking()
        .Where(op => outpassIds.Contains(op.OutpassId.Value));
    
    // Apply product name filter if specified
    if (!string.IsNullOrEmpty(filters.OutpassProductName))
    {
        itemsBaseQuery = itemsBaseQuery.Where(item => 
            item.ProductName.Contains(filters.OutpassProductName));
    }
    
    // Apply joins and projections
    var outpassItems = itemsBaseQuery
        .GroupJoin(
            db.RackMasters,
            op => op.RackId,
            r => r.RackId,
            (op, rGroup) => new { OutpassItem = op, RackGroup = rGroup })
        .SelectMany(
            x => x.RackGroup.DefaultIfEmpty(),
            (x, r) => new { x.OutpassItem, Rack = r })
        .GroupJoin(
            db.StoreMasters,
            x => x.Rack.StoreId,
            s => s.StoreId,
            (x, sGroup) => new { x.OutpassItem, x.Rack, StoreGroup = sGroup })
        .SelectMany(
            x => x.StoreGroup.DefaultIfEmpty(),
            (x, s) => new { x.OutpassItem, x.Rack, Store = s })
        .GroupJoin(
            db.RackMasters,
            x => x.OutpassItem.ReturnedRackId,
            rr => rr.RackId,
            (x, rrGroup) => new { x.OutpassItem, x.Rack, x.Store, ReturnedRackGroup = rrGroup })
        .SelectMany(
            x => x.ReturnedRackGroup.DefaultIfEmpty(),
            (x, rr) => new { x.OutpassItem, x.Rack, x.Store, ReturnedRack = rr })
        .GroupJoin(
            db.StoreMasters,
            x => x.ReturnedRack.StoreId,
            rs => rs.StoreId,
            (x, rsGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, ReturnedStoreGroup = rsGroup })
        .SelectMany(
            x => x.ReturnedStoreGroup.DefaultIfEmpty(),
            (x, rs) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, ReturnedStore = rs })
        .GroupJoin(
            db.UserMasters,
            x => x.OutpassItem.ReturnCompletedBy,
            um => um.Email,
            (x, umGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, UserGroup = umGroup })
        .SelectMany(
            x => x.UserGroup.DefaultIfEmpty(),
            (x, um) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, User = um })
        // Add join with StockProducts table
        .GroupJoin(
            db.StockProductTables,
            x => x.OutpassItem.StockProductId,
            sp => sp.StockProductId,
            (x, spGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, StockProductGroup = spGroup })
        .SelectMany(
            x => x.StockProductGroup.DefaultIfEmpty(),
            (x, sp) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, StockProduct = sp })
        // Add join with StockMasters table
        .GroupJoin(
            db.StockMasters,
            x => x.StockProduct.StockId,
            sm => sm.StockId,
            (x, smGroup) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, x.StockProduct, StockMasterGroup = smGroup })
        .SelectMany(
            x => x.StockMasterGroup.DefaultIfEmpty(),
            (x, sm) => new { x.OutpassItem, x.Rack, x.Store, x.ReturnedRack, x.ReturnedStore, x.User, x.StockProduct, StockMaster = sm })
        .Select(x => new OutpassItemTableVm
        {
            OutpassItemId = x.OutpassItem.OutpassItemId,
            OutpassId = x.OutpassItem.OutpassId,
            ProductName = x.OutpassItem.ProductName,
            StockProductId = x.OutpassItem.StockProductId,
            RackId = x.OutpassItem.RackId,
            Quantity = x.OutpassItem.Quantity,
            Amount = x.OutpassItem.Amount,
            Unit = x.OutpassItem.Unit,
            RackName = x.Rack.RackName,
            StoreName = x.Store.StoreName,
            BatchNo = x.StockMaster.Batch,
            // OutpassType = pagedResults.FirstOrDefault(o => o.OutpassId == x.OutpassItem.OutpassId).OutpassType,
            ReturnCompletedBy = x.User != null ? new UserMasterVm { Name = x.User.Name } : null,
            ReturnCompletedDate = x.OutpassItem.ReturnCompletedDate,
            ReturnedQuantity = x.OutpassItem.ReturnedQuantity,
            ReturnedRackId = x.OutpassItem.ReturnedRackId,
            ReturnedRackName = x.ReturnedRack.RackName,
            ReturnedStoreName = x.ReturnedStore.StoreName
        })
        .ToList();

    // Group items by OutpassId and assign to respective Outpass objects
    var itemsByOutpassId = outpassItems.GroupBy(item => item.OutpassId);
    foreach (var group in itemsByOutpassId)
    {
        var outpass = pagedResults.FirstOrDefault(o => o.OutpassId == group.Key);
        if (outpass != null)
        {
            outpass.OutpassItems = group.ToList();
        }
    }
}
        public OutpassMasterVm GetOutpassById(long OutpassId)
        {
            OutpassMasterVm res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OutpassMasters
                       join um in db.UserMasters on a.AddedBy equals um.Email
                       where a.OutpassId == OutpassId
                       select new OutpassMasterVm
                       {
                           OutpassId = a.OutpassId,
                           OutpassTo = a.OutpassTo,
                           OutpassDate = a.OutpassDate,
                           OutpassNumber = a.OutpassNumber,
                           OutpassType = a.OutpassType,
                           Purpose = a.Purpose,
                           PurposeId = a.PurposeId,
                           Remark = a.Remark,
                           IsOutpassIn = a.IsOutpassIn,
                           AddedBy = um.Name,
                           AddedDate = a.AddedDate,
                           ExpectedReturnDate = a.ExpectedReturnDate,
                           OutpassToCustomerId = a.OutpassToCustomerId,
                           Status = a.Status,
                           TransportId = a.TransportId,
                           VehicleId = a.VehicleId,
                           IsGateIn = a.IsGateIn,
                           ApprovedByName = (from osh in db.OutpassStatusHistories
                                             join um in db.UserMasters on osh.AddedBy equals um.Email
                                             where osh.OutpassId == a.OutpassId && osh.Status == "Approved"
                                             select um.Name).FirstOrDefault(),
                           OutpassItems = (from op in db.OutpassItemTables
                                           join sp in db.StockProductTables on op.StockProductId equals sp.StockProductId into spd
                                           from sp in spd.DefaultIfEmpty()
                                           join sm in db.StockMasters on sp.StockId equals sm.StockId into smd
                                           from sm in smd.DefaultIfEmpty()
                                           join p in db.ProductMasters on sp.ProductId equals p.ProductId into pd
                                           from p in pd.DefaultIfEmpty()
                                           join r in db.RackMasters on op.RackId equals r.RackId into rc
                                           from r in rc.DefaultIfEmpty()
                                           join s in db.StoreMasters on r.StoreId equals s.StoreId into sc
                                           from s in sc.DefaultIfEmpty()
                                           join rr in db.RackMasters on op.ReturnedRackId equals rr.RackId into rrc
                                           from rr in rrc.DefaultIfEmpty()
                                           join rs in db.StoreMasters on rr.StoreId equals rs.StoreId into rsc
                                           from rs in rsc.DefaultIfEmpty()
                                           join um in db.UserMasters on op.ReturnCompletedBy equals um.Email into umd
                                           from um in umd.DefaultIfEmpty()
                                           where op.OutpassId == a.OutpassId
                                           select new OutpassItemTableVm
                                           {
                                               OutpassItemId = op.OutpassItemId,
                                               OutpassId = op.OutpassId,
                                               ProductId = p.ProductId,
                                               ProductName = op.ProductName,
                                               StockProductId = op.StockProductId,
                                               RackId = op.RackId,
                                               Quantity = op.Quantity,
                                               Amount = op.Amount,
                                               Unit = op.Unit,
                                               RackName = r.RackName,
                                               StoreName = s.StoreName,
                                               BatchNo = sm.Batch,
                                               OutpassType = a.OutpassType,
                                               ReturnCompletedBy = new UserMasterVm
                                               {
                                                   Name = um.Name
                                               },
                                               ReturnCompletedDate = op.ReturnCompletedDate,
                                               ReturnedQuantity = op.ReturnedQuantity,
                                               ReturnedRackId = op.ReturnedRackId,
                                               ReturnedRackName = rr.RackName,
                                               ReturnedStoreName = rs.StoreName
                                           }).ToList()
                       }).FirstOrDefault();
            }
            return res;
        }
        public ApiFunctionResponseVm AddOutpasss(OutpassMasterVm Outpass)
        {
            using (var db = new Models.pmsdbContext())
            {
                OutpassMaster mm = new OutpassMaster
                {
                    OutpassId = Outpass.OutpassId,
                    OutpassTo = Outpass.OutpassTo,
                    OutpassDate = Outpass.OutpassDate,
                    OutpassType = Outpass.OutpassType,
                    Purpose = Outpass.Purpose,
                    PurposeId = Outpass.PurposeId,
                    Remark = Outpass.Remark,
                    IsOutpassIn = false,
                    AddedBy = Outpass.AddedBy,
                    AddedDate = DateTime.Now,
                    OutpassToCustomerId = Outpass.OutpassToCustomerId,
                    ExpectedReturnDate = Outpass.ExpectedReturnDate,
                    Status = OutpassStatus.ApprovalPending,
                    TransportId = Outpass.TransportId,
                    VehicleId = Outpass.VehicleId,
                    IsGateIn = Outpass.VehicleId > 0
                };
                db.OutpassMasters.Add(mm);
                db.SaveChanges();
                mm.OutpassNumber = "Z-DC/" + mm.OutpassId;
                db.SaveChanges();

                if (Outpass.VehicleId > 0)
                {
                    GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == Outpass.VehicleId && x.GateIn == true && x.GateOut != true);
                    if (sm == null)
                    {
                        sm = new GateInTable();
                        sm.VehicleId = Outpass.VehicleId.Value;
                        sm.GateInDate = System.DateTime.Now;
                        sm.GateInPerson = "System";
                        sm.GateInPersonContact = "System";
                        sm.GateIn = true;
                        sm.AddedBy = GlobalData.loggedInUser;
                        sm.AddedDate = System.DateTime.Now;
                        sm.Type = "Outpass Dispatch";
                        db.GateInTables.Add(sm);
                        db.SaveChanges();
                    }
                    GateInInvoiceMappingTable gi = new()
                    {
                        OutpassId = mm.OutpassId,
                        GateInId = sm.GateInId
                    };
                    db.GateInInvoiceMappingTables.Add(gi);
                    db.SaveChanges();
                }

                db.OutpassStatusHistories.Add(
                    new OutpassStatusHistory
                    {
                        OutpassId = mm.OutpassId,
                        Status = OutpassStatus.ApprovalPending,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        Remark = Outpass.Remark
                    }
                );

                foreach (var op in Outpass.OutpassItems)
                {
                    OutpassItemTable spt = new OutpassItemTable
                    {
                        OutpassItemId = op.OutpassItemId,
                        OutpassId = mm.OutpassId,
                        ProductName = op.ProductName,
                        StockProductId = op.StockProductId,
                        RackId = op.RackId,
                        Quantity = op.Quantity,
                        Amount = op.Amount,
                        Unit = op.Unit
                    };
                    db.OutpassItemTables.Add(spt);
                    // if (op.StockProductId > 0)
                    // {
                    //     var removestock = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.RackId);
                    //     removestock.Quantity = removestock.Quantity - op.Quantity.Value;
                    //     if (removestock.Quantity == 0)
                    //     {
                    //         db.StockProductAllocationTables.Remove(removestock);
                    //     }
                    // }
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
        public ApiFunctionResponseVm ModifyOutpass(OutpassMasterVm Outpass)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var existing = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == Outpass.OutpassId);
                var PreviousExpectedReturnDate = existing.ExpectedReturnDate;
                if (existing != null && Outpass.VehicleId > 0)
                {
                    existing.VehicleId = Outpass.VehicleId;
                    existing.TransportId = Outpass.TransportId;
                    existing.IsGateIn = true;
                    db.SaveChanges();
                }
                else if (existing != null && Outpass.ExpectedReturnDate != null)
                {
                    existing.ExpectedReturnDate = Outpass.ExpectedReturnDate;
                    db.SaveChanges();

                    db.OutpassStatusHistories.Add(
                        new OutpassStatusHistory
                        {
                            OutpassId = Outpass.OutpassId,
                            Status = OutpassStatus.ReturnExtended,
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = DateTime.Now,
                            Remark = "Return Extended from " + TimeZoneHelper.ConvertToTimeZone(PreviousExpectedReturnDate.Value, TimeZoneId.IndiaStandardTime) + " To " + TimeZoneHelper.ConvertToTimeZone(Outpass.ExpectedReturnDate.Value, TimeZoneId.IndiaStandardTime) + ". Due to: " + Outpass.Remark,
                        });
                }

                if (Outpass.VehicleId > 0)
                {
                    GateInTable sm = db.GateInTables.FirstOrDefault(x => x.VehicleId == Outpass.VehicleId && x.GateIn == true && x.GateOut != true);
                    if (sm == null)
                    {
                        sm = new GateInTable();
                        sm.VehicleId = Outpass.VehicleId.Value;
                        sm.GateInDate = System.DateTime.Now;
                        sm.GateInPerson = "System";
                        sm.GateInPersonContact = "System";
                        sm.GateIn = true;
                        sm.GateOut = false;
                        sm.AddedBy = GlobalData.loggedInUser;
                        sm.AddedDate = System.DateTime.Now;
                        sm.Type = "Outpass Dispatch";
                        db.GateInTables.Add(sm);
                        db.SaveChanges();
                    }
                    GateInInvoiceMappingTable gi = new()
                    {
                        OutpassId = Outpass.OutpassId,
                        GateInId = sm.GateInId
                    };
                    db.GateInInvoiceMappingTables.Add(gi);
                    var vehicleInfo = (from tc in db.TransportCompanyMasters
                                       join vm in db.TransportVehicleTables on tc.TransportId equals vm.TransportId
                                       where vm.VehicleId == Outpass.VehicleId
                                       select new
                                       {
                                           tc.TransportCompanyName,
                                           vm.VehicleNumber,
                                           vm.VehicleType
                                       }).FirstOrDefault();
                    if (vehicleInfo != null)
                    {
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = Outpass.OutpassId,
                                Status = OutpassStatus.VehicleAssigned,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = "Vehicle Type: " + vehicleInfo.VehicleType + " with Registration No.: " + vehicleInfo.VehicleNumber + " from " + vehicleInfo.TransportCompanyName + " Assigned."
                            });
                    }
                    else
                    {
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = Outpass.OutpassId,
                                Status = OutpassStatus.VehicleAssigned,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = "Vehicle assigned"
                            });
                    }
                }
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Outpass Updated Successfully.");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }

        }

        public ApiFunctionResponseVm InOutpass(OutpassMasterVm Outpass)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                OutpassMaster mm = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == Outpass.OutpassId);
                mm.IsOutpassIn = true;
                mm.Status = OutpassStatus.ReturnCompleted;
                db.SaveChanges();
                foreach (var op in Outpass.OutpassItems)
                {
                    OutpassItemTable spt = db.OutpassItemTables.FirstOrDefault(x => x.OutpassItemId == op.OutpassItemId);
                    spt.ReturnedQuantity = op.ReturnedQuantity;
                    spt.ReturnCompletedBy = GlobalData.loggedInUser;
                    spt.ReturnCompletedDate = DateTime.Now;
                    spt.ReturnedRackId = op.ReturnedRackId;
                    db.SaveChanges();
                    if (op.StockProductId > 0)
                    {
                        var stockrec = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.ReturnedRackId && x.InspectionType == PmsCommon.PMSStatus.Accepted);
                        if (stockrec == null)
                        {
                            var spa = new StockProductAllocationTable()
                            {
                                StockProductId = op.StockProductId.Value,
                                Quantity = op.ReturnedQuantity.Value,
                                InspectionType = PmsCommon.PMSStatus.Accepted,
                                RackId = op.ReturnedRackId.Value
                            };
                            db.StockProductAllocationTables.Add(spa);
                            db.SaveChanges();
                        }
                        else
                        {
                            stockrec.Quantity = stockrec.Quantity + op.Quantity.Value;
                            db.SaveChanges();
                        }
                    }
                }
                db.OutpassStatusHistories.Add(
                    new OutpassStatusHistory
                    {
                        OutpassId = Outpass.OutpassId,
                        Status = OutpassStatus.ReturnCompleted,
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        Remark = Outpass.Remark,
                    }
                );
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Outpass return submitted successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }
        }
        public List<OutPassPurposeMasterVm> GetAllOutPassPurposes()
        {
            List<OutPassPurposeMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OutPassPurposeMasters
                       where a.Disabled != true
                       select new OutPassPurposeMasterVm
                       {
                           PurposeId = a.PurposeId,
                           PurposeName = a.PurposeName,
                           PurposeCode = a.PurposeCode,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                       }).OrderByDescending(x => x.PurposeId).ToList();
            }
            return res;
        }

        public ApiFunctionResponseVm AddUpdateOutPassPurpose(OutPassPurposeMasterVm item)
        {
            using (var db = new Models.pmsdbContext())
            {
                OutPassPurposeMaster res = new OutPassPurposeMaster();
                if (item.PurposeId == 0)
                {
                    res.PurposeName = item.PurposeName;
                    res.AddedBy = GlobalData.loggedInUser;
                    res.AddedDate = System.DateTime.Now;
                    db.OutPassPurposeMasters.Add(res);
                    db.SaveChanges();

                    res.PurposeCode = "OP-" + res.PurposeId;
                }
                else
                {
                    res = db.OutPassPurposeMasters.Where(x => x.PurposeId == item.PurposeId).FirstOrDefault();

                    if (res != null)
                    {
                        res.PurposeName = item.PurposeName;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.AuditTables.Add(new AuditTable
                        {
                            RecId = res.PurposeId,
                            TableName = "OutPassPurposeMaster",
                            EntityName = "OutPassPurposeMasters",
                            AddedBy = GlobalData.loggedInUser,
                            AddedDate = System.DateTime.Now,
                        });
                        db.SaveChanges();
                    }
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm DeleteOutPassPurpose(long itemid)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var res = db.OutPassPurposeMasters.Where(x => x.PurposeId == itemid).FirstOrDefault();
                        if (res != null)
                        {
                            res.Disabled = true;
                            res.DisabledBy = GlobalData.loggedInUser;
                            res.DisabledDate = DateTime.Now;
                            db.SaveChanges();

                        }
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "OutPass Purpose Deleted Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                        throw;
                    }
                }
            }
        }
        public ApiFunctionResponseVm OutpassStatusActions(OutpassStatusActionVm actionVm)
        {
            using var db = new pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {
                var outpass = db.OutpassMasters.FirstOrDefault(x => x.OutpassId == actionVm.OutpassId);
                if (outpass == null)
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Outpass not found");
                }
                switch (actionVm.Status)
                {
                    case OutpassStatus.Approved:
                        outpass.Status = OutpassStatus.Approved;
                        var outpassHistory = db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Approved,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = actionVm.Remark
                            }
                        );
                        var OutpassItems = db.OutpassItemTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        foreach (var op in OutpassItems)
                        {
                            if (op.StockProductId > 0)
                            {
                                var removestock = db.StockProductAllocationTables.FirstOrDefault(x => x.StockProductId == op.StockProductId && x.RackId == op.RackId);
                                removestock.Quantity = removestock.Quantity - op.Quantity.Value;
                                if (removestock.Quantity == 0)
                                {
                                    db.StockProductAllocationTables.Remove(removestock);
                                }
                            }
                        }
                        break;
                    case OutpassStatus.Cancelled:
                        outpass.Status = OutpassStatus.Cancelled;

                        var gateInMapping = db.GateInInvoiceMappingTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        if (gateInMapping != null && gateInMapping.Count == 1)
                        {
                            var gateIn = db.GateInTables.FirstOrDefault(x => x.GateInId == gateInMapping[0].GateInId);
                            db.GateInTables.Remove(gateIn);
                            db.GateInInvoiceMappingTables.RemoveRange(gateInMapping);
                        }

                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Cancelled,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    case OutpassStatus.Rejected:
                        outpass.Status = OutpassStatus.Rejected;

                        var gateInMappingR = db.GateInInvoiceMappingTables.Where(x => x.OutpassId == outpass.OutpassId).ToList();
                        if (gateInMappingR != null && gateInMappingR.Count == 1)
                        {
                            var gateIn = db.GateInTables.FirstOrDefault(x => x.GateInId == gateInMappingR[0].GateInId);
                            db.GateInTables.Remove(gateIn);
                            db.GateInInvoiceMappingTables.RemoveRange(gateInMappingR);
                        }
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.Rejected,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    case OutpassStatus.OnHold:
                        outpass.Status = OutpassStatus.OnHold;
                        db.OutpassStatusHistories.Add(
                            new OutpassStatusHistory
                            {
                                OutpassId = outpass.OutpassId,
                                Status = OutpassStatus.OnHold,
                                AddedBy = GlobalData.loggedInUser,
                                AddedDate = DateTime.Now,
                                Remark = actionVm.Remark
                            }
                        );
                        break;
                    default:
                        break;
                }
                db.SaveChanges();
                transaction.Commit();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Outpass Status Updated Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
                throw;
            }
        }
        public List<OutpassStatusHistoryVm> GetOutpassTimeline(long outpassId)
        {
            using var db = new pmsdbContext();
            var res = (from a in db.OutpassStatusHistories
                       join b in db.UserMasters on a.AddedBy equals b.Email
                       join c in db.OutpassMasters on a.OutpassId equals c.OutpassId
                       where a.OutpassId == outpassId
                       select new OutpassStatusHistoryVm
                       {
                           OutpassId = a.OutpassId,
                           OutpassNumber = c.OutpassNumber,
                           Status = a.Status,
                           AddedBy = a.AddedBy,
                           AddedByName = b.Name,
                           AddedDate = a.AddedDate,
                           Remark = a.Remark
                       }).ToList();
            return res;
        }
    }
}
