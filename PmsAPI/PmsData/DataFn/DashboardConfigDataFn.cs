using Microsoft.EntityFrameworkCore;
using PmsCommon;
using PmsData.Models;
using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsData.DataFn
{
    /// <summary>
    /// Dashboard Configuration Data Functions
    /// Following PMS DataFn naming and structure patterns
    /// </summary>
    public class DashboardConfigDataFn
    {
        public GlobalDataEntity GlobalData;

        public DashboardConfigDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }

        /// <summary>
        /// Get user's default dashboard configuration with fallback mechanisms
        /// </summary>
        public UserDashboardConfigVm GetUserDefaultConfig(string userId, string dashboardType)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    // Check if table exists first (fallback mechanism)
                    if (!DoesTableExist(db, "UserDashboardConfig"))
                    {
                        return GetHardcodedDefault(dashboardType);
                    }

                    var config = db.UserDashboardConfigTables
                        .Where(a => a.UserId == userId
                                 && a.DashboardType == dashboardType
                                 && a.IsDefault == true
                                 && a.Disabled == false)
                        .OrderByDescending(a => a.ModifiedDate)
                        .Select(a => new UserDashboardConfigVm
                        {
                            ConfigId = a.ConfigId,
                            UserId = a.UserId,
                            DashboardType = a.DashboardType,
                            ConfigJson = a.ConfigJson,
                            ConfigName = a.ConfigName,
                            Description = a.Description,
                            IsDefault = a.IsDefault,
                            Version = a.Version,
                            Tags = a.Tags
                        })
                        .FirstOrDefault();

                    if (config != null)
                    {
                        return config;
                    }

                    // No user default found, try system default
                    return GetSystemDefault(dashboardType) ?? GetHardcodedDefault(dashboardType);
                }
            }
            catch (Exception ex)
            {
                // PMS pattern: Log but don't throw, return fallback
                Console.WriteLine($"Error in GetUserDefaultConfig: {ex.Message}");
                return GetHardcodedDefault(dashboardType);
            }
        }

        /// <summary>
        /// Get system default configuration with fallback
        /// </summary>
        public UserDashboardConfigVm GetSystemDefault(string dashboardType)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    if (!DoesTableExist(db, "SystemDashboardDefaults"))
                    {
                        return GetHardcodedDefault(dashboardType);
                    }

                    var systemDefault = db.SystemDashboardDefaultsTables
                        .Where(a => a.DashboardType == dashboardType && a.Disabled == false)
                        .OrderByDescending(a => a.Version)
                        .FirstOrDefault();

                    if (systemDefault != null)
                    {
                        return new UserDashboardConfigVm
                        {
                            ConfigId = 0,
                            UserId = "system",
                            DashboardType = dashboardType,
                            ConfigJson = systemDefault.ConfigJson,
                            ConfigName = "System Default",
                            Description = "System provided default configuration",
                            IsDefault = true,
                            Version = systemDefault.Version
                        };
                    }

                    return GetHardcodedDefault(dashboardType);
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetSystemDefault: {ex.Message}");
                return GetHardcodedDefault(dashboardType);
            }
        }

        /// <summary>
        /// Save user dashboard configuration
        /// </summary>
        public long SaveUserDashboardConfig(UserDashboardConfigVm config)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    // Check if table exists
                    if (!DoesTableExist(db, "UserDashboardConfig"))
                    {
                        throw new InvalidOperationException("Dashboard configuration table not available");
                    }

                    // Clear other defaults if this is being set as default
                    if (config.IsDefault)
                    {
                        var existingDefaults = db.UserDashboardConfigTables
                            .Where(a => a.UserId == config.UserId
                                     && a.DashboardType == config.DashboardType
                                     && a.IsDefault == true)
                            .ToList();

                        foreach (var existing in existingDefaults)
                        {
                            existing.IsDefault = false;
                            existing.ModifiedBy = GlobalData.loggedInUser;
                            existing.ModifiedDate = DateTime.Now;
                        }
                    }

                    var entity = new UserDashboardConfigTable
                    {
                        UserId = config.UserId,
                        DashboardType = config.DashboardType,
                        ConfigJson = config.ConfigJson,
                        ConfigName = config.ConfigName ?? "User Configuration",
                        Description = config.Description ?? "",
                        IsDefault = config.IsDefault,
                        Disabled = false,
                        Version = config.Version,
                        Tags = config.Tags ?? "",
                        AddedBy = GlobalData.loggedInUser,
                        AddedDate = DateTime.Now,
                        ModifiedBy = GlobalData.loggedInUser,
                        ModifiedDate = DateTime.Now
                    };

                    db.UserDashboardConfigTables.Add(entity);
                    db.SaveChanges();

                    return entity.ConfigId;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SaveUserDashboardConfig: {ex.Message}");
                throw; // Business layer will handle this
            }
        }

        /// <summary>
        /// Get all user configurations
        /// </summary>
        public List<UserDashboardConfigVm> GetAllUserConfigs(string userId, string dashboardType)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    if (!DoesTableExist(db, "UserDashboardConfig"))
                    {
                        return new List<UserDashboardConfigVm>();
                    }

                    return db.UserDashboardConfigTables
                        .Where(a => a.UserId == userId
                                 && a.DashboardType == dashboardType
                                 && a.Disabled == false)
                        .OrderByDescending(a => a.IsDefault)
                        .ThenByDescending(a => a.ModifiedDate)
                        .Select(a => new UserDashboardConfigVm
                        {
                            ConfigId = a.ConfigId,
                            UserId = a.UserId,
                            DashboardType = a.DashboardType,
                            ConfigJson = a.ConfigJson,
                            ConfigName = a.ConfigName,
                            Description = a.Description,
                            IsDefault = a.IsDefault,
                            Version = a.Version,
                            Tags = a.Tags
                        })
                        .ToList();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in GetAllUserConfigs: {ex.Message}");
                return new List<UserDashboardConfigVm>();
            }
        }

        /// <summary>
        /// Update existing dashboard configuration
        /// </summary>
        public bool UpdateUserDashboardConfig(UserDashboardConfigVm config)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    if (!DoesTableExist(db, "UserDashboardConfig"))
                    {
                        throw new InvalidOperationException("Dashboard configuration table not available");
                    }

                    var existing = db.UserDashboardConfigTables
                        .FirstOrDefault(a => a.ConfigId == config.ConfigId && a.UserId == config.UserId);

                    if (existing == null)
                    {
                        return false;
                    }

                    // Clear other defaults if this is being set as default
                    if (config.IsDefault && !existing.IsDefault)
                    {
                        var otherDefaults = db.UserDashboardConfigTables
                            .Where(a => a.UserId == config.UserId
                                     && a.DashboardType == config.DashboardType
                                     && a.IsDefault == true
                                     && a.ConfigId != config.ConfigId)
                            .ToList();

                        foreach (var other in otherDefaults)
                        {
                            other.IsDefault = false;
                            other.ModifiedBy = GlobalData.loggedInUser;
                            other.ModifiedDate = DateTime.Now;
                        }
                    }

                    // Update the configuration
                    existing.ConfigJson = config.ConfigJson;
                    existing.ConfigName = config.ConfigName ?? existing.ConfigName;
                    existing.Description = config.Description ?? existing.Description;
                    existing.IsDefault = config.IsDefault;
                    existing.Version = config.Version;
                    existing.Tags = config.Tags ?? existing.Tags;
                    existing.ModifiedBy = GlobalData.loggedInUser;
                    existing.ModifiedDate = DateTime.Now;

                    db.SaveChanges();
                    return true;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in UpdateUserDashboardConfig: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Delete user dashboard configuration (soft delete)
        /// </summary>
        public bool DeleteUserDashboardConfig(long configId, string userId)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    var config = db.UserDashboardConfigTables
                        .FirstOrDefault(a => a.ConfigId == configId && a.UserId == userId);

                    if (config != null)
                    {
                        // Soft delete following PMS pattern
                        config.Disabled = true;
                        config.ModifiedBy = GlobalData.loggedInUser;
                        config.ModifiedDate = DateTime.Now;
                        db.SaveChanges();
                        return true;
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in DeleteUserDashboardConfig: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Set configuration as default for user
        /// </summary>
        public bool SetAsDefaultConfig(long configId, string userId, string dashboardType)
        {
            try
            {
                using (var db = new pmsdbContext())
                {
                    if (!DoesTableExist(db, "UserDashboardConfig"))
                    {
                        return false;
                    }

                    // Clear all existing defaults
                    var existingDefaults = db.UserDashboardConfigTables
                        .Where(a => a.UserId == userId
                                 && a.DashboardType == dashboardType
                                 && a.IsDefault == true)
                        .ToList();

                    foreach (var existing in existingDefaults)
                    {
                        existing.IsDefault = false;
                        existing.ModifiedBy = GlobalData.loggedInUser;
                        existing.ModifiedDate = DateTime.Now;
                    }

                    // Set new default
                    var newDefault = db.UserDashboardConfigTables
                        .FirstOrDefault(a => a.ConfigId == configId && a.UserId == userId);

                    if (newDefault != null)
                    {
                        newDefault.IsDefault = true;
                        newDefault.ModifiedBy = GlobalData.loggedInUser;
                        newDefault.ModifiedDate = DateTime.Now;
                        db.SaveChanges();
                        return true;
                    }

                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in SetAsDefaultConfig: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Check if table exists (fallback mechanism)
        /// </summary>
        private bool DoesTableExist(pmsdbContext db, string tableName)
        {
            try
            {
                var sql = $"SELECT CASE WHEN EXISTS (SELECT 1 FROM sys.tables WHERE name = '{tableName}') THEN 1 ELSE 0 END";
                var connection = db.Database.GetDbConnection();
                if (connection.State != System.Data.ConnectionState.Open)
                {
                    connection.Open();
                }

                using var command = connection.CreateCommand();
                command.CommandText = sql;
                var result = command.ExecuteScalar();
                return Convert.ToInt32(result) == 1;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Hardcoded fallback configurations following PMS patterns
        /// </summary>
        private UserDashboardConfigVm GetHardcodedDefault(string dashboardType)
        {
            var gateDefault = @"{
                ""sections"": [
                    {
                        ""id"": ""operations"",
                        ""title"": ""Gate Operations"",
                        ""description"": ""Gate management and monitoring"",
                        ""isVisible"": true,
                        ""order"": 0,
                        ""isCollapsed"": false,
                        ""tiles"": [
                            {
                                ""id"": ""pending-gate-out"",
                                ""title"": ""Pending Gate-Out"",
                                ""description"": ""Items pending gate-out approval"",
                                ""value"": 0,
                                ""icon"": ""exit_to_app"",
                                ""color"": ""#ff9800"",
                                ""isVisible"": true,
                                ""order"": 0,
                                ""sectionId"": ""operations"",
                                ""actionRoute"": ""/home/<USER>/gateout"",
                                ""actionLabel"": ""View Gate Out""
                            },
                            {
                                ""id"": ""pending-gate-passes"",
                                ""title"": ""Pending Gate Passes"",
                                ""description"": ""Gate passes awaiting approval"",
                                ""value"": 0,
                                ""icon"": ""assignment"",
                                ""color"": ""#2196f3"",
                                ""isVisible"": true,
                                ""order"": 1,
                                ""sectionId"": ""operations"",
                                ""actionRoute"": ""/home/<USER>/gatepass"",
                                ""actionLabel"": ""View Gate Pass""
                            }
                        ]
                    },
                    {
                        ""id"": ""analytics"",
                        ""title"": ""Analytics"",
                        ""description"": ""Gate performance metrics and analytics"",
                        ""isVisible"": true,
                        ""order"": 1,
                        ""isCollapsed"": false,
                        ""tiles"": [
                            {
                                ""id"": ""invoices-without-po"",
                                ""title"": ""Invoice Gate-ins Without Purchase Order"",
                                ""description"": ""Invoices processed without purchase orders"",
                                ""value"": 0,
                                ""icon"": ""warning"",
                                ""color"": ""#95de64"",
                                ""isVisible"": true,
                                ""order"": 0,
                                ""sectionId"": ""analytics"",
                                ""actionRoute"": ""/home/<USER>/gatein"",
                                ""actionLabel"": ""View Gate In""
                            }
                        ]
                    }
                ],
                ""lastModified"": """ + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") + @""",
                ""version"": 1
            }";

            var productionDefault = @"{
                ""sections"": [
                    {
                        ""id"": ""production-overview"",
                        ""title"": ""Production Overview"",
                        ""description"": ""Production metrics and status"",
                        ""isVisible"": true,
                        ""order"": 0,
                        ""isCollapsed"": false,
                        ""tiles"": []
                    }
                ],
                ""lastModified"": """ + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") + @""",
                ""version"": 1
            }";

            var salesDefault = @"{
                ""sections"": [
                    {
                        ""id"": ""sales-overview"",
                        ""title"": ""Sales Overview"",
                        ""description"": ""Sales metrics and performance"",
                        ""isVisible"": true,
                        ""order"": 0,
                        ""isCollapsed"": false,
                        ""tiles"": []
                    }
                ],
                ""lastModified"": """ + DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") + @""",
                ""version"": 1
            }";

            var configs = new Dictionary<string, string>
            {
                ["gate"] = gateDefault,
                ["production"] = productionDefault,
                ["sales"] = salesDefault
            };

            return new UserDashboardConfigVm
            {
                ConfigId = -1, // Indicates hardcoded fallback
                UserId = "system",
                DashboardType = dashboardType,
                ConfigJson = configs.GetValueOrDefault(dashboardType, configs["gate"]),
                ConfigName = "System Default (Fallback)",
                Description = "Hardcoded fallback configuration",
                IsDefault = true,
                Version = 1
            };
        }
    }
}
