﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;

namespace PmsData.DataFn
{
    public class OrderDataFn
    {
        public List<OrderTableVm> GetAllOrders()
        {
            List<OrderTableVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.OrderTables
                       join i in db.CustomerMasters on a.CustomerId equals i.CustomerId
                       select new OrderTableVm
                       {
                           OrderId = a.OrderId,
                           SaleOrderNo = a.SaleOrderNo,
                           CustomerId = a.CustomerId,
                           CustomerName = i.CustomerName,
                           OrderCreationDate = a.OrderCreationDate,
                           DeliveryDate = a.DeliveryDate,
                           OrderStatus = a.OrderStatus,
                           OrderType = a.OrderType,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           OrderProduct = (from op in db.OrderProductTables
                                           join p in db.ProductMasters on op.ProductId equals p.ProductId
                                           join c in db.ColorMasters on op.ColorId equals c.ColorId
                                           where op.OrderId == a.OrderId
                                           select new OrderProductTableVm
                                           {
                                               OrderProductId = op.OrderProductId,
                                               OrderId = op.OrderId,
                                               ProductId = op.ProductId,
                                               ProductName = p.ProductName,
                                               ProductCode = p.ProductCode,
                                               ColorId = op.ColorId,
                                               ColorName = c.ColorName,
                                               Width = op.Width,
                                               Thick = op.Thick,
                                               Unit = op.Unit,
                                               Rate = op.Rate,
                                               Quantity = op.Quantity,
                                               Grade = op.Grade,
                                           }).ToList()
                       }).OrderByDescending(x => x.OrderId).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddOrders(OrderTableVm order)
        {
            using (var db = new Models.pmsdbContext())
            {
                OrderTable ot = new OrderTable();
                ot.SaleOrderNo = order.SaleOrderNo;
                ot.CustomerId = order.CustomerId;
                ot.OrderCreationDate = order.OrderCreationDate;
                ot.DeliveryDate = order.DeliveryDate;
                ot.OrderType = order.OrderType;
                ot.OrderStatus = PmsCommon.PMSStatus.NotStarted;
                ot.AddedBy = order.AddedBy;
                ot.AddedDate = System.DateTime.Now;
                db.OrderTables.Add(ot);
                db.SaveChanges();
                foreach (var item in order.OrderProduct)
                {
                    OrderProductTable spt = new OrderProductTable();
                    spt.OrderId = ot.OrderId;
                    spt.ProductId = item.ProductId;
                    spt.ColorId = item.ColorId;
                    spt.ProductId = item.ProductId;
                    spt.Width = item.Width;
                    spt.Thick = item.Thick;
                    spt.Unit = item.Unit;
                    spt.Rate = item.Rate;
                    spt.Quantity = item.Quantity;
                    spt.Grade = item.Grade;
                    db.OrderProductTables.Add(spt);
                }
                db.SaveChanges();
            }
            return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
        }
    }
}
