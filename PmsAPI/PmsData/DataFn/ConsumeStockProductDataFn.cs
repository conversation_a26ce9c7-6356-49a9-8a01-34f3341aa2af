﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Microsoft.Extensions.Azure;

namespace PmsData.DataFn
{
    public class ConsumeStockProductDataFn
    {
        public GlobalDataEntity GlobalData;
        public ConsumeStockProductDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ConsumeStockProductMasterVm> GetAllConsumeStockProducts()
        {
            List<ConsumeStockProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ConsumeStockProductMasters
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId
                       join s in db.StoreMasters on a.StoreId equals s.StoreId
                       join r in db.RackMasters on a.RackId equals r.RackId
                       join so in db.SaleOrderTables on a.SaleOrderId equals so.SaleOrderId into solg
                       from so in solg.DefaultIfEmpty()
                       select new ConsumeStockProductMasterVm
                       {
                           ConsumeStockProductId = a.ConsumeStockProductId,
                           RackId = a.RackId,
                           RackCode = r.RackCode,
                           RackName = r.RackName,
                           StoreId = a.StoreId,
                           StoreCode = s.StoreCode,
                           StoreName = s.StoreName,
                           ProductId = a.ProductId,
                           ProductCode = pr.ProductCode,
                           ProductName = pr.ProductName,
                           Quantity = a.Quantity,
                           SCQuantity = a.Scquantity,
                           Unit = a.Unit,
                           ConsumedDate = a.ConsumedDate,
                           IsDamaged = a.IsDamaged,
                           SaleOrderId = a.SaleOrderId,
                           SaleOrderNumber = so.SaleOrderNumber,
                           StockProductId = a.StockProductId,
                           Purpose = a.Purpose,
                           StockId = a.StockId,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderByDescending(x => x.ConsumeStockProductId).ToList();
            }
            return res;
        }

        public List<ConsumeStockProductMasterVm> GetConsumptionbyStockProductId(long stockProductId)
        {
            List<ConsumeStockProductMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ConsumeStockProductMasters
                       join pr in db.ProductMasters on a.ProductId equals pr.ProductId
                       join s in db.StoreMasters on a.StoreId equals s.StoreId
                       join r in db.RackMasters on a.RackId equals r.RackId
                       join so in db.SaleOrderTables on a.SaleOrderId equals so.SaleOrderId into solg
                       from so in solg.DefaultIfEmpty()
                       where a.StockProductId == stockProductId
                       select new ConsumeStockProductMasterVm
                       {
                           ConsumeStockProductId = a.ConsumeStockProductId,
                           RackId = a.RackId,
                           RackCode = r.RackCode,
                           RackName = r.RackName,
                           StoreId = a.StoreId,
                           StoreCode = s.StoreCode,
                           StoreName = s.StoreName,
                           ProductId = a.ProductId,
                           ProductCode = pr.ProductCode,
                           ProductName = pr.ProductName,
                           Quantity = a.Quantity,
                           SCQuantity = a.Scquantity,
                           Unit = a.Unit,
                           ConsumedDate = a.ConsumedDate,
                           IsDamaged = a.IsDamaged,
                           SaleOrderId = a.SaleOrderId,
                           SaleOrderNumber = so.SaleOrderNumber,
                           StockProductId = a.StockProductId,
                           Purpose = a.Purpose,
                           StockId = a.StockId,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderByDescending(x => x.ConsumeStockProductId).ToList();
            }
            return res;
        }
        public List<PendingConsumptionOrdersResponseVm> GetAllPendingConsumptionOrderswithFilter(PendingConsumptionOrdersRequestVm filters)
        {
            List<ESalesOrderStatus> stsList = new();
            foreach (var item in filters.Status)
            {
                stsList.Add(PMSEnum.ParseEnum<ESalesOrderStatus>(item));
            }

            using var db = new pmsdbContext();
            var res = (from so in db.SaleOrderTables
                       join sop in db.SaleOrderProductionTables on so.SaleOrderId equals sop.SaleOrderId
                       join sopc in db.SaleOrderProductionCompleteTables on so.SaleOrderId equals sopc.SaleOrderId into sopcd
                       from sopc in sopcd.DefaultIfEmpty()
                       join ifcm in db.InspectionFormulationCodeMixingTables on so.SaleOrderId equals ifcm.SaleOrderId into ifcmd
                       from ifcm in ifcmd.DefaultIfEmpty()
                       join isfcm in db.InspectionSaleFormulationCodeMasters on ifcm.InspectionSaleFormulationCodeId equals isfcm.InspectionSaleFormulationCodeId into isfcmd
                       from isfcm in isfcmd.DefaultIfEmpty()
                       join sot in db.SaleOrderTimelineTables on so.SaleOrderId equals sot.SaleOrderId
                       join wpo in db.WorkPlanOrders on so.SaleOrderId equals wpo.OrderId
                       join wp in db.WorkPlanMasters on wpo.WorkplanId equals wp.WorkPlanId
                       join fc in db.SaleFormulationCodeMasters on so.SaleFormulationCodeId equals fc.SaleFormulationCodeId
                       join fcp in db.ProductMasters on fc.FabricProductId equals fcp.ProductId into fcpd
                       from fcp in fcpd.DefaultIfEmpty()
                       join gr in db.GrainMasters on sop.GrainId equals gr.GrainId into grn
                       from gr in grn.DefaultIfEmpty()
                       join cl in db.ColorMasters on sop.ColorId equals cl.ColorId into cln
                       from cl in cln.DefaultIfEmpty()
                       join tk in db.ThicknessMasters on sop.Thick equals tk.ThicknessId into tkn
                       from tk in tkn.DefaultIfEmpty()
                       join wth in db.WidthMasters on sop.Width equals wth.WidthId into wthn
                       from wth in wthn.DefaultIfEmpty()
                       join cust in db.CustomerMasters on so.CustomerId equals cust.CustomerId
                       join sm in db.StoreMasters on sop.SaleOrderStoreId equals sm.StoreId into smd
                       from sm in smd.DefaultIfEmpty()
                       where stsList.Contains((ESalesOrderStatus)so.Status) && wp.Disabled != true
                        && (((string.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "inspectiondate") && (filters.FromInspectionDate == null || isfcm.AddedDate.Value >= filters.FromInspectionDate)
                        && (string.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "inspectiondate") && (filters.ToInspectionDate == null || isfcm.AddedDate <= filters.ToInspectionDate))
                        || ((string.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "productiondate") && (filters.FromProductionDate == null || sot.AddedDate >= filters.FromProductionDate)
                        && (string.IsNullOrEmpty(filters.DateType) || filters.DateType.ToLowerInvariant() == "productiondate") && (filters.ToProductionDate == null || sot.AddedDate <= filters.ToProductionDate)))

                        && (string.IsNullOrEmpty(filters.ProductType) || fc.SaleFormulationCode.ToLower().StartsWith(filters.ProductType.ToLower()))
                        && (filters.SaleFormulationCodeId == 0 || filters.SaleFormulationCodeId == null || so.SaleFormulationCodeId == filters.SaleFormulationCodeId)
                        && (string.IsNullOrEmpty(filters.SaleOrderNumber) || so.SaleOrderNumber.Contains(filters.SaleOrderNumber))
                        && (string.IsNullOrEmpty(filters.ArticleName) || sop.ManufacturingProductName.ToLower().Contains(filters.ArticleName.ToLower()))
                        && (filters.ColorId == 0 || filters.ColorId == null || sop.ColorId == filters.ColorId)
                        && (filters.GrainId == 0 || filters.GrainId == null || sop.GrainId == filters.GrainId)
                        && (filters.CustomerId == 0 || filters.CustomerId == null || so.CustomerId == filters.CustomerId)
                        && (filters.ThicknessId == 0 || filters.ThicknessId == null || sop.Thick == filters.ThicknessId)
                       select new PendingConsumptionOrdersResponseVm
                       {
                           SaleOrderNo = so.SaleOrderNumber,
                           SaleOrderId = so.SaleOrderId,
                           WorkPlanNo = wp.WorkPlanNo,
                           WorkPlanId = wp.WorkPlanId,
                           OrderQty = sop.OrderQuantity,
                           MFDQTY = sop.ManufacturingQuantity,
                           CustomerName = cust.CustomerName,
                           FormulationCode = fc.SaleFormulationCode,
                           GrainCode = gr.GrainCode,
                           ColorName = cl.ColorName,
                           Thickness = tk.ThicknessNumber,
                           ArticleName = sop.ManufacturingProductName,
                           PreInspectionCompletedBy = isfcm.AddedBy,
                           PreInspectionCompletedDate = isfcm.AddedDate,
                           PreInspectionStore = sm.StoreName,
                           Status = (ESalesOrderStatus)so.Status,
                           ProductionCompletedBy = sopc != null ? sopc.Addedby : "Not Completed",
                           ProductionCompletedDate = sopc != null ? sopc.AddedDate : null,
                       }).Distinct().OrderByDescending(x => x.PreInspectionCompletedDate).ToList();

            if (!filters.ShowConsumedOrders)
            {
                res = (from a in res
                       join cspm in db.ConsumeStockProductMasters on a.SaleOrderId equals cspm.SaleOrderId into cspmd
                       from cspm in cspmd.DefaultIfEmpty()
                       where cspm == null
                       select a).ToList();
            }
            else
            {
                res = (from a in res
                       join cspm in db.ConsumeStockProductMasters on a.SaleOrderId equals cspm.SaleOrderId into cspmd
                       from cspm in cspmd.DefaultIfEmpty()
                       where cspm != null
                       select a).Distinct().ToList();
            }
            // foreach (var item in res)
            // {
            //     item.ProductionCompletedBy = item.Status == ESalesOrderStatus.MoveToPostProcess
            //                 ? (from s in db.SaleOrderTimelineTables
            //                    where s.SaleOrderId == item.SaleOrderId && s.Status == (int)ESalesOrderStatus.MoveToPostProcess
            //                    select s.AddedBy).FirstOrDefault()
            //                  : (from s in db.SaleOrderTimelineTables
            //                     where s.SaleOrderId == item.SaleOrderId && s.Status == (int)ESalesOrderStatus.JumboInspection
            //                     select s.AddedBy).FirstOrDefault() ?? "Not Completed";
            //     item.ProductionCompletedDate = item.Status == ESalesOrderStatus.MoveToPostProcess
            //      ? (from s in db.SaleOrderTimelineTables
            //         where s.SaleOrderId == item.SaleOrderId && s.Status == (int)ESalesOrderStatus.MoveToPostProcess
            //         select s.AddedDate).FirstOrDefault()
            //      : (from s in db.SaleOrderTimelineTables
            //         where s.SaleOrderId == item.SaleOrderId && s.Status == (int)ESalesOrderStatus.JumboInspection
            //         select s.AddedDate).FirstOrDefault();

            //     if (item.ProductionCompletedDate == DateTime.MinValue)
            //     {
            //         item.ProductionCompletedDate = null;
            //     }
            // }
            if (filters.DateType.ToLower() == "productiondate")
            {
                res = res.Where(x => x.ProductionCompletedDate != null).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddUpdateConsumeStockProduct(List<ConsumeStockProductMasterVm> brlist)
        {
            using var db = new Models.pmsdbContext();
            using var transaction = db.Database.BeginTransaction();
            try
            {

                var addedby = brlist[0].AddedBy;
                foreach (var br in brlist)
                {
                    ConsumeStockProductMaster res = new ConsumeStockProductMaster();
                    {
                        res.RackId = br.RackId;
                        res.StoreId = br.StoreId;
                        res.ProductId = br.ProductId;
                        res.Quantity = br.Quantity;
                        res.Scquantity = br.SCQuantity;
                        res.Unit = br.Unit;
                        res.ConsumedDate = System.DateTime.Now;
                        res.SaleOrderId = br.SaleOrderId;
                        res.AddedBy = addedby;
                        res.AddedDate = System.DateTime.Now;
                        res.StockId = br.StockId;
                        res.StockProductId = br.StockProductId;
                        res.Purpose = br.Purpose;
                        res.IsDamaged = br.IsDamaged;
                        res.MaterialCategory = br.MixingName;
                        db.ConsumeStockProductMasters.Add(res);
                    }
                    db.SaveChanges();

                    if (brlist[0].SaleOrderId != null)
                    {
                        res.Purpose = "Sale Order No: " + db.SaleOrderTables.FirstOrDefault(x => x.SaleOrderId == brlist[0].SaleOrderId).SaleOrderNumber;
                    }

                    if (br.IsNewAdd == true && br.SaleOrderId != null)
                    {
                        var saleOrderProductionId = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == br.SaleOrderId).SaleOrderProductionId;
                        var categoryCheck = (from s in db.InspectionFormulationCodeMixingTables
                                             join mix in db.MixingMasters on s.MixingId equals mix.MixingId
                                             where s.SaleOrderId == br.SaleOrderId && mix.MixingName == br.MixingName
                                             select s).FirstOrDefault();
                        if (br.MixingName != null && categoryCheck == null)
                        {
                            SaleOrderProductionMiscellaneousRawMaterialTable spt = new SaleOrderProductionMiscellaneousRawMaterialTable();
                            spt.SaleOrderProductionId = saleOrderProductionId;
                            spt.ProductId = br.ProductId;
                            spt.Quantity = br.Quantity;
                            spt.MaterialCategory = br.MixingName;
                            spt.Unit = br.Unit;
                            spt.AddedBy = addedby;
                            spt.AddedDate = System.DateTime.Now;
                            db.SaleOrderProductionMiscellaneousRawMaterialTables.Add(spt);
                        }
                        else
                        {
                            if (br.MixingName == "Lacquer")
                            {
                                SaleOrderProductionLacquerRawMaterialTable spt = new SaleOrderProductionLacquerRawMaterialTable();
                                spt.SaleOrderProductionId = saleOrderProductionId;
                                //spt.LacquerMasterId = item;
                                spt.ProductId = br.ProductId;
                                spt.Quantity = br.Quantity;
                                spt.Unit = br.Unit;
                                db.SaleOrderProductionLacquerRawMaterialTables.Add(spt);
                            }
                            else
                            {
                                var forcode = (from s in db.InspectionFormulationCodeMixingTables
                                               join mix in db.MixingMasters on s.MixingId equals mix.MixingId
                                               where s.SaleOrderId == br.SaleOrderId && mix.MixingName == br.MixingName
                                               select s).FirstOrDefault().FormulationCodeMixingId;
                                InspectionFormulationCodeMixingRawMaterialTable spt = new InspectionFormulationCodeMixingRawMaterialTable
                                {
                                    ProductId = br.ProductId,
                                    Quantity = br.Quantity,
                                    Unit = br.Unit,
                                    Scquantity = br.SCQuantity,
                                    FormulationCodeMixingId = forcode
                                };
                                db.InspectionFormulationCodeMixingRawMaterialTables.Add(spt);
                            }
                        }
                    }

                    // Get stock allocation and update quantity
                    var stockAllocation = db.StockProductAllocationTables
                        .FirstOrDefault(x => x.StockProductId == br.StockProductId && x.RackId == br.RackId);
                    if (stockAllocation == null)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, $"Stock allocation not found for stock product ID: {br.StockProductId} and rack ID: {br.RackId}");
                    }
                    if (stockAllocation.Quantity < br.Quantity)
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, $"Requested quantity {br.Quantity} exceeds available stock allocation quantity {stockAllocation.Quantity}");
                    }
                    stockAllocation.Quantity -= br.Quantity;
                    db.SaveChanges();

                    if (br.StockLabelId != null && br.StockLabelId != 0)
                    {
                        var stockLabel = db.StockLabelTables
                            .FirstOrDefault(l => l.StockLabelId == br.StockLabelId);

                        if (stockLabel != null)
                        {
                            if (br.Quantity > stockLabel.Quantity)
                            {
                                return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, $"Requested quantity {br.Quantity} exceeds available label quantity {stockLabel.Quantity}");
                            }

                            var stockDataFn = new StockDataFn(new GlobalDataEntity());
                            stockLabel.Quantity -= br.Quantity;

                            stockDataFn.UpdateStockLabelStatus(db, stockLabel.StockLabelId,
                                StockLabelStatus.Consumed,
                                $"Consumed quantity: {br.Quantity} {stockLabel.PackagingUnit} for {res.Purpose}",
                                res.ConsumeStockProductId,
                                "ConsumeStockProductMaster");

                            if (stockLabel.Quantity <= 0)
                            {
                                stockLabel.IsActive = false;
                                stockLabel.LabelStatus = StockLabelStatus.InActive;
                            }
                        }
                        else
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.BadRequest, $"Stock label not found or inactive: {br.StockLabelId}");
                        }
                    }
                    else
                    {
                        // Check and update labels if they exist
                        var stockLabels = db.StockLabelTables
                            .Where(l => l.StockProductId == br.StockProductId
                                   && l.AllocationId == stockAllocation.AllocationId
                                   && l.IsActive == true)
                            .OrderBy(l => l.SerialNo)
                            .ToList();

                        if (stockLabels.Any())
                        {
                            decimal remainingQuantity = br.Quantity;
                            var stockDataFn = new StockDataFn(new GlobalDataEntity());

                            foreach (var label in stockLabels)
                            {
                                if (remainingQuantity <= 0) break;

                                var deductQuantity = Math.Min(label.Quantity ?? 0, remainingQuantity);
                                label.Quantity -= deductQuantity;
                                remainingQuantity -= deductQuantity;

                                stockDataFn.UpdateStockLabelStatus(db, label.StockLabelId,
                                    StockLabelStatus.Consumed,
                                    $"Consumed quantity: {deductQuantity} {br.Unit} for {res.Purpose}",
                                    res.ConsumeStockProductId,
                                    "ConsumeStockProductMaster");

                                if (label.Quantity <= 0)
                                {
                                    label.IsActive = false;
                                    label.LabelStatus = StockLabelStatus.InActive;
                                }
                            }
                        }
                    }
                }
                if (brlist[0].SaleOrderId != null)
                {
                    var recdata = db.WorkPlanJumboMasters.Where(x => x.SaleOrderId == brlist[0].SaleOrderId).ToList();
                    var sop = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == brlist[0].SaleOrderId);
                    var PackagingCategoryItemCount = brlist.Where(x => x.MixingName == "Packaging").Count();
                    if (sop.CostingStatus != CostingStatus.Submitted)
                    {
                        if (recdata != null)
                        {
                            if (!recdata.Any(x => x.IsInspectionCompleted != true) && PackagingCategoryItemCount > 0)
                            {
                                sop.CostingStatus = CostingStatus.Ready;
                                db.SaveChanges();
                            }
                            else
                            {
                                sop.CostingStatus = CostingStatus.PartialReady;
                                db.SaveChanges();
                            }
                        }
                        else
                        {
                            sop.CostingStatus = CostingStatus.PartialReady;
                            db.SaveChanges();
                        }
                    }
                }
                db.SaveChanges();
                transaction.Commit();

                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Consumption Saved Successfully");
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occured. Please contact administrator. " + ex);
            }
        }

        public List<IssueSaleOrderProductsStockVm> GetAllSaleOrderProductsToConsume(long saleorderId)
        {
            List<IssueSaleOrderProductsStockVm> data = null;
            using (var db = new Models.pmsdbContext())
            {
                // var res = (from op in db.InspectionFormulationCodeMixingRawMaterialTables
                //            join s in db.InspectionFormulationCodeMixingTables on op.FormulationCodeMixingId equals s.FormulationCodeMixingId
                //            join mix in db.MixingMasters on s.MixingId equals mix.MixingId
                //            join so in db.SaleOrderTables on s.SaleOrderId equals so.SaleOrderId
                //            join p in db.ProductMasters on op.ProductId equals p.ProductId
                //            where s.SaleOrderId == saleorderId
                //            select new InspectionFormulationCodeMixingRawMaterialTableVm
                //            {
                //                ProductId = op.ProductId,
                //                ProductName = p.ProductName,
                //                ProductCode = p.ProductCode,
                //                Quantity = op.Quantity,
                //                Scquantity = op.Scquantity,
                //                BaseQuantity = op.BaseQuantity,
                //                IsBaseMaterial = op.IsBaseMaterial,
                //                AvgGsm = p.AvgGsm,
                //                Unit = op.Unit,
                //                Price = op.Price,
                //                MixingName = mix.MixingName
                //            }).ToList();
                var FabricObject = new InspectionFormulationCodeMixingRawMaterialTableVm();
                var mixingData = from s in db.InspectionFormulationCodeMixingTables
                                 join spc in db.SaleOrderProductionCompleteTables on s.SaleOrderId equals spc.SaleOrderId
                                 join mix in db.MixingMasters on s.MixingId equals mix.MixingId
                                 where s.SaleOrderId == saleorderId
                                 select new
                                 {
                                     s.FormulationCodeMixingId,
                                     mix.MixingName,
                                     mix.MixingId,
                                     spc.PreSkinActualPasteQty,
                                     spc.SkinActualPasteQty,
                                     spc.FoamActualPasteQty,
                                     spc.AdhesiveActualPasteQty
                                 };
                var rawMaterialData = from op in db.InspectionFormulationCodeMixingRawMaterialTables
                                      join ifc in db.InspectionFormulationCodeMixingTables on op.FormulationCodeMixingId equals ifc.FormulationCodeMixingId
                                      join mix in db.MixingMasters on ifc.MixingId equals mix.MixingId
                                      join p in db.ProductMasters on op.ProductId equals p.ProductId
                                      select new
                                      {
                                          op.FormulationCodeMixingId,
                                          op.ProductId,
                                          p.ProductName,
                                          p.ProductCode,
                                          op.Quantity,
                                          op.Scquantity,
                                          op.BaseQuantity,
                                          op.IsBaseMaterial,
                                          p.AvgGsm,
                                          op.Unit,
                                          op.Price,
                                          mix.MixingName
                                      };
                var inscode = db.InspectionFormulationCodeMixingTables.Where(s => s.SaleOrderId == saleorderId).FirstOrDefault().InspectionSaleFormulationCodeId;
                var SaleFormulationCode = db.InspectionSaleFormulationCodeMasters.Where(x => x.InspectionSaleFormulationCodeId == inscode).FirstOrDefault();
                if (SaleFormulationCode != null)
                {
                    var FabPro = db.ProductMasters.Where(x => x.ProductId == SaleFormulationCode.FabricProductId).FirstOrDefault();
                    if (FabPro != null)
                    {
                        FabricObject = new InspectionFormulationCodeMixingRawMaterialTableVm
                        {
                            ProductId = FabPro.ProductId,
                            ProductName = FabPro.ProductName,
                            ProductCode = FabPro.ProductCode,
                            Quantity = SaleFormulationCode.FabricProductQty,
                            Unit = FabPro.Unit,
                            AvgGsm = FabPro.AvgGsm,
                            MixingName = "Fabric"
                        };
                        //res.Add(FabricObject);
                    }
                }
                var res1 = (from m in mixingData
                            select new InspectionFormulationCodeMixingTableVm
                            {
                                MixingName = m.MixingName,
                                MixingId = m.MixingId,
                                PreSkinGsmPasteReq = m.PreSkinActualPasteQty,
                                SkinGsmPasteReq = m.SkinActualPasteQty,
                                FoamGsmPasteReq = m.FoamActualPasteQty,
                                AdhesiveGsmPasteReq = m.AdhesiveActualPasteQty,
                                MixingRawMaterial = rawMaterialData
                                                       .Where(r => r.FormulationCodeMixingId == m.FormulationCodeMixingId)
                                                       .Select(r => new InspectionFormulationCodeMixingRawMaterialTableVm
                                                       {
                                                           ProductId = r.ProductId,
                                                           ProductName = r.ProductName,
                                                           ProductCode = r.ProductCode,
                                                           Quantity = r.Quantity,
                                                           Scquantity = r.Scquantity,
                                                           BaseQuantity = r.BaseQuantity,
                                                           IsBaseMaterial = r.IsBaseMaterial,
                                                           AvgGsm = r.AvgGsm,
                                                           Unit = r.Unit,
                                                           Price = r.Price,
                                                           MixingName = r.MixingName
                                                       }).ToList()
                            }).ToList();
                if (SaleFormulationCode != null)
                {
                    var FabPro = db.ProductMasters.Where(x => x.ProductId == SaleFormulationCode.FabricProductId).FirstOrDefault();
                    if (FabPro != null)
                    {
                        var MixingDataFabric = new List<InspectionFormulationCodeMixingRawMaterialTableVm>
                        {
                            new() {
                                ProductId = FabricObject.ProductId,
                                ProductName = FabricObject.ProductName,
                                ProductCode = FabricObject.ProductCode,
                                Quantity = FabricObject.Quantity,
                                AvgGsm = FabricObject.AvgGsm,
                                Unit = FabricObject.Unit,
                                MixingName = FabricObject.MixingName
                            }
                        };
                        res1.Add(new InspectionFormulationCodeMixingTableVm
                        {
                            MixingName = "Fabric",
                            MixingRawMaterial = MixingDataFabric
                        });
                    }
                }

                var productlist = res1.SelectMany(item => item.MixingRawMaterial.Select(x => x.ProductId).ToList());
                var tostoreid = db.SaleOrderProductionTables.FirstOrDefault(x => x.SaleOrderId == saleorderId).SaleOrderStoreId;
                data = (from a in db.StockProductTables
                        join stm in db.StockMasters on a.StockId equals stm.StockId into fcms
                        from stm in fcms.DefaultIfEmpty()
                        join inv in db.InvoiceMasters on stm.InvoiceId equals inv.InvoiceId into fcmsin
                        from inv in fcmsin.DefaultIfEmpty()
                        join sup in db.SupplierMasters on inv.SupplierId equals sup.SupplierId into fcmsup
                        from sup in fcmsup.DefaultIfEmpty()
                        join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                        join sl in db.StockLabelTables on b.StockProductId equals sl.StockProductId into sld
                        from sl in sld.DefaultIfEmpty()
                        join p in db.ProductMasters on a.ProductId equals p.ProductId
                        join r in db.RackMasters on b.RackId equals r.RackId
                        join s in db.StoreMasters on r.StoreId equals s.StoreId
                        where productlist.Contains(a.ProductId) && b.InspectionType == "Accepted" && (sl == null || (sl.IsActive == true && sl.CurrentStoreId == tostoreid))
                        select new IssueSaleOrderProductsStockVm
                        {
                            ProductId = a.ProductId,
                            ProductName = p.ProductName,
                            ProductCode = p.ProductCode,
                            ProductType = p.ProductType,
                            SKU = a.Sku,
                            BarCode = sl != null ? sl.SerialNo : a.Barcode,
                            StockLabelId = sl != null ? sl.StockLabelId : 0,
                            ShortCode = sl != null ? sl.ShortCode : null,
                            Unit = a.Unit,
                            PerUnitPrice = a.PricePerUnit,
                            Batch = stm.Batch,
                            ManufacturedDate = a.ManufacturedDate,
                            ExpiryDate = a.ExpiryDate,
                            StockId = a.StockId,
                            StockProductId = a.StockProductId,
                            SupplierId = inv.SupplierId,
                            SupplierName = sup.SupplierName,
                            InvoiceId = stm.InvoiceId,
                            InvoiceNumber = inv.InvoiceNumber,
                            StoreId = r.StoreId,
                            StoreName = s.StoreName,
                            StoreCode = s.StoreCode,
                            RackId = b.RackId,
                            RackName = r.RackName,
                            RackCode = r.RackCode,
                            Quantity = sl != null ? sl.Quantity : b.Quantity,
                            StockDate = stm.StockDate,
                        }).ToList();
                data = data.Where(x => x.StoreId == tostoreid).ToList();
                //foreach (var item in data)
                //{
                //    item.SaleOrderQuantity = res.FirstOrDefault(x => x.ProductId == item.ProductId).Quantity;
                //}
                var newdata = (from r in res1.SelectMany(x => x.MixingRawMaterial)
                               join a in data on r.ProductId equals a.ProductId
                               select new IssueSaleOrderProductsStockVm
                               {
                                   ProductId = a.ProductId,
                                   ProductName = a.ProductName,
                                   ProductCode = a.ProductCode,
                                   ProductType = a.ProductType,
                                   SKU = a.SKU,
                                   BarCode = a.BarCode,
                                   StockLabelId = a.StockLabelId,
                                   ShortCode = a.ShortCode,
                                   Unit = a.Unit,
                                   PerUnitPrice = a.PerUnitPrice,
                                   Batch = a.Batch,
                                   ManufacturedDate = a.ManufacturedDate,
                                   ExpiryDate = a.ExpiryDate,
                                   StockId = a.StockId,
                                   StockProductId = a.StockProductId,
                                   SupplierId = a.SupplierId,
                                   SupplierName = a.SupplierName,
                                   InvoiceId = a.InvoiceId,
                                   InvoiceNumber = a.InvoiceNumber,
                                   StoreId = a.StoreId,
                                   StoreName = a.StoreName,
                                   StoreCode = a.StoreCode,
                                   RackId = a.RackId,
                                   RackName = a.RackName,
                                   RackCode = a.RackCode,
                                   Quantity = a.Quantity,
                                   StockDate = a.StockDate,
                                   SaleOrderQuantity = r.Quantity,
                                   MixingName = r.MixingName
                               }).ToList();

                var sopc = db.SaleOrderProductionCompleteTables.FirstOrDefault(x => x.SaleOrderId == saleorderId);
                if (sopc != null)
                {
                    var fcdata = new CalculationsDataFn();
                    // decimal? fabricWidth = 0;
                    List<InspectionFormulationCodeMixingTableVm> calculateddata = fcdata.GetFormulationProductCalculateMaterialsQuantity(res1, saleorderId, sopc.ManufacturedQuantity);
                    // var fabric = newdata.Where(x => x.MixingName.ToLower() == "fabric").FirstOrDefault();
                    // if (fabric != null)
                    // {
                    //     var pro = db.ProductMasters.Where(x => x.ProductId == fabric.ProductId).FirstOrDefault();
                    //     fabricWidth = pro != null ? pro.WidthInMeter : 0;
                    // }


                    foreach (var item in newdata)
                    {
                        var calculatedItem = calculateddata.FirstOrDefault(x => x.MixingName.Trim().ToLower() == item.MixingName.Trim().ToLower());
                        if (calculatedItem != null)
                        {
                            var calculatedRaw = calculatedItem.MixingRawMaterial.FirstOrDefault(raw => raw.ProductId == item.ProductId);

                            if (calculatedRaw != null)
                            {
                                switch (item.MixingName.Trim().ToLower())
                                {
                                    case "pre skin":
                                        if (sopc.PreSkinGsm != null && sopc.PreSkinGsm > 0)
                                        {
                                            item.ActualQuantity = calculatedRaw.CalculatedQuantity == null ? 0 : (calculatedRaw.CalculatedQuantity <= 0 ? 0 : decimal.Round(calculatedRaw.CalculatedQuantity.Value, 2));
                                            item.ActualQuantity = item.ActualQuantity == 0 ? decimal.Parse("0.01") : item.ActualQuantity;
                                        }
                                        else
                                        {
                                            item.ActualQuantity = 0;
                                        }
                                        break;
                                    case "skin":
                                        if (sopc.SkinGsm != null && sopc.SkinGsm > 0)
                                        {
                                            item.ActualQuantity = calculatedRaw.CalculatedQuantity == null ? 0 : (calculatedRaw.CalculatedQuantity <= 0 ? decimal.Parse("0.01") : decimal.Round(calculatedRaw.CalculatedQuantity.Value, 2));
                                        }
                                        else
                                        {
                                            item.ActualQuantity = 0;
                                        }
                                        break;
                                    case "foam":
                                        if (sopc.FoamGsm != null && sopc.FoamGsm > 0)
                                        {
                                            item.ActualQuantity = calculatedRaw.CalculatedQuantity == null ? 0 : (calculatedRaw.CalculatedQuantity <= 0 ? decimal.Parse("0.01") : decimal.Round(calculatedRaw.CalculatedQuantity.Value, 2));
                                        }
                                        else
                                        {
                                            item.ActualQuantity = 0;
                                        }
                                        break;
                                    case "adhesive":
                                        if (sopc.AdhesiveGsm != null && sopc.AdhesiveGsm > 0)
                                        {
                                            item.ActualQuantity = calculatedRaw.CalculatedQuantity == null ? 0 : (calculatedRaw.CalculatedQuantity <= 0 ? decimal.Parse("0.01") : decimal.Round(calculatedRaw.CalculatedQuantity.Value, 2));
                                        }
                                        else
                                        {
                                            item.ActualQuantity = 0;
                                        }
                                        break;
                                    case "fabric":
                                        if (calculatedRaw.AvgGsm != null && calculatedRaw.AvgGsm > 0)
                                        {
                                            item.ActualQuantity = calculatedRaw.CalculatedQuantity == null ? 0 : decimal.Round(calculatedRaw.CalculatedQuantity.Value, 2);
                                        }
                                        else
                                        {
                                            item.ActualQuantity = 0;
                                        }


                                        break;
                                }
                            }
                        }
                    }
                }
                //To show consumed QTY if consumption needs to be done in multiple attempts
                // var csp = db.ConsumeStockProductMasters.FirstOrDefault(x => x.SaleOrderId == saleorderId);
                // if (csp != null)
                // {
                //     foreach (var item in newdata)
                //     {
                //         var cspItem = db.ConsumeStockProductMasters.FirstOrDefault(x => x.ProductId == item.ProductId && x.StockProductId == item.StockProductId && x.StockId == item.StockId && x.SaleOrderId == saleorderId);
                //         if (cspItem != null)
                //         {
                //             item.ConsumedQty = cspItem.Quantity;
                //         }
                //     }
                // }

                //var tostoreid = db.IssueProductTables.FirstOrDefault(x => x.SaleOrderId == saleorderid).ToStore;
                //data = (from inv in db.IssueProductTables
                //        join stm in db.StockMasters on inv.ToNewStockId equals stm.StockId
                //        join a in db.StockProductTables on inv.ToNewStockProductId equals a.StockProductId
                //        join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                //        join invoice in db.InvoiceMasters on stm.InvoiceId equals invoice.InvoiceId into fcmsin
                //        from invoice in fcmsin.DefaultIfEmpty()
                //        join sup in db.SupplierMasters on invoice.SupplierId equals sup.SupplierId into fcmsup
                //        from sup in fcmsup.DefaultIfEmpty()
                //        join p in db.ProductMasters on a.ProductId equals p.ProductId
                //        join r in db.RackMasters on b.RackId equals r.RackId
                //        join s in db.StoreMasters on r.StoreId equals s.StoreId
                //        where b.InspectionType == "Accepted" && inv.SaleOrderId == saleorderid && inv.Status == PmsCommon.PMSStatus.Approved
                //        select new IssueSaleOrderProductsStockVm
                //        {
                //            ProductId = a.ProductId,
                //            ProductName = p.ProductName,
                //            ProductCode = p.ProductCode,
                //            ProductType = p.ProductType,
                //            SKU = a.Sku,
                //            BarCode = a.Barcode,
                //            Unit = a.Unit,
                //            PerUnitPrice = a.PricePerUnit,
                //            ManufacturedDate = a.ManufacturedDate,
                //            ExpiryDate = a.ExpiryDate,
                //            StockId = a.StockId,
                //            StockProductId = a.StockProductId,
                //            SupplierId = invoice.SupplierId,
                //            SupplierName = sup.SupplierName,
                //            InvoiceId = stm.InvoiceId,
                //            InvoiceNumber = invoice.InvoiceNumber,
                //            StoreId = r.StoreId,
                //            StoreName = s.StoreName,
                //            StoreCode = s.StoreCode,
                //            RackId = b.RackId,
                //            RackName = r.RackName,
                //            RackCode = r.RackCode,
                //            Quantity = b.Quantity,
                //            StockDate = stm.StockDate,
                //            Batch = stm.Batch
                //        }).ToList();

                //data = data.Where(x => x.StoreId == tostoreid).ToList();
                // var consumedrecs = db.ConsumeStockProductMasters.Where(x => x.SaleOrderId == saleorderId).ToList();
                // foreach (var item in consumedrecs)
                // {
                //     newdata.Where(x => x.ProductId == item.ProductId).FirstOrDefault().ConsumedQty = item.Quantity;
                //     newdata.Where(x => x.ProductId == item.ProductId).FirstOrDefault().ConsumedScQty = item.Scquantity;
                //     newdata.Where(x => x.ProductId == item.ProductId).FirstOrDefault().ConsumedBy = item.AddedBy;
                //     newdata.Where(x => x.ProductId == item.ProductId).FirstOrDefault().ConsumedDate = item.ConsumedDate;

                // var productsToUpdate = newdata.Where(w => w.ProductId == item.ProductId).ToList();

                // foreach (var productToUpdate in productsToUpdate)
                // {
                //     productToUpdate.ConsumedQty = item.Quantity;
                //     productToUpdate.ConsumedScQty = item.Scquantity;
                //     productToUpdate.IsDamaged = item.IsDamaged;
                //     productToUpdate.ConsumedBy = item.AddedBy;
                //     productToUpdate.ConsumedDate = item.AddedDate;
                // }

                // }
                return newdata;
            }
        }
        public List<ConsumedSaleOrderProductsStockVm> GetAllSaleOrderConsumedProducts(long saleorderId)
        {
            using (var db = new pmsdbContext())
            {
                var newdata = (from cspm in db.ConsumeStockProductMasters
                               join spt in db.StockProductTables on cspm.StockProductId equals spt.StockProductId
                               join stm in db.StockMasters on spt.StockId equals stm.StockId
                               join a in db.ProductMasters on cspm.ProductId equals a.ProductId
                               join sm in db.StoreMasters on cspm.StoreId equals sm.StoreId
                               join rm in db.RackMasters on cspm.RackId equals rm.RackId
                               where cspm.SaleOrderId == saleorderId
                               select new ConsumedSaleOrderProductsStockVm
                               {
                                   ProductId = a.ProductId,
                                   ProductName = a.ProductName,
                                   ProductCode = a.ProductCode,
                                   ProductType = a.ProductType,
                                   Unit = a.Unit,
                                   Batch = stm.Batch,
                                   StockId = cspm.StockId,
                                   StockProductId = cspm.StockProductId,
                                   StoreId = cspm.StoreId,
                                   StoreName = sm.StoreName,
                                   RackId = cspm.RackId,
                                   RackName = rm.RackName,
                                   MaterialCategory = cspm.MaterialCategory,
                                   IsDamaged = cspm.IsDamaged,
                                   Damaged = cspm.IsDamaged == true ? "Yes" : "No",
                                   ConsumedQty = cspm.Quantity,
                                   ConsumedScQty = cspm.Scquantity,
                                   ConsumedBy = cspm.AddedBy,
                                   ConsumedDate = cspm.ConsumedDate
                               }).Distinct().ToList();

                return newdata;
            }
        }
        public List<IssueSaleOrderProductsStockVm> GetAllProductsToConsumeByStoreID(long storeId)
        {
            List<IssueSaleOrderProductsStockVm> data = null;
            using (var db = new Models.pmsdbContext())
            {
                data = (from stm in db.StockMasters
                        join a in db.StockProductTables on stm.StockId equals a.StockId
                        join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                        join sl in db.StockLabelTables on b.StockProductId equals sl.StockProductId into sld
                        from sl in sld.DefaultIfEmpty()
                        join invoice in db.InvoiceMasters on stm.InvoiceId equals invoice.InvoiceId into fcmsin
                        from invoice in fcmsin.DefaultIfEmpty()
                        join sup in db.SupplierMasters on invoice.SupplierId equals sup.SupplierId into fcmsup
                        from sup in fcmsup.DefaultIfEmpty()
                        join p in db.ProductMasters on a.ProductId equals p.ProductId
                        join r in db.RackMasters on b.RackId equals r.RackId
                        join s in db.StoreMasters on r.StoreId equals s.StoreId
                        where b.InspectionType == "Accepted" && r.StoreId == storeId
                        select new IssueSaleOrderProductsStockVm
                        {
                            ProductId = a.ProductId,
                            ProductName = p.ProductName,
                            ProductCode = p.ProductCode,
                            ProductType = p.ProductType,
                            SKU = a.Sku,
                            BarCode = sl != null ? sl.SerialNo : a.Barcode,
                            StockLabelId = sl != null ? sl.StockLabelId : 0,
                            ShortCode = sl != null ? sl.ShortCode : null,
                            Unit = a.Unit,
                            PerUnitPrice = a.PricePerUnit,
                            ManufacturedDate = a.ManufacturedDate,
                            ExpiryDate = a.ExpiryDate,
                            StockId = a.StockId,
                            StockProductId = a.StockProductId,
                            SupplierId = invoice.SupplierId,
                            SupplierName = sup.SupplierName,
                            InvoiceId = stm.InvoiceId,
                            InvoiceNumber = invoice.InvoiceNumber,
                            StoreId = r.StoreId,
                            StoreName = s.StoreName,
                            StoreCode = s.StoreCode,
                            RackId = b.RackId,
                            RackName = r.RackName,
                            RackCode = r.RackCode,
                            Quantity = sl != null ? sl.Quantity : b.Quantity,
                            StockDate = stm.StockDate,
                            Batch = stm.Batch
                        }).ToList();
            }
            return data;
        }
    }
}
