using System.Collections.Generic;
using System.Threading.Tasks;
using PmsCore.Notifications.Interfaces;
using System.Linq;
using PmsCommon;
using Microsoft.EntityFrameworkCore;
using PmsData.Models;
using PmsCore.Notifications.Models;
using Microsoft.Extensions.Logging;
using System;
using Cronos;
using PmsEntity.ViewModel;
using PmsCore.PDFGeneration.Interfaces;
using PmsData.Adapters;


namespace PmsData.DataFn
{
    public class NotificationDataFn : INotificationDataAccess
    {
        private readonly ILogger<NotificationDataFn> _logger;
        private readonly GlobalDataEntity _globalData;
        private readonly IPdfService _pdfService;
        private readonly pmsdbContext _dbContext;

        public NotificationDataFn(
            ILogger<NotificationDataFn> logger,
            GlobalDataEntity globalData,
            IPdfService pdfService,
            pmsdbContext dbContext)
        {
            _logger = logger;
            _globalData = globalData;
            _pdfService = pdfService;
            _dbContext = dbContext;
        }

        public async Task<List<NotificationRecipient>> GetReportRecipients(string notificationType, string TriggerType = null)
        {
            using (var db = new Models.pmsdbContext())
            {
                var recipients = db.NotificationGroupsTables
                    .Where(n => n.NotificationType == notificationType && n.Disabled != true)
                    .Where(n => TriggerType == null || n.TriggerType.Contains(TriggerType))
                    .Select(n => new NotificationRecipient
                    {
                        Id = n.NotificationGroupUserId,
                        Name = n.Name,
                        EmailId = n.Email,
                        MobileNumberString = n.MobileNumber,
                        EnableToEmail = n.EnableToEmail ?? false,
                        EnableCCEmail = n.EnableCcemail ?? false,
                        EnableBCCEmail = n.EnableBccemail ?? false,
                        IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                        UserType = n.UserType,
                        UserMasterId = n.UserMasterId,
                        NotificationType = n.NotificationType,
                        ReportType = n.TriggerType,
                        WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId ?? 0
                    })
                    .ToList();

                foreach (var recipient in recipients)
                {
                    recipient.MobileNumbers = !string.IsNullOrEmpty(recipient.MobileNumberString) ? recipient.MobileNumberString.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList() : new List<string>();
                }

                foreach (var recipient in recipients)
                {
                    if (recipient.UserType == "Internal")
                    {
                        var user = db.UserMasters.Where(u => u.UserId == recipient.UserMasterId.Value).FirstOrDefault();
                        recipient.Name = user.Name;
                        recipient.EmailId = user.Email;
                        recipient.MobileNumbers = user.Contact != null ? user.Contact.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                    }
                    else if (recipient.UserType == "Customer")
                    {
                        var customer = db.CustomerMasters.Where(c => c.CustomerId == recipient.UserMasterId.Value).FirstOrDefault();
                        recipient.Name = customer.CustomerName;
                        recipient.EmailId = customer.Email;
                        recipient.MobileNumbers = customer.CustomerContactNumber != null ? customer.CustomerContactNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>();
                    }
                }

                return recipients;
            }
        }

        public async Task<NotificationTemplate> GetNotificationTemplate(string notificationType, string reportType)
        {
            using var db = new Models.pmsdbContext();
            try
            {
                _logger.LogInformation("Searching template with type: {NotificationType}, report: {ReportType}", notificationType, reportType);

                var template = db.NotificationGroupsTables
                    .FirstOrDefault(t => t.NotificationType.ToLower() == notificationType.ToLower() && t.TriggerType.ToLower() == reportType.ToLower());

                if (template == null)
                {
                    _logger.LogWarning($"No template found for type: {notificationType} and report: {reportType}");
                    return null;
                }

                var whatsappTemplate = db.WhatsAppTemplateMasters
                    .FirstOrDefault(w => w.WhatsAppTemplateMasterId == template.WhatsAppTemplateMasterId);



                return template == null ? null : new NotificationTemplate
                {
                    NotificationType = template.NotificationType,
                    ReportType = template.TriggerType,
                    Subject = "Template Subject",
                    WhatsAppProviderTemplateId = whatsappTemplate.WhatsAppTemplateMasterId,
                    WhatsAppTemplateMasterId = whatsappTemplate.WhatsAppTemplateMasterId
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification template");
                return null;
            }
            // using (var db = new Models.pmsdbContext())
            // {
            //     // Implementation based on your report generation logic
            //     throw new NotImplementedException("Implement based on your report generation requirements");
            // }
        }
        public async Task<List<long>> GetRecipientTemplateIds(long recipientId, string notificationType)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get templates based on recipient's notification group mappings
                var templateIds = (from ng in db.NotificationGroupsTables
                                   join egm in db.EmailGroupMappingTables
                                       on ng.NotificationGroupUserId equals egm.NotificationGroupUserId
                                   join wt in db.WhatsAppTemplateMasters
                                       on ng.WhatsAppTemplateMasterId equals wt.WhatsAppTemplateMasterId
                                   where ng.NotificationGroupUserId == recipientId
                                       && !ng.Disabled.GetValueOrDefault()
                                       && egm.Enabled.GetValueOrDefault()
                                       && !wt.Disabled.GetValueOrDefault()
                                       && ng.NotificationType == notificationType
                                   select wt.WhatsAppTemplateMasterId)
                                       .ToList();

                return templateIds;
            }
        }
        public async Task<(NotificationTemplate template, Dictionary<string, string> parameters)> GetSaleOrderStatusNotification(long saleOrderId, long stageId)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Get sale order details
                var saleOrder = await (from s in db.SaleOrderTables
                                       join c in db.CustomerMasters
                                       on s.CustomerId equals c.CustomerId
                                       where s.SaleOrderId == saleOrderId
                                       select new { s, c })
                                       .FirstOrDefaultAsync();

                if (saleOrder == null)
                    throw new ArgumentException($"Sale order not found: {saleOrderId}");

                // Get stage notification configuration
                var stageConfig = await db.NotificationSaleOrderStagesTables
                    .Include(s => s.WhatsappTemplate)
                    .FirstOrDefaultAsync(s => s.StageId == stageId && !s.Disabled.GetValueOrDefault());

                if (stageConfig == null)
                    throw new ArgumentException($"Stage configuration not found: {stageId}");

                // Get notification template
                var template = new NotificationTemplate
                {
                    NotificationType = "SaleOrder",
                    ReportType = stageConfig.SaleOrderStages,
                    Subject = $"Sale Order {saleOrder.s.SaleOrderNumber} - {stageConfig.SaleOrderStages}",
                    WhatsAppProviderTemplateId = stageConfig.WhatsappTemplateId
                };

                // Build parameters
                var parameters = new Dictionary<string, string>
        {
            { "SaleOrderNumber", saleOrder.s.SaleOrderNumber },
            { "Stage", stageConfig.SaleOrderStages },
            { "CustomerName", saleOrder.c.CustomerName },
            { "CustomerCode", saleOrder.c.CustomerCode }
        };

                return (template, parameters);
            }
        }

        public async Task<List<NotificationRecipient>> GetStageNotificationRecipients(long stageId, long customerId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var stageConfig = await db.NotificationSaleOrderStagesTables
                    .FirstOrDefaultAsync(s => s.StageId == stageId && !s.Disabled.GetValueOrDefault());

                if (stageConfig == null)
                    return new List<NotificationRecipient>();

                var recipients = new List<NotificationRecipient>();

                // Add internal recipients if configured
                if (stageConfig.OnlyInternal ?? false || (!stageConfig.OnlyCustomer.GetValueOrDefault() && !stageConfig.OnlyInternal.GetValueOrDefault()))
                {
                    var internalRecipients = await db.NotificationGroupsTables
                        .Where(n => n.UserType == "Internal" && !n.Disabled.GetValueOrDefault())
                        .Select(n => new NotificationRecipient
                        {
                            Id = n.NotificationGroupUserId,
                            EmailId = n.Email,
                            MobileNumbers = n.MobileNumber != null ? n.MobileNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>(),
                            EnableToEmail = n.EnableToEmail ?? false,
                            IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                            UserType = "Internal",
                            UserMasterId = n.UserMasterId
                        })
                        .ToListAsync();

                    recipients.AddRange(internalRecipients);
                }

                // Add customer recipients if configured
                if (stageConfig.OnlyCustomer ?? false || (!stageConfig.OnlyCustomer.GetValueOrDefault() && !stageConfig.OnlyInternal.GetValueOrDefault()))
                {
                    var customer = await db.CustomerMasters
                        .FirstOrDefaultAsync(c => c.CustomerId == customerId);

                    var customerWhatsAppSubscription = await db.WhatsAppSubscriptionCustomerTables
                        .FirstOrDefaultAsync(w => w.CustomerId == customerId);

                    var customerEmailSubscription = await db.EmailSubscriptionCustomerTables
                        .FirstOrDefaultAsync(e => e.CustomerId == customerId);

                    if (customer != null)
                    {
                        var customerEmails = !string.IsNullOrEmpty(customer.Email)
                            ? customer.Email.Split(',').Select(e => e.Trim()).ToList()
                            : new List<string>();

                        var customerMobiles = !string.IsNullOrEmpty(customer.CustomerContactNumber)
                            ? customer.CustomerContactNumber.Split(',').Select(m => m.Trim()).ToList()
                            : new List<string>();



                        foreach (var email in customerEmails)
                        {
                            recipients.Add(new NotificationRecipient
                            {
                                Id = customer.CustomerId,
                                EmailId = email,
                                MobileNumbers = customerMobiles,
                                EnableToEmail = customerEmailSubscription.Enabled,
                                IsWhatsAppNotificationEnabled = customerWhatsAppSubscription.Enabled,
                                UserType = "External"
                            });
                        }
                    }
                }

                return recipients;
            }
        }

        public async Task<ReportData> GenerateReportData(string reportType)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Implementation based on your report generation logic
                throw new NotImplementedException("Implement based on your report generation requirements");
            }
        }

        public async Task UpdateNotificationStatus(string providerMessageId, string status, DateTime? deliveredTime, DateTime? readTime)
        {
            using (var db = new Models.pmsdbContext())
            {
                // Find all tracking records with this provider message ID
                var trackingRecords = await db.NotificationTrackingTables
                    .Where(t => t.ProviderMessageId == providerMessageId)
                    .ToListAsync();

                if (trackingRecords != null && trackingRecords.Any())
                {
                    foreach (var tracking in trackingRecords)
                    {
                        tracking.Status = status;
                        tracking.DeliveredTime = deliveredTime;
                        tracking.ReadTime = readTime;
                    }

                    await db.SaveChangesAsync();
                    _logger.LogInformation("Updated status to {Status} for {Count} notification records with provider message ID {ProviderMessageId}",
                        status, trackingRecords.Count, providerMessageId);
                }
                else
                {
                    _logger.LogWarning("No notification records found with provider message ID {ProviderMessageId}", providerMessageId);
                }
            }
        }

        public async Task SendEmailNotification()
        {
            using var db = new Models.pmsdbContext();
            try
            {
                var schedules = await db.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled)
                    .ToListAsync();

                foreach (var schedule in schedules)
                {
                    try
                    {
                        var cronExpression = CronExpression.Parse(schedule.CronExpression);
                        var timeZone = TimeZoneInfo.FindSystemTimeZoneById(schedule.TimeZone);
                        var utcNow = DateTime.UtcNow;

                        // Convert UTC to local timezone for cron calculation
                        var currentTimeInTimeZone = TimeZoneInfo.ConvertTime(utcNow, timeZone);

                        if (!schedule.NextRunTime.HasValue)
                        {
                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);
                            await db.SaveChangesAsync();
                            continue;
                        }

                        // Compare in UTC to avoid timezone conversion issues
                        if (utcNow >= schedule.NextRunTime)
                        {
                            // await _notificationService.SendScheduledReport(schedule.ReportType);

                            // Update the schedule - store times in UTC
                            schedule.LastRunTime = utcNow;

                            // Calculate next occurrence in the specified timezone, then convert back to UTC
                            var nextOccurrenceInTimeZone = cronExpression.GetNextOccurrence(
                                DateTime.SpecifyKind(currentTimeInTimeZone, DateTimeKind.Utc));
                            schedule.NextRunTime = TimeZoneInfo.ConvertTimeToUtc(
                                DateTime.SpecifyKind(nextOccurrenceInTimeZone.Value, DateTimeKind.Unspecified),
                                timeZone);
                            await db.SaveChangesAsync();

                            _logger.LogInformation(
                                "Sent scheduled report {ReportType}. Next run scheduled for {NextRunTime} UTC",
                                schedule.ReportType,
                                schedule.NextRunTime);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex,
                            "Failed to process schedule {ScheduleId} for report type {ReportType}",
                            schedule.ReportId,
                            schedule.ReportType);
                        continue;
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process notification schedules");
                throw;
            }
        }
        public async Task<(WhatsAppTemplate template, WhatsAppConfig config, WhatsAppSettings settings)> GetWhatsAppTemplateAndConfig(long? templateMasterId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var dbTemplate = db.WhatsAppTemplateMasters
                    .FirstOrDefault(t => t.WhatsAppTemplateMasterId == templateMasterId && t.Disabled != true);

                if (dbTemplate == null)
                    return (null, null, null);

                var dbConfig = db.WhatsAppConfigTables
                    .FirstOrDefault(c => c.ProviderName == dbTemplate.ProviderName && c.Disabled != true);

                if (dbConfig == null)
                    return (null, null, null);

                // Count the number of parameters for this template
                var parameterCount = await db.NotificationTemplateParameterTables
                    .Where(p => p.TemplateMasterId == dbTemplate.WhatsAppTemplateMasterId)
                    .CountAsync();

                return (
                    new WhatsAppTemplate
                    {
                        TemplateId = dbTemplate.WhatsAppTemplateMasterId,
                        ProviderTemplateId = dbTemplate.ProviderTemplateId,
                        ProviderName = dbTemplate.ProviderName,
                        ProviderTemplateName = dbTemplate.ProviderTemplateName,
                        Language = dbTemplate.Language,
                    },
                    new WhatsAppConfig
                    {
                        ProviderName = dbConfig.ProviderName,
                        ProviderKey = dbConfig.ProviderKey,
                        RegisteredSenderNumber = dbConfig.RegisteredSenderNumber,
                        ApiEndpoint = dbConfig.ApiEndpoint,
                    },
                    new WhatsAppSettings
                    {
                        ApiEndpoint = dbConfig.ApiEndpoint,
                        DefaultLanguageCode = dbTemplate.Language,
                    }
                );
            }
        }

        public async Task<WhatsAppConfig> GetDefaultWhatsAppConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var config = await db.WhatsAppConfigTables
                    .FirstOrDefaultAsync(c => c.ProviderName == "Brevo" && c.Disabled != true);

                return config == null ? null : new WhatsAppConfig
                {
                    ProviderName = config.ProviderName,
                    ProviderKey = config.ProviderKey,
                    RegisteredSenderNumber = config.RegisteredSenderNumber,
                    ApiEndpoint = config.ApiEndpoint,
                };
            }
        }
        public async Task<WhatsAppConfig> GetWhatsAppConfigByProviderName(string providerName)
        {
            using (var db = new Models.pmsdbContext())
            {
                var config = await db.WhatsAppConfigTables
                    .FirstOrDefaultAsync(c => c.ProviderName == providerName && c.Disabled != true);

                return config == null ? null : new WhatsAppConfig
                {
                    ProviderName = config.ProviderName,
                    ProviderKey = config.ProviderKey,
                    RegisteredSenderNumber = config.RegisteredSenderNumber,
                    ApiEndpoint = config.ApiEndpoint,
                };
            }
        }
        public async Task TrackNotification(NotificationTrackingModel tracking)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        // Generate a unique notification message ID if not provided
                        if (string.IsNullOrEmpty(tracking.NotificationMessageId))
                        {
                            tracking.NotificationMessageId = $"{tracking.NotificationType}-{Guid.NewGuid():N}";
                        }

                        var trackingEntity = new NotificationTrackingTable
                        {
                            NotificationType = tracking.NotificationType,
                            MessageType = tracking.MessageType,
                            RecipientId = tracking.RecipientId,
                            MessageContent = tracking.MessageContent,
                            MasterTemplateId = tracking.MasterTemplateId,
                            Status = tracking.Status,
                            ErrorMessage = tracking.ErrorMessage,
                            ProviderMessageId = tracking.ProviderMessageId,
                            SentTime = tracking.SentTime,
                            DeliveredTime = tracking.DeliveredTime,
                            ReadTime = tracking.ReadTime,
                            AddedBy = _globalData.loggedInUser,
                            AddedDate = DateTime.UtcNow,
                            NotificationGroupUserId = tracking.NotificationGroupUserId,
                            NotificationMessageId = tracking.NotificationMessageId,
                            RecipientMobileNumber = tracking.RecipientMobileNumber,
                            RecipientEmail = tracking.RecipientEmail
                        };

                        db.NotificationTrackingTables.Add(trackingEntity);
                        db.SaveChanges();
                        transaction.Commit();

                        _logger.LogInformation("Tracked notification with ID {NotificationMessageId} for recipient {RecipientId}",
                            tracking.NotificationMessageId, tracking.RecipientId);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error tracking notification for recipient {RecipientId}", tracking.RecipientId);
                        transaction.Rollback();
                        throw;
                    }
                }
            }
        }

        public async Task<bool> CheckRateLimit(string notificationType, long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rateLimit = await db.NotificationRateLimitTables
                    .FirstOrDefaultAsync(r => r.NotificationType == notificationType &&
                                            r.RecipientId == recipientId);

                if (rateLimit == null) return true;

                var today = DateTime.UtcNow.Date;
                var thisMonth = new DateTime(today.Year, today.Month, 1);

                if (rateLimit.LastUpdated.Date != today)
                {
                    rateLimit.MessagesSentToday = 0;
                }

                if (rateLimit.LastUpdated < thisMonth)
                {
                    rateLimit.MessagesSentThisMonth = 0;
                }

                var config = await GetNotificationLimits(notificationType);
                return rateLimit.MessagesSentToday < config.MaxDailyMessages &&
                       rateLimit.MessagesSentThisMonth < config.MaxMonthlyMessages;
            }
        }
        public async Task<NotificationLimitConfig> GetNotificationLimits(string notificationType)
        {
            using (var db = new Models.pmsdbContext())
            {
                if (notificationType.Equals("WhatsApp", StringComparison.OrdinalIgnoreCase))
                {
                    var whatsAppConfig = await db.WhatsAppConfigTables
                        .Where(w => !w.Disabled.GetValueOrDefault())
                        .FirstOrDefaultAsync();

                    return new NotificationLimitConfig
                    {
                        MaxDailyMessages = whatsAppConfig?.MaxDailyMessages ?? 1000,
                        MaxMonthlyMessages = whatsAppConfig?.MaxMonthlyMessages ?? 25000
                    };
                }
                else if (notificationType.Equals("Email", StringComparison.OrdinalIgnoreCase))
                {
                    var emailConfig = await db.EmailConfigTables
                        .Where(e => !e.Disabled.GetValueOrDefault())
                        .FirstOrDefaultAsync();

                    return new NotificationLimitConfig
                    {
                        MaxDailyMessages = emailConfig?.MaxDailyEmails ?? 1000,
                        MaxMonthlyMessages = emailConfig?.MaxDailyEmails * 30 ?? 30000
                    };
                }

                return new NotificationLimitConfig
                {
                    MaxDailyMessages = 1000,
                    MaxMonthlyMessages = 30000
                };
            }
        }

        public async Task UpdateRateLimit(string notificationType, long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        var rateLimit = await db.NotificationRateLimitTables
                            .FirstOrDefaultAsync(r => r.NotificationType == notificationType &&
                                                    r.RecipientId == recipientId);

                        var now = DateTime.UtcNow;

                        if (rateLimit == null)
                        {
                            rateLimit = new NotificationRateLimitTable
                            {
                                NotificationType = notificationType,
                                RecipientId = recipientId,
                                MessagesSentToday = 1,
                                MessagesSentThisMonth = 1,
                                LastMessageTime = now,
                                LastUpdated = now
                            };
                            db.NotificationRateLimitTables.Add(rateLimit);
                        }
                        else
                        {
                            var today = now.Date;
                            var thisMonth = new DateTime(today.Year, today.Month, 1);

                            if (rateLimit.LastUpdated.Date != today)
                            {
                                rateLimit.MessagesSentToday = 1;
                            }
                            else
                            {
                                rateLimit.MessagesSentToday++;
                            }

                            if (rateLimit.LastUpdated < thisMonth)
                            {
                                rateLimit.MessagesSentThisMonth = 1;
                            }
                            else
                            {
                                rateLimit.MessagesSentThisMonth++;
                            }

                            rateLimit.LastMessageTime = now;
                            rateLimit.LastUpdated = now;
                        }

                        await db.SaveChangesAsync();
                        await transaction.CommitAsync();
                    }
                    catch (Exception)
                    {
                        await transaction.RollbackAsync();
                        throw;
                    }
                }
            }
        }

        public async Task<NotificationRecipient> GetRecipientById(long recipientId)
        {
            using (var db = new Models.pmsdbContext())
            {
                var recipient = await db.NotificationGroupsTables.FindAsync(recipientId);

                if (recipient == null)
                    throw new ArgumentException($"Recipient not found: {recipientId}");

                return new NotificationRecipient
                {
                    Id = recipient.NotificationGroupUserId,
                    EmailId = recipient.Email,
                    MobileNumbers = recipient.MobileNumber != null ? recipient.MobileNumber.Split(new[] { ',' }, StringSplitOptions.None).ToList() : new List<string>(),
                };
            }
        }
        public async Task<Dictionary<string, string>> GenerateLowStockParameters(long productId, long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Create a mapping of property names to their values
                var productProperties = new Dictionary<string, string>
                {
                    { nameof(productStockAvailabilityVm.PRODUCTNAME), productStockAvailabilityVm.PRODUCTNAME },
                    { nameof(productStockAvailabilityVm.PRODUCTCATEGORY), productStockAvailabilityVm.PRODUCTCATEGORY },
                    { nameof(productStockAvailabilityVm.PRODUCTFIRSTCATEGORY), productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "NA" },
                    { nameof(productStockAvailabilityVm.PRODUCTSECSUBCATEGORY), productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "NA" },
                    { nameof(productStockAvailabilityVm.UNIT), productStockAvailabilityVm.UNIT },
                    { nameof(productStockAvailabilityVm.AVAILABLEQTY), productStockAvailabilityVm.AVAILABLEQTY.ToString() },
                    { nameof(productStockAvailabilityVm.MINIMUMQTY), productStockAvailabilityVm.MINIMUMQTY.ToString() },
                    { nameof(productStockAvailabilityVm.DOMESTICQTY), productStockAvailabilityVm.DOMESTICQTY.ToString() },
                    { nameof(productStockAvailabilityVm.IMPORTEDQTY), productStockAvailabilityVm.IMPORTEDQTY.ToString() }
                };

                // Map template parameters to their values
                foreach (var param in templateParams)
                {
                    string paramValue = productProperties.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    parameters.Add(param.ParameterName, paramValue);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning($"Required parameter {param.ParameterName} has no value for template {templateMasterId}");
                    }
                }

                return parameters;
            }
        }

        /// <summary>
        /// Enhanced method that returns NotificationParameterCollection for provider-specific handling
        /// Handles duplicate parameter names by processing each template parameter individually
        /// </summary>
        public PmsCore.Notifications.Models.NotificationParameterCollection GenerateLowStockParametersEnhanced(long templateMasterId, ProductStockAvailabilityVm productStockAvailabilityVm)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameterCollection = new PmsCore.Notifications.Models.NotificationParameterCollection();

                // Create a mapping of property names to their values
                var productProperties = new Dictionary<string, string>
                {
                    { "ProductName", productStockAvailabilityVm.PRODUCTNAME ?? "N/A" },
                    { "ProductCategory", productStockAvailabilityVm.PRODUCTCATEGORY ?? "N/A" },
                    { "ProductFirstSubCategory", productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "N/A" },
                    { "ProductSecSubCategory", productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "N/A" },
                    { "TotalQuantity", productStockAvailabilityVm.AVAILABLEQTY?.ToString() ?? "0" },
                    { "MinimumQuantity", productStockAvailabilityVm.MINIMUMQTY?.ToString() ?? "0" },
                    { "Unit", productStockAvailabilityVm.UNIT ?? "N/A" },
                    { "UNIT", productStockAvailabilityVm.UNIT ?? "N/A" }, // Handle both "Unit" and "UNIT" parameter names
                    { "PRODUCTNAME", productStockAvailabilityVm.PRODUCTNAME ?? "N/A" },
                    { "PRODUCTCATEGORY", productStockAvailabilityVm.PRODUCTCATEGORY ?? "N/A" },
                    { "PRODUCTFIRSTCATEGORY", productStockAvailabilityVm.PRODUCTFIRSTCATEGORY ?? "N/A" },
                    { "PRODUCTSECSUBCATEGORY", productStockAvailabilityVm.PRODUCTSECSUBCATEGORY ?? "N/A" },
                    { "AVAILABLEQTY", productStockAvailabilityVm.AVAILABLEQTY?.ToString() ?? "0" },
                    { "MINIMUMQTY", productStockAvailabilityVm.MINIMUMQTY?.ToString() ?? "0" }
                };

                // Process each template parameter individually (allows duplicates)
                foreach (var param in templateParams)
                {
                    // Get the parameter value based on parameter name
                    string paramValue = productProperties.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    // Create a notification parameter for each template parameter entry
                    // This allows the same parameter name to appear multiple times with different sequences
                    var notificationParam = new PmsCore.Notifications.Models.NotificationParameter
                    {
                        Name = param.ParameterName,
                        Value = paramValue,
                        Sequence = param.Sequence ?? int.MaxValue,
                        IsRequired = param.IsRequired ?? false,
                        DefaultValue = param.DefaultValue
                    };

                    // Add each parameter individually (no duplicate key issues)
                    parameterCollection.Add(notificationParam);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning("Required parameter {ParameterName} has no value for template {TemplateMasterId}", param.ParameterName, templateMasterId);
                    }
                }

                _logger.LogDebug("Generated {ParameterCount} parameters for template {TemplateMasterId}", parameterCollection.Count, templateMasterId);
                return parameterCollection;
            }
        }
        public async Task<ProductStockAvailabilityVm> GetProductStockAvailabilty(long productId)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var query = (from p in db.ProductMasters
                             join pr in db.ProductCategoryMasters on p.ProductCategoryId equals pr.ProductCategoryId
                             join pf in db.ProductFirstSubCategoryMasters on p.ProductFirstSubCategoryId equals pf.ProductFirstSubCategoryId into psf
                             from pf in psf.DefaultIfEmpty()
                             join psc in db.ProductSecSubCategoryMasters on p.ProductSecSubCategoryId equals psc.ProductSecSubCategoryId into pssc
                             from psc in pssc.DefaultIfEmpty()
                             join a in db.StockProductTables on p.ProductId equals a.ProductId
                             join b in db.StockProductAllocationTables on a.StockProductId equals b.StockProductId
                             join r in db.RackMasters on b.RackId equals r.RackId
                             join s in db.StoreMasters on r.StoreId equals s.StoreId
                             where productId == p.ProductId && p.Disabled != true && p.MinimumQuantity > 0
                                   && b.InspectionType == "Accepted" && b.Quantity > 0 && s.IsWorkInProgressStore == false
                             select new
                             {
                                 p.ProductId,
                                 p.ProductName,
                                 pr.ProductCategory,
                                 pf.ProductFirstSubCategory,
                                 psc.ProductSecSubCategory,
                                 b.Quantity,
                                 p.MinimumQuantity,
                                 a.Unit,
                                 a.StockProductId,
                                 a.StockId
                             }).ToList();

                if (!query.Any())
                    return null;

                var totalQuantity = query.Sum(x => x.Quantity);
                var minimumQuantity = query.First().MinimumQuantity;

                // Only return data if total quantity is less than minimum quantity
                if (totalQuantity >= minimumQuantity)
                    return null;

                var StockQuality = (from sm in db.StockMasters
                                    join sp in db.StockProductTables on sm.StockId equals sp.StockId
                                    join spa in db.StockProductAllocationTables on sp.StockProductId equals spa.StockProductId
                                    where query.Select(q => q.StockProductId).Contains(sp.StockProductId)
                                    group spa by new { sm.ProductQuality } into g
                                    select new
                                    {
                                        g.Key.ProductQuality,
                                        Quantity = g.Sum(x => x.Quantity)
                                    }).ToList();

                var DomesticQty = StockQuality.Where(x => x.ProductQuality == "DOMESTIC").Select(x => x.Quantity).FirstOrDefault() + StockQuality.Where(x => string.IsNullOrEmpty(x.ProductQuality)).Select(x => x.Quantity).FirstOrDefault();
                var ImportedQty = StockQuality.Where(x => x.ProductQuality == "IMPORTED").Select(x => x.Quantity).FirstOrDefault();

                return new ProductStockAvailabilityVm()
                {
                    ProductId = query.First().ProductId,
                    PRODUCTNAME = query.First().ProductName,
                    PRODUCTCATEGORY = query.First().ProductCategory,
                    PRODUCTFIRSTCATEGORY = query.First().ProductFirstSubCategory,
                    PRODUCTSECSUBCATEGORY = query.First().ProductSecSubCategory,
                    AVAILABLEQTY = totalQuantity,
                    MINIMUMQTY = minimumQuantity,
                    UNIT = query.First().Unit,
                    DOMESTICQTY = DomesticQty,
                    IMPORTEDQTY = ImportedQty
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting product stock availability for ProductId: {ProductId}", productId);
                return new ProductStockAvailabilityVm();
            }
        }
        public async Task<EmailSettings> GetPrimaryProviderEmailConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var emailConfig = await db.EmailConfigTables
                    .Where(e => !e.Disabled.GetValueOrDefault() && e.EmailConfigName == "Primary")
                    .FirstOrDefaultAsync();

                return emailConfig == null ? null : new EmailSettings
                {
                    ConfigName = emailConfig.EmailConfigName,
                    FromEmail = emailConfig.EmailConfigAccountId,
                    FromName = emailConfig.EmailConfigFromEmailDisplayName,
                    SmtpServer = emailConfig.EmailConfigSmtp,
                    AccessKey = emailConfig.EmailConfigFromEmailId,
                    SecretKey = emailConfig.EmailConfigPassword,
                    Region = "us-east-1",
                    Port = int.Parse(emailConfig.EmailConfigPort),
                    EnableSsl = emailConfig.EnableSsl.GetValueOrDefault(),
                    EnableRetry = emailConfig.RetryCount > 0,
                    MaxRetryAttempts = emailConfig.RetryCount,
                    MaxAttachmentSize = emailConfig.MaxAttachmentSize.GetValueOrDefault(),
                };
            }
        }
        public async Task<EmailSettings> GetSecondaryProviderEmailConfig()
        {
            using (var db = new Models.pmsdbContext())
            {
                var emailConfig = await db.EmailConfigTables
                    .Where(e => !e.Disabled.GetValueOrDefault() && e.EmailConfigName == "Secondary")
                    .FirstOrDefaultAsync();

                return emailConfig == null ? null : new EmailSettings
                {
                    ConfigName = emailConfig.EmailConfigName,
                    FromEmail = emailConfig.EmailConfigAccountId,
                    FromName = emailConfig.EmailConfigFromEmailDisplayName,
                    SmtpServer = emailConfig.EmailConfigSmtp,
                    AccessKey = emailConfig.EmailConfigFromEmailId,
                    SecretKey = emailConfig.EmailConfigPassword,
                    Region = "us-east-1",
                    Port = int.Parse(emailConfig.EmailConfigPort),
                    EnableSsl = emailConfig.EnableSsl.GetValueOrDefault(),
                    EnableRetry = emailConfig.RetryCount > 0,
                    MaxRetryAttempts = emailConfig.RetryCount,
                    MaxAttachmentSize = emailConfig.MaxAttachmentSize.GetValueOrDefault(),
                };
            }
        }
        public async Task<string> GenerateYieldSummaryReportPdfAndUploadToStorageAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                YieldReportRequestVm filter = new YieldReportRequestVm();
                filter.DateTo = toDate;
                filter.DateFrom = fromDate;
                filter.FabricProductId = 0;
                var data = new ReportDataFn(_globalData);
                var dataSet = data.YieldReport(filter);

                var pdfData = new YieldSummaryReportPdfAdapter(dataSet, filter.DateFrom.Value, filter.DateTo.Value);

                var fileName = $"YieldSummaryReport_{Guid.NewGuid():N}.pdf";

                if (pdfData.AllRecords.Count > 0)
                {
                    // Use the new WhatsApp-compatible upload method
                    var pdfUrl = await _pdfService.GeneratePdfAndUploadToWhatsAppStorageAsync(
                        pdfData,
                        fileName,
                        "yieldsummaryreport"  // Document type for hierarchical organization
                    );

                    _logger.LogInformation("Successfully generated and uploaded yield summary report PDF: {PdfUrl}", pdfUrl);
                    return pdfUrl;
                }

                _logger.LogWarning("No data found for yield summary report from {FromDate} to {ToDate}", fromDate, toDate);
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating yield summary report PDF and uploading to storage");
                return null;
            }
        }
        public async Task<Dictionary<string, string>> GenerateYieldSummaryReportParameters(long templateMasterId, DateTime fromdate, DateTime todate)
        {
            using (var db = new Models.pmsdbContext())
            {
                var templateParams = db.NotificationTemplateParameterTables
                    .Where(x => x.TemplateMasterId == templateMasterId)
                    .OrderBy(x => x.Sequence ?? int.MaxValue)
                    .ThenBy(x => x.ParameterName)
                    .ToList();

                var parameters = new Dictionary<string, string>();

                // Create a mapping of property names to their values
                // IMPORTANT: Use the EXACT parameter names from the template, not the variable names
                var productProperties = new Dictionary<string, string>
                {
                    // Use the exact parameter names from the WhatsApp template
                    { "1", fromdate.ToString("dd-MMM-yyyy") },
                    { "2", todate.ToString("dd-MMM-yyyy") },
                };

                // Map template parameters to their values
                foreach (var param in templateParams)
                {
                    string paramValue = productProperties.TryGetValue(param.ParameterName, out string value)
                        ? value
                        : param.DefaultValue ?? "N/A";

                    parameters.Add(param.ParameterName, paramValue);

                    if (param.IsRequired == true && (string.IsNullOrEmpty(paramValue) || paramValue.Equals("0")))
                    {
                        _logger.LogWarning($"Required parameter {param.ParameterName} has no value for template {templateMasterId}");
                    }
                }

                return parameters;
            }
        }

        public async Task<List<ScheduledNotification>> GetPendingScheduledNotifications()
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var currentTime = DateTime.UtcNow;

                // Get all active schedules that are due to run
                var schedules = await db.NotificationReportScheduleMappingTables
                    .Where(s => s.IsActive && !s.Disabled && s.NextRunTime.HasValue && s.NextRunTime <= currentTime)
                    .ToListAsync();

                var result = new List<ScheduledNotification>();

                foreach (var schedule in schedules)
                {
                    // Get the recipient information
                    var recipient = await GetRecipientById(schedule.NotificationGroupUserId ?? 0);
                    if (recipient == null) continue;

                    // Create parameters based on report type
                    var parameters = new Dictionary<string, string>();

                    if (schedule.ReportType == "YieldReportSummary")
                    {
                        // For yield report, we use the last 24 hours by default
                        var toDate = DateTime.UtcNow;
                        var fromDate = toDate.AddDays(-1);

                        // Use the exact parameter names from the WhatsApp template
                        parameters.Add("fromdate", fromDate.ToString("dd-MMM-yyyy"));
                        parameters.Add("todate", toDate.ToString("dd-MMM-yyyy"));

                        _logger.LogInformation("Created parameters for YieldReportSummary: fromdate={FromDate}, todate={ToDate}",
                            parameters["fromdate"], parameters["todate"]);
                    }

                    result.Add(new ScheduledNotification
                    {
                        ReportId = schedule.ReportId,
                        ReportType = schedule.ReportType,
                        ReportName = schedule.ReportName,
                        NotificationGroupUserId = schedule.NotificationGroupUserId ?? 0,
                        TemplateMasterId = schedule.TemplateMasterId ?? 0,
                        CronExpression = schedule.CronExpression,
                        LastRunTime = schedule.LastRunTime,
                        NextRunTime = schedule.NextRunTime,
                        IsActive = schedule.IsActive,
                        TimeZone = schedule.TimeZone,
                        AddedBy = schedule.AddedBy,
                        AddedDate = schedule.AddedDate,
                        Disabled = schedule.Disabled,
                        Recipient = recipient,
                        Parameters = parameters
                    });
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting pending scheduled notifications");
                return new List<ScheduledNotification>();
            }
        }

        public async Task UpdateNextRunTime(long reportId, DateTime lastRunTime, DateTime nextRunTime)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var schedule = await db.NotificationReportScheduleMappingTables.FindAsync(reportId);

                if (schedule != null)
                {
                    schedule.LastRunTime = lastRunTime;
                    schedule.NextRunTime = nextRunTime;
                    await db.SaveChangesAsync();

                    _logger.LogInformation("Updated schedule {ReportId} next run time to {NextRunTime}",
                        reportId, nextRunTime);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating next run time for report {ReportId}", reportId);
                throw;
            }
        }

        public async Task UpdateOnDemandNotificationStatus(long notificationGroupId)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var notificationGroup = await db.NotificationGroupsTables.FindAsync(notificationGroupId);

                if (notificationGroup != null)
                {
                    // Update the LastTriggeredBy and LastTriggeredDate fields
                    // These fields need to be added to the NotificationGroupsTable
                    // For now, we'll just log that the notification was triggered
                    _logger.LogInformation("On-demand notification triggered for group {NotificationGroupId}",
                        notificationGroupId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating on-demand notification status for group {NotificationGroupId}",
                    notificationGroupId);
                throw;
            }
        }

        public async Task<List<NotificationRecipient>> GetNotificationsByTriggerType(string triggerType)
        {
            try
            {
                using var db = new Models.pmsdbContext();
                var notifications = await db.NotificationGroupsTables
                    .Where(n => n.TriggerType == triggerType && !n.Disabled.GetValueOrDefault())
                    .Select(n => new NotificationRecipient
                    {
                        Id = n.NotificationGroupUserId,
                        Name = n.Name,
                        EmailId = n.Email,
                        MobileNumberString = n.MobileNumber,
                        EnableToEmail = n.EnableToEmail ?? false,
                        EnableCCEmail = n.EnableCcemail ?? false,
                        EnableBCCEmail = n.EnableBccemail ?? false,
                        IsWhatsAppNotificationEnabled = n.IsWhatsAppNotificationEnabled ?? false,
                        UserType = n.UserType,
                        UserMasterId = n.UserMasterId,
                        NotificationType = n.NotificationType,
                        ReportType = n.TriggerType,
                        WhatsAppTemplateMasterId = n.WhatsAppTemplateMasterId ?? 0
                    })
                    .ToListAsync();

                foreach (var notification in notifications)
                {
                    notification.MobileNumbers = !string.IsNullOrEmpty(notification.MobileNumberString)
                        ? notification.MobileNumberString.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries).ToList()
                        : new List<string>();
                }

                return notifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notifications by trigger type {TriggerType}", triggerType);
                return new List<NotificationRecipient>();
            }
        }
    }
}

