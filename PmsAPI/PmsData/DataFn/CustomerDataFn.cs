﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;

namespace PmsData.DataFn
{
    public class CustomerDataFn
    {
        public List<CustomerMasterVm> GetAllCustomers()
        {
            List<CustomerMasterVm> res = null;
            using (var db = new pmsdbContext())
            {
                res = (from a in db.CustomerMasters
                       join esc in db.EmailSubscriptionCustomerTables on a.CustomerId equals esc.CustomerId into escd
                       from esc in escd.DefaultIfEmpty()
                       join wasc in db.WhatsAppSubscriptionCustomerTables on a.CustomerId equals wasc.CustomerId into wascd
                       from wasc in wascd.DefaultIfEmpty()
                       where a.Disabled != true
                       select new CustomerMasterVm
                       {
                           CustomerId = a.CustomerId,
                           CustomerName = a.CustomerName,
                           CustomerContactNumber = a.CustomerContactNumber,
                           Gstnumber = a.Gstnumber,
                           Email = a.Email,
                           Address = a.Address,
                           State = a.State,
                           Country = a.Country,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           CustomerShortName = a.CustomerShortName,
                           CustomerCode = a.CustomerCode,
                           IsEmailNotification = esc.Enabled == true,
                           IsWhatsappNotification = wasc.Enabled == true
                       }).OrderBy(x => x.CustomerName).ToList();
            }
            return res;
        }


        public ApiFunctionResponseVm AddUpdateCustomer(CustomerMasterVm cust)
        {
            using (var db = new Models.pmsdbContext())
            {
                CustomerMaster res = new CustomerMaster();
                if (cust.CustomerId == 0)
                {
                    res.CustomerId = cust.CustomerId;
                    res.CustomerName = cust.CustomerName;
                    res.Gstnumber = cust.Gstnumber;
                    res.CustomerContactNumber = cust.CustomerContactNumber;
                    res.Email = cust.Email;
                    res.Address = cust.Address;
                    res.State = cust.State;
                    res.Country = cust.Country;
                    res.AddedBy = cust.AddedBy;
                    res.AddedDate = DateTime.Now;
                    res.Disabled = false;
                    res.CustomerShortName = cust.CustomerShortName;
                    res.CustomerCode = cust.CustomerCode;
                    db.CustomerMasters.Add(res);
                    db.SaveChanges();

                    AddOrUpdateSubscription(db, res.CustomerId, cust);

                }
                else
                {
                    res = db.CustomerMasters.Where(x => x.CustomerId == cust.CustomerId).FirstOrDefault();
                    if (res != null)
                    {
                        res.CustomerId = cust.CustomerId;
                        res.CustomerName = cust.CustomerName;
                        res.Gstnumber = cust.Gstnumber;
                        res.CustomerContactNumber = cust.CustomerContactNumber;
                        res.Email = cust.Email;
                        res.Address = cust.Address;
                        res.State = cust.State;
                        res.Country = cust.Country;
                        res.AddedBy = cust.AddedBy;
                        res.AddedDate = DateTime.Now;
                        res.CustomerShortName = cust.CustomerShortName;
                        res.CustomerCode = cust.CustomerCode;

                    }
                    AddOrUpdateSubscription(db, cust.CustomerId, cust);
                }

                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        private void AddOrUpdateSubscription(Models.pmsdbContext db, long CustomerId, CustomerMasterVm cust)
        {
            try
            {
                var emailSubscription = db.EmailSubscriptionCustomerTables.FirstOrDefault(x => x.CustomerId == CustomerId);
                if (emailSubscription != null)
                {
                    emailSubscription.Enabled = cust.IsEmailNotification;
                    emailSubscription.LastUpdatedBy = cust.AddedBy;
                    emailSubscription.LastUpdatedDate = DateTime.Now;
                }
                else
                {
                    emailSubscription = new EmailSubscriptionCustomerTable
                    {
                        CustomerId = CustomerId,
                        Enabled = cust.IsEmailNotification,
                        AddedBy = cust.AddedBy,
                        AddedDate = DateTime.Now,
                    };
                    db.EmailSubscriptionCustomerTables.Add(emailSubscription);
                }

                var whatsappSubscription = db.WhatsAppSubscriptionCustomerTables.FirstOrDefault(x => x.CustomerId == CustomerId);
                if (whatsappSubscription != null)
                {
                    whatsappSubscription.Enabled = cust.IsWhatsappNotification;
                    whatsappSubscription.LastUpdatedBy = cust.AddedBy;
                    whatsappSubscription.LastUpdatedDate = DateTime.Now;
                }
                else
                {
                    whatsappSubscription = new WhatsAppSubscriptionCustomerTable
                    {
                        CustomerId = CustomerId,
                        Enabled = cust.IsWhatsappNotification,
                        AddedBy = cust.AddedBy,
                        AddedDate = DateTime.Now,
                    };
                    db.WhatsAppSubscriptionCustomerTables.Add(whatsappSubscription);
                }
            }
            catch (Exception ex)
            {

            }
        }
        public ApiFunctionResponseVm DeleteCustomer(CustomerMasterVm param)
        {
            using (var db = new Models.pmsdbContext())
            {
                using (var transaction = db.Database.BeginTransaction())
                {
                    try
                    {
                        CustomerMaster item = db.CustomerMasters.FirstOrDefault(x => x.CustomerId == param.CustomerId);
                        if (item == null)
                        {
                            return new ApiFunctionResponseVm(HttpStatusCode.NotFound, "Customer not found.");
                        }

                        item.Disabled = true;
                        item.DisabledBy = param.DisabledBy;
                        item.DisabledDate = System.DateTime.Now;

                        var emailSubscriptions = db.EmailSubscriptionCustomerTables.Where(e => e.CustomerId == param.CustomerId);
                        if (emailSubscriptions != null)
                        {
                            foreach (var emailSubscription in emailSubscriptions)
                            {
                                emailSubscription.Enabled = false;
                                emailSubscription.LastUpdatedBy = param.DisabledBy;
                                emailSubscription.LastUpdatedDate = System.DateTime.Now;
                            }
                        }

                        var whatsappSubscriptions = db.WhatsAppSubscriptionCustomerTables.Where(w => w.CustomerId == param.CustomerId);
                        if (whatsappSubscriptions != null)
                        {
                            foreach (var whatsappSubscription in whatsappSubscriptions)
                            {
                                whatsappSubscription.Enabled = false;
                                whatsappSubscription.LastUpdatedBy = param.DisabledBy;
                                whatsappSubscription.LastUpdatedDate = System.DateTime.Now;
                            }
                        }

                        db.SaveChanges();
                        transaction.Commit();
                        return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        return new ApiFunctionResponseVm(HttpStatusCode.InternalServerError, "An error has occurred. Please contact the administrator. " + ex);
                        throw;
                    }
                }
            }
        }

    }
}
