using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    /// <summary>
    /// User Dashboard Configuration Table
    /// Following PMS entity naming and structure patterns
    /// </summary>
    public partial class UserDashboardConfigTable
    {
        public long ConfigId { get; set; }
        public string UserId { get; set; }
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public string ConfigName { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public bool Disabled { get; set; }           // PMS soft delete pattern
        public int Version { get; set; }
        public string Tags { get; set; }
        public string AddedBy { get; set; }          // PMS audit pattern
        public DateTime AddedDate { get; set; }      // PMS audit pattern
        public string ModifiedBy { get; set; }       // PMS audit pattern
        public DateTime ModifiedDate { get; set; }   // PMS audit pattern
    }
}
