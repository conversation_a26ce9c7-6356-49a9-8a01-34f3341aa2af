using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    /// <summary>
    /// System Dashboard Defaults Table
    /// Following PMS entity naming and structure patterns
    /// </summary>
    public partial class SystemDashboardDefaultsTable
    {
        public long DefaultId { get; set; }
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public int Version { get; set; }
        public bool Disabled { get; set; }           // PMS soft delete pattern
        public DateTime CreatedDate { get; set; }
    }
}
