﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleFormulationCodeMaster
    {
        public long SaleFormulationCodeId { get; set; }
        public string SaleFormulationCode { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public long? CategoryId { get; set; }
        public long? ThicknessId { get; set; }
        public decimal? PreSkinGsm { get; set; }
        public decimal? SkinGsm { get; set; }
        public decimal? FoamGsm { get; set; }
        public decimal? AdhesiveGsm { get; set; }
        public decimal? FabricGsm { get; set; }
        public decimal? TotalGsm { get; set; }
        public long? FabricProductId { get; set; }
        public decimal? FabricProductQty { get; set; }
        public long? FinishedProductId { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public decimal? MinSpeed { get; set; }
        public decimal? MaxSpeed { get; set; }
        public bool? IsOrderLinkingRequired { get; set; }
    }
}
