﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class UserMaster
    {
        public UserMaster()
        {
            FileUploadTables = new HashSet<FileUploadTable>();
            PackagingTypeMasterAddedBies = new HashSet<PackagingTypeMaster>();
            PackagingTypeMasterDisabledBies = new HashSet<PackagingTypeMaster>();
            ProductTransferTableActionBies = new HashSet<ProductTransferTable>();
            ProductTransferTableAddedBies = new HashSet<ProductTransferTable>();
            StockMasters = new HashSet<StockMaster>();
            StockPriceTrackingTables = new HashSet<StockPriceTrackingTable>();
            UserExceptionForceLogoutTableAddedBies = new HashSet<UserExceptionForceLogoutTable>();
            UserExceptionForceLogoutTableUsers = new HashSet<UserExceptionForceLogoutTable>();
        }

        public long UserId { get; set; }
        public string Name { get; set; }
        public string Contact { get; set; }
        public string Email { get; set; }
        public string Address { get; set; }
        public string Status { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string AdobjectId { get; set; }
        public string EmailAddress { get; set; }

        public virtual ICollection<FileUploadTable> FileUploadTables { get; set; }
        public virtual ICollection<PackagingTypeMaster> PackagingTypeMasterAddedBies { get; set; }
        public virtual ICollection<PackagingTypeMaster> PackagingTypeMasterDisabledBies { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableActionBies { get; set; }
        public virtual ICollection<ProductTransferTable> ProductTransferTableAddedBies { get; set; }
        public virtual ICollection<StockMaster> StockMasters { get; set; }
        public virtual ICollection<StockPriceTrackingTable> StockPriceTrackingTables { get; set; }
        public virtual ICollection<UserExceptionForceLogoutTable> UserExceptionForceLogoutTableAddedBies { get; set; }
        public virtual ICollection<UserExceptionForceLogoutTable> UserExceptionForceLogoutTableUsers { get; set; }
    }
}
