﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class WhatsAppTemplateMaster
    {
        public WhatsAppTemplateMaster()
        {
            NotificationGroupsTables = new HashSet<NotificationGroupsTable>();
            NotificationSaleOrderStagesTables = new HashSet<NotificationSaleOrderStagesTable>();
            NotificationTemplateParameterTables = new HashSet<NotificationTemplateParameterTable>();
        }

        public long WhatsAppTemplateMasterId { get; set; }
        public string ProviderName { get; set; }
        public long? ProviderTemplateId { get; set; }
        public string ProviderTemplateName { get; set; }
        public string ProviderTemplateDescription { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public string TemplateParameters { get; set; }
        public string Category { get; set; }
        public string Language { get; set; }
        public string ApprovalStatus { get; set; }

        public virtual ICollection<NotificationGroupsTable> NotificationGroupsTables { get; set; }
        public virtual ICollection<NotificationSaleOrderStagesTable> NotificationSaleOrderStagesTables { get; set; }
        public virtual ICollection<NotificationTemplateParameterTable> NotificationTemplateParameterTables { get; set; }
    }
}
