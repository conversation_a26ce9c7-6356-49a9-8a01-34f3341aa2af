﻿using System;
using System.Collections.Generic;

namespace PmsData.Models
{
    public partial class SaleOrderDispatchTable
    {
        public long SaleOrderDispatchId { get; set; }
        public long SaleOrderId { get; set; }
        public long TransportId { get; set; }
        public long? VehicleId { get; set; }
        public DateTime DispatchDate { get; set; }
        public string DispatchNumber { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public decimal? PacketNumber { get; set; }
        public decimal? PacketWeight { get; set; }
        public bool? IsGateIn { get; set; }
        public bool? IsGateOut { get; set; }
    }
}
