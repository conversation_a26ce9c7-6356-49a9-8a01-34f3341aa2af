﻿using System;
using System.Collections.Generic;
using System.Linq;

namespace PmsEntity.ViewModel
{
    public class NotificationGroupsTableVm
    {
        public long NotificationGroupUserId { get; set; }
        public string NotificationType { get; set; }
        public string Name { get; set; }
        public string Email { get; set; }
        public string UserType { get; set; }
        public long? UserMasterId { get; set; }
        public long? WhatsAppTemplateMasterId { get; set; }

        // Updated to support multiple trigger types
        public List<string> TriggerType { get; set; } = new List<string>();

        // Used for internal JSON serialization/deserialization
        public string TriggerTypeJson { get; set; }

        public string ReportName { get; set; }
        public bool? IsWhatsAppNotificationEnabled { get; set; }
        public string CronScheduleExpression { get; set; }
        public string MobileNumber { get; set; }
        public bool? EnableToEmail { get; set; }
        public bool? EnableCcemail { get; set; }
        public bool? EnableBccemail { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool? Disabled { get; set; }
        public string DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
        public DateTime? LastTriggeredDate { get; set; }
        public string LastTriggeredBy { get; set; }
        public List<EmailGroupMappingTableVm> EmailGroupMappings { get; set; }
        public List<NotificationReportScheduleMappingTableVm> NotificationReportScheduleMappings { get; set; }
    }
    public class NotificationReportScheduleMappingTableVm
    {
        public long ReportId { get; set; }
        public string ReportType { get; set; }
        public string ReportName { get; set; }
        public long? NotificationGroupUserId { get; set; }
        public long? TemplateMasterId { get; set; }
        public string CronExpression { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public bool IsActive { get; set; }
        public string TimeZone { get; set; }
        public string AddedBy { get; set; }
        public DateTime? AddedDate { get; set; }
        public bool Disabled { get; set; }
        public long? DisabledBy { get; set; }
        public DateTime? DisabledDate { get; set; }
    }

    public class UpdateNotificationScheduleVm
    {
        public long NotificationGroupUserId { get; set; }
        public long ReportId { get; set; }
        public bool IsActive { get; set; }
        public bool Disabled { get; set; }
    }
}
