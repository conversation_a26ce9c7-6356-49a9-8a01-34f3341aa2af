using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    /// <summary>
    /// User Dashboard Configuration View Model
    /// Following PMS ViewModel naming pattern
    /// </summary>
    public class UserDashboardConfigVm
    {
        public long ConfigId { get; set; }
        public string UserId { get; set; }
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public string ConfigName { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public int Version { get; set; }
        public string Tags { get; set; }
    }

    /// <summary>
    /// Request model for saving dashboard configuration
    /// </summary>
    public class SaveDashboardConfigRequest
    {
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public string ConfigName { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public string Tags { get; set; }
    }

    /// <summary>
    /// Response model for dashboard configuration operations
    /// </summary>
    public class DashboardConfigResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public UserDashboardConfigVm Data { get; set; }
        public List<UserDashboardConfigVm> DataList { get; set; }
    }

    /// <summary>
    /// Request model for updating dashboard configuration
    /// </summary>
    public class UpdateDashboardConfigRequest
    {
        public long ConfigId { get; set; }
        public string DashboardType { get; set; }
        public string ConfigJson { get; set; }
        public string ConfigName { get; set; }
        public string Description { get; set; }
        public bool IsDefault { get; set; }
        public string Tags { get; set; }
    }
}
