using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class GateDashboardVm
    {
        // Gate Operations - Enhanced with Total and Filtered counts
        public int PendingGateOutTotalCount { get; set; }
        public int PendingGateOutFilteredCount { get; set; }
        public int PendingGatePassTotalCount { get; set; }
        public int PendingGatePassFilteredCount { get; set; }
        public int InvoicesWithoutPOTotalCount { get; set; }
        public int InvoicesWithoutPOFilteredCount { get; set; }

        // Purchase Order Analytics - Enhanced with Total and Filtered counts
        public int ActivePOTotalCount { get; set; }
        public int ActivePOFilteredCount { get; set; }
        public int RevisedPOTotalCount { get; set; }
        public int RevisedPOFilteredCount { get; set; }
        public int DelayedDeliveryPOTotalCount { get; set; }
        public int DelayedDeliveryPOFilteredCount { get; set; }
        public int DelayedPaymentPOTotalCount { get; set; }
        public int DelayedPaymentPOFilteredCount { get; set; }

        // Product Analytics - Enhanced with Total and Filtered counts
        public int TotalProductsTotalCount { get; set; }
        public int TotalProductsFilteredCount { get; set; }
        public int LowStockProductsTotalCount { get; set; }
        public int LowStockProductsFilteredCount { get; set; }
        public int OutOfStockProductsTotalCount { get; set; }
        public int OutOfStockProductsFilteredCount { get; set; }
        public int DelayedDemandsTotalCount { get; set; }
        public int DelayedDemandsFilteredCount { get; set; }
        public int ProductsBelowMinQuantityTotalCount { get; set; }
        public int ProductsBelowMinQuantityFilteredCount { get; set; }
        public int PendingIssueRequestsTotalCount { get; set; }
        public int PendingIssueRequestsFilteredCount { get; set; }

        // Backward compatibility properties (deprecated but maintained for existing integrations)
        [Obsolete("Use PendingGateOutFilteredCount instead")]
        public int PendingGateOutCount => PendingGateOutFilteredCount;

        [Obsolete("Use PendingGatePassFilteredCount instead")]
        public int PendingGatePassCount => PendingGatePassFilteredCount;

        [Obsolete("Use InvoicesWithoutPOFilteredCount instead")]
        public int InvoicesWithoutPOCount => InvoicesWithoutPOFilteredCount;

        [Obsolete("Use ActivePOFilteredCount instead")]
        public int ActivePOCount => ActivePOFilteredCount;

        [Obsolete("Use RevisedPOFilteredCount instead")]
        public int RevisedPOCount => RevisedPOFilteredCount;

        [Obsolete("Use DelayedDeliveryPOFilteredCount instead")]
        public int DelayedDeliveryPOCount => DelayedDeliveryPOFilteredCount;

        [Obsolete("Use DelayedPaymentPOFilteredCount instead")]
        public int DelayedPaymentPOCount => DelayedPaymentPOFilteredCount;

        [Obsolete("Use TotalProductsFilteredCount instead")]
        public int TotalProductsCount => TotalProductsFilteredCount;

        [Obsolete("Use LowStockProductsFilteredCount instead")]
        public int LowStockProductsCount => LowStockProductsFilteredCount;

        [Obsolete("Use OutOfStockProductsFilteredCount instead")]
        public int OutOfStockProductsCount => OutOfStockProductsFilteredCount;

        [Obsolete("Use DelayedDemandsFilteredCount instead")]
        public int DelayedDemandsCount => DelayedDemandsFilteredCount;

        [Obsolete("Use ProductsBelowMinQuantityFilteredCount instead")]
        public int ProductsBelowMinQuantityCount => ProductsBelowMinQuantityFilteredCount;

        [Obsolete("Use PendingIssueRequestsFilteredCount instead")]
        public int PendingIssueRequestsCount => PendingIssueRequestsFilteredCount;
    }

    public class GateDashboardRequestVm
    {
        public string DateFilterType { get; set; } = "fullday";
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
}
