using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class GateDashboardVm
    {
        // Gate Operations - Enhanced with Total and Filtered counts
        public int PendingGateOutTotalCount { get; set; }
        public int PendingGateOutFilteredCount { get; set; }
        public int PendingGatePassTotalCount { get; set; }
        public int PendingGatePassFilteredCount { get; set; }
        public int InvoicesWithoutPOTotalCount { get; set; }
        public int InvoicesWithoutPOFilteredCount { get; set; }

        // Purchase Order Analytics - Enhanced with Total and Filtered counts
        public int ActivePOTotalCount { get; set; }
        public int ActivePOFilteredCount { get; set; }
        public int RevisedPOTotalCount { get; set; }
        public int RevisedPOFilteredCount { get; set; }
        public int DelayedDeliveryPOTotalCount { get; set; }
        public int DelayedDeliveryPOFilteredCount { get; set; }
        public int DelayedPaymentPOTotalCount { get; set; }
        public int DelayedPaymentPOFilteredCount { get; set; }

        // Product Analytics - Enhanced with Total and Filtered counts
        public int TotalProductsTotalCount { get; set; }
        public int TotalProductsFilteredCount { get; set; }
        public int LowStockProductsTotalCount { get; set; }
        public int LowStockProductsFilteredCount { get; set; }
        public int OutOfStockProductsTotalCount { get; set; }
        public int OutOfStockProductsFilteredCount { get; set; }
        public int DelayedDemandsTotalCount { get; set; }
        public int DelayedDemandsFilteredCount { get; set; }
        public int ProductsBelowMinQuantityTotalCount { get; set; }
        public int ProductsBelowMinQuantityFilteredCount { get; set; }
        public int PendingIssueRequestsTotalCount { get; set; }
        public int PendingIssueRequestsFilteredCount { get; set; }


    }

    public class GateDashboardRequestVm
    {
        public string DateFilterType { get; set; } = "fullday";
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
}
