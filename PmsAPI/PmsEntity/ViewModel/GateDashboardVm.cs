using System;
using System.Collections.Generic;

namespace PmsEntity.ViewModel
{
    public class GateDashboardVm
    {
        // Gate Operations
        public int PendingGateOutCount { get; set; }
        public int PendingGatePassCount { get; set; }
        public int InvoicesWithoutPOCount { get; set; }

        // Purchase Order Analytics
        public int ActivePOCount { get; set; }
        public int RevisedPOCount { get; set; }
        public int DelayedDeliveryPOCount { get; set; }
        public int DelayedPaymentPOCount { get; set; }

        // Product Analytics
        public int TotalProductsCount { get; set; }
        public int LowStockProductsCount { get; set; }
        public int OutOfStockProductsCount { get; set; }
        public int DelayedDemandsCount { get; set; }
        public int ProductsBelowMinQuantityCount { get; set; }
        public int PendingIssueRequestsCount { get; set; }
    }

    public class GateDashboardRequestVm
    {
        public string DateFilterType { get; set; } = "fullday";
        public DateTime? DateFrom { get; set; }
        public DateTime? DateTo { get; set; }
    }
}
