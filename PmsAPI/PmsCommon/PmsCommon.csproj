<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Communication.Messages" Version="1.1.0" />
    <PackageReference Include="Azure.Identity" Version="1.10.0" />
    <PackageReference Include="Azure.Storage.Blobs" Version="12.17.0" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    <PackageReference Include="Microsoft.Azure.KeyVault" Version="3.0.5" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.EnvironmentVariables" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="6.0.21" />
    <PackageReference Include="AWSSDK.Core" Version="3.7.202.16" />
    <PackageReference Include="AWSSDK.SimpleEmailV2" Version="3.7.201.20" />
    <PackageReference Include="MimeKit" Version="4.2.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Options.ConfigurationExtensions" Version="6.0.0" />
    <PackageReference Include="RestSharp" Version="112.1.0" />
    <PackageReference Include="QuestPDF" Version="2024.12.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\PmsCore\PmsCore.csproj" />
  </ItemGroup>

</Project>
