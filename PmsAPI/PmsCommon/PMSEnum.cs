using System;
using System.Text;
using Microsoft.Azure.KeyVault;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;

namespace PmsCommon
{
    public static class PMSEnum
    {
        public static T ParseEnum<T>(string value)
        {
            return (T)Enum.Parse(typeof(T), value, true);
        }
    }

    public enum ESalesOrderStatus
    {
        NotYet = 0,
        WorkPlan = 1,
        Inspection = 2,
        RawMaterialRequested = 3,
        RawMaterialIssued = 4,
        ProductionStarted = 5,
        Mixing = 6,
        InJumbo = 7,
        MoveToPostProcess = 8,
        PrintAssigned = 9,
        PrintInProcess = 10,
        PrintCompleted = 11,
        EmbossingAssigned = 12,
        EmbossingInProcess = 13,
        EmbossingCompleted = 14,
        VacuumAssigned = 15,
        VacuumInProcess = 16,
        VacuumCompleted = 17,
        LacquerAssigned = 18,
        LacquerInProcess = 19,
        LacquerCompleted = 20,
        TumblingAssigned = 21,
        TumblingInProcess = 22,
        TumblingCompleted = 23,
        JumboInspection = 24,
        MoveToDispatch = 25,
        PartialDispatchReady = 26,
        PartialDispatchCompleted = 27,
        DispatchReady = 28,
        DispatchCompleted = 30,
        LiningOrderMerged = 101,
    }

    public static class PMSStatus
    {
        public const string
           Planning = "Planning",
           InProgress = "InProgress",
           Complete = "Complete",
           Hold = "Hold",
           Blocked = "Blocked",
           Pending = "Pending",
           Approved = "Approved",
           Declined = "Declined",
           NotStarted = "NotStarted",
           ToView = "ToView",
           ToAction = "ToAction",
            Accepted = "Accepted",
            Rejected = "Rejected";
    }
    public static class CostingStatus
    {
        public const string
            Pending = "Pending",
            PartialReady = "Partial Ready",
            Ready = "Ready",
            Submitted = "Submitted",
            Approved = "Approved",
            Rejected = "Rejected",
            Hold = "Hold";

    }

    public static class PMSSaleOrderStatus
    {
        public const string
           ApprovalPending = "Approval Pending",
           Active = "Active",
           Hold = "Hold",
           Cancelled = "Cancelled",
           Blocked = "Blocked";
    }

    public static class PMSPurchaseOrderStatus
    {
        public const string
           DemandReceived = "Demand Received",
           New = "New",
           Revised = "Revised",
           Approved = "Approved",
           Active = "Active",
           Complete = "Complete",
           ReOpen = "Re-Open",
           Blocked = "Blocked",
           Unblock = "Unblock",
           SupplierNotifiedViaEmail = "Supplier Notified Via Email",
           Cancelled = "Cancelled",
           PartialReceived = "Partial Received",
           PartialPaymentCompleted = "Partial Payment Completed",
           FullPaymentCompleted = "Full Payment Completed",
           InTransit = "In Transit",
           GateInCompleted = "Gate In Completed",
           StoreAcknowledged = "Store Acknowledged";
    }

    public static class PMSDemandtatus
    {
        public const string
           Active = "Active",
           POCreated = "PO Created",
           PartialReceived = "Partial Received",
           Received = "Received",
           Blocked = "Blocked",
           Cancelled = "Cancelled";
    }

    public static class OrderType
    {
        public const string
           Sample = "Sample",
           Paid = "Paid";
    }
    public static class SaleOrderType
    {
        public const string
           PVC = "PVC",
           PU = "PU";
    }

    public static class PostProcessItems
    {
        public const string
           Print = "Print",
           Lacquer = "Lacquer",
           Embossing = "Embossing",
           Vacuum = "Vacuum",
           Tumbling = "Tumbling";
    }
    public static class PostProcessItemsStatus
    {
        public const string
           Assigned = "Assigned",
           InProcess = "InProcess",
           Completed = "Completed",
           Remove = "Remove";
    }

    public static class PMSEmailGroups
    {
        public const string
            ScheduledReportsGroup = "ScheduledReportsGroup",
            LowStockReportsGroup = "LowStockReportsGroup",
            CostingRecalculationGroup = "CostingRecalculationGroup",
            SaleOrderStatusCCGroup = "SaleOrderStatusCCGroup",
            SaleOrderStatusReplyTo = "SaleOrderStatusReplyTo";
    }

    public static class SaleOrderEmailStatus
    {
        public const string
           OrderReceived = "Order Received",
           InProductionPlanning = "In Production Planning",
           RemovedPlanning = "Removed From Planning",
           StartingProduction = "Starting Production",
           ProductionCompleted = "Production Completed",
           FinalInspectionCompleted = "Final Inspection Completed",
           ReadyToDispatch = "Ready To Dispatch All",
           PartialDispatchReady = "Partial Dispatch Ready",
           OrderDispatched = "Order Dispatched";
    }
    public static class EmailTrackingModules
    {
        public const string
           DispatchPackaging = "Dispatch Packaging",
           PurchaseOrder = "Purchase Order",
           ProductionPlanningReport = "Production Planning Report";
    }

    public static class EstimationStatus
    {
        public const string
           Draft = "Draft",
           Active = "Active",
           Reviewed = "Reviewed",
           Cancelled = "Cancelled",
           Approved = "Approved";
    }

    public enum TimeZoneId
    {
        IndiaStandardTime,
        // Add other time zones as needed
    }

    public static class OutpassStatus
    {
        public const string
           ApprovalPending = "Approval Pending",
           Approved = "Approved",
           VehicleAssigned = "Vehicle Assigned",
           OnHold = "On Hold",
           Cancelled = "Cancelled",
           Rejected = "Rejected",
           ReturnExtended = "Return Extended",
           ReturnCompleted = "Return Completed",
           GateOutCompleted = "Gate Out Completed";
    }

    public static class StockLabelStatus
    {
        public const string
           Pending = "Pending",
           Inspected = "Inspected",
           Updated = "Updated",
           Active = "Active",
           Accepted = "Accepted",
           Rejected = "Rejected",
           Allocated = "Allocated",
           DeAllocated = "De-Allocated",
           InActive = "In-Active",
           Splitted = "Splitted",
           Transferred = "Transferred",
           Consumed = "Consumed",
           Damaged = "Damaged",
           Disposed = "Disposed",
           Returned = "Returned",
           Dispatched = "Dispatched";
    }
}
