// using QuestPDF.Fluent;
// using QuestPDF.Helpers;
// using QuestPDF.Infrastructure;
// using System;
// using System.Collections.Generic;
// using System.IO;
// using System.Linq;
// using PmsCore.DataAccessRepository.Models;
// public class YieldReportDocument : IDocument
// {
//     private readonly List<YieldReportDto> _data;
//     private readonly string _companyLogoPath;

//     public YieldReportDocument(List<YieldReportDto> data, string companyLogoPath)
//     {
//         _data = data;
//         _companyLogoPath = companyLogoPath;
//     }

//     public void Compose(IDocumentContainer container)
//     {
//         container.Page(page =>
//         {
//             page.Margin(20);
//             page.Size(PageSizes.A4.Landscape());

//             page.Header().Element(ComposeHeader);
//             page.Content().Element(ComposeContent);
//             page.Footer().AlignCenter().Text(text =>
//             {
//                 text.Span("Powered By: ").FontSize(9);
//                 text.Span("KanzenFlow").FontSize(9);
//             });
//         });
//     }

//     private void ComposeHeader(IContainer container)
//     {
//         container.Row(row =>
//         {
//             // Company Logo
//             if (File.Exists(_companyLogoPath))
//             {
//                 row.ConstantItem(100).Height(50).Image(_companyLogoPath);
//             }

//             // Company Details
//             row.RelativeItem().Column(column =>
//             {
//                 column.Item().AlignCenter().Text("ZAIBUNCO INDUSTRIES")
//                     .FontSize(20).Bold();
//                 column.Item().AlignCenter().Text("Yield Report Summary for today")
//                     .FontSize(14);
//                 column.Item().AlignCenter().Text(DateTime.Now.ToString("dd-MMM-yyyy"))
//                     .FontSize(10);
//             });
//         });
//     }

//     private void ComposeContent(IContainer container)
//     {
//         var summaryData = CalculateSummary();

//         container.Column(column =>
//         {
//             // Summary Table
//             column.Item().Element(cont => ComposeSummaryTable(cont, summaryData));

//             column.Item().PaddingVertical(10).Text("Orders with Yield below 90% or PRD MTR above 5% of Order QTY:")
//                 .Bold();

//             // Detailed Table
//             column.Item().Element(cont => ComposeDetailTable(cont));
//         });
//     }

//     private void ComposeSummaryTable(IContainer container, YieldSummary summary)
//     {
//         container.Table(table =>
//         {
//             table.ColumnsDefinition(columns =>
//             {
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//                 columns.RelativeColumn();
//             });

//             // Header
//             AddTableHeader(table, "Order Quantity", "PRD MTR", "Over Production %", "First", "A Grade",
//                 "Lot", "NS", "CP", "Yield %", "SAMP", "SCRP", "GRD Total");

//             // Data
//             table.Cell().Element(StyleCell).Text(summary.OrderQty.ToString());
//             table.Cell().Element(StyleCell).Text(summary.PrdMtr.ToString());
//             table.Cell().Element(StyleCell).Text($"{summary.OverProdPer:F2}");
//             table.Cell().Element(StyleCell).Text(summary.First.ToString());
//             table.Cell().Element(StyleCell).Text(summary.AGrade.ToString());
//             table.Cell().Element(StyleCell).Text(summary.Lot.ToString());
//             table.Cell().Element(StyleCell).Text(summary.NS.ToString());
//             table.Cell().Element(StyleCell).Text(summary.CutPc.ToString());
//             table.Cell().Element(StyleCell).Text($"{summary.YieldPer:F2}");
//             table.Cell().Element(StyleCell).Text(summary.Sample.ToString());
//             table.Cell().Element(StyleCell).Text(summary.Scrap.ToString());
//             table.Cell().Element(StyleCell).Text(summary.GrandTotal.ToString());
//         });
//     }

//     private void ComposeDetailTable(IContainer container)
//     {
//         container.Table(table =>
//         {
//             // Define columns
//             table.ColumnsDefinition(columns =>
//             {
//                 foreach (var _ in Enumerable.Range(0, 17))
//                 {
//                     columns.RelativeColumn();
//                 }
//             });

//             // Add headers
//             AddTableHeader(table, "S.No", "Inspection Date", "Sale Order No.", "Sale Order Code", "Fabric",
//                 "Order QTY", "PRD MTR", "Over PRD %", "1st", "A", "Lot", "NS", "CP", "Yield(%)",
//                 "SAMP", "SCRP", "Grd Total");

//             // Add data rows
//             var rowNumber = 1;
//             foreach (var item in _data)
//             {
//                 var yield = (item.FirstGrade + item.AGrade + item.SampleQuantity) * 100 / item.ActualQuantity;
//                 var overProdPer = ((item.ManufacturingQuantity - item.SaleOrderQuantity) / item.SaleOrderQuantity) * 100;
//                 var grandTotal = item.FirstGrade + item.AGrade + item.LOTGrade + item.NSGrade +
//                                item.CUTPCGrade + item.SampleQuantity + item.FILMGrade + item.WASTEGrade;

//                 if (yield < 90 || overProdPer > 5)
//                 {
//                     AddDataRow(table, rowNumber++, item, yield ?? 0, overProdPer ?? 0, grandTotal ?? 0);
//                 }
//             }
//         });
//     }

//     private static void AddTableHeader(TableDescriptor table, params string[] headers)
//     {
//         foreach (var header in headers)
//         {
//             table.Cell().Element(HeaderCellStyle).Text(header);
//         }
//     }
//     private static IContainer HeaderCellStyle(IContainer container) =>
//         container.DefaultTextStyle(x => x.SemiBold())
//             .PaddingVertical(5)
//             .PaddingHorizontal(5)
//             .Border(0.5f)
//             .BorderColor(Colors.Grey.Darken2)
//             .Background(Colors.Grey.Lighten3);


//     private static IContainer StyleCell(IContainer container) => container
//         .DefaultTextStyle(x => x.FontSize(9))
//         .PaddingVertical(5)
//         .PaddingHorizontal(5)
//         .Border(0.5f)
//         .BorderColor(Colors.Grey.Lighten2);

//     private static void AddDataRow(TableDescriptor table, int rowNumber, YieldReportVm item,
//         decimal yield, decimal overProdPer, decimal grandTotal)
//     {
//         table.Cell().Element(StyleCell).Text(rowNumber.ToString());
//         table.Cell().Element(StyleCell).Text(item.AddedDate?.ToString("dd-MMM-yyyy"));
//         table.Cell().Element(StyleCell).Text(item.SaleOrderNumber);
//         table.Cell().Element(StyleCell).Text(item.SaleOrderCode);
//         table.Cell().Element(StyleCell).Text(item.FabricName);
//         table.Cell().Element(StyleCell).Text(item.SaleOrderQuantity.ToString());
//         table.Cell().Element(StyleCell).Text(item.ActualQuantity.ToString());
//         table.Cell().Element(StyleCell).Text($"{overProdPer:F2}");
//         table.Cell().Element(StyleCell).Text(item.FirstGrade.ToString());
//         table.Cell().Element(StyleCell).Text(item.AGrade.ToString());
//         table.Cell().Element(StyleCell).Text(item.LOTGrade.ToString());
//         table.Cell().Element(StyleCell).Text(item.NSGrade.ToString());
//         table.Cell().Element(StyleCell).Text(item.CUTPCGrade.ToString());
//         table.Cell().Element(StyleCell).Text($"{yield:F2}");
//         table.Cell().Element(StyleCell).Text(item.SampleQuantity.ToString());
//         table.Cell().Element(StyleCell).Text((item.FILMGrade + item.WASTEGrade).ToString());
//         table.Cell().Element(StyleCell).Text($"{grandTotal:F2}");
//     }

//     private YieldSummary CalculateSummary()
//     {
//         var summary = new YieldSummary();
//         foreach (var item in _data)
//         {
//             summary.OrderQty += item.SaleOrderQuantity ?? 0;
//             summary.PrdMtr += item.ManufacturingQuantity ?? 0;
//             summary.First += item.FirstGrade ?? 0;
//             summary.AGrade += item.AGrade ?? 0;
//             summary.Lot += item.LOTGrade ?? 0;
//             summary.NS += item.NSGrade ?? 0;
//             summary.CutPc += item.CUTPCGrade ?? 0;
//             summary.Sample += item.SampleQuantity ?? 0;
//             summary.WasteGrade += item.WASTEGrade ?? 0;
//             summary.FilmGrade += item.FILMGrade ?? 0;
//         }

//         summary.OverProdPer = (summary.PrdMtr - summary.OrderQty) / summary.OrderQty * 100;
//         summary.Scrap = summary.FilmGrade + summary.WasteGrade;
//         summary.YieldPer = (summary.First + summary.AGrade + summary.Sample) * 100 / summary.PrdMtr;
//         summary.GrandTotal = summary.First + summary.AGrade + summary.Lot + summary.NS +
//                            summary.CutPc + summary.Sample + summary.WasteGrade + summary.FilmGrade;

//         return summary;
//     }

//     private class YieldSummary
//     {
//         public decimal OrderQty { get; set; }
//         public decimal PrdMtr { get; set; }
//         public decimal OverProdPer { get; set; }
//         public decimal First { get; set; }
//         public decimal AGrade { get; set; }
//         public decimal Lot { get; set; }
//         public decimal NS { get; set; }
//         public decimal CutPc { get; set; }
//         public decimal YieldPer { get; set; }
//         public decimal Sample { get; set; }
//         public decimal Scrap { get; set; }
//         public decimal GrandTotal { get; set; }
//         public decimal WasteGrade { get; set; }
//         public decimal FilmGrade { get; set; }
//     }
// }