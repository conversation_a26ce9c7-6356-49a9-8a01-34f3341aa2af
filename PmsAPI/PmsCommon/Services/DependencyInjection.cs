using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using PmsCore.Notifications.Interfaces;
using PmsCommon.Services.Notifications.Implementations;
using PmsCore.Notifications.Models;
using PmsCore.PDFGeneration.Models;

namespace PmsCommon.Services
{
    public static class DependencyInjection
    {
        public static IServiceCollection AddNotificationServices(this IServiceCollection services, IConfiguration configuration)
        {
            // services.AddHttpClient();  // Add this line for HttpClient registration

            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<INotificationRepository, NotificationRepository>();
            services.AddScoped<IEmailService, EmailService>();
            services.AddScoped<IWhatsAppService, WhatsAppService>();
            services.AddScoped<INotificationTrackingService, NotificationTrackingService>();

            services.Configure<WhatsAppSettings>(whatsAppSettings =>
            {
                configuration.GetSection("WhatsAppSettings").Bind(whatsAppSettings);
            });

            return services;
        }
        public static IServiceCollection AddPdfServices(this IServiceCollection services, IConfiguration configuration)
        {
            services.Configure<PdfConfiguration>(configuration.GetSection("PdfConfiguration"));
            return services;
        }
    }
}