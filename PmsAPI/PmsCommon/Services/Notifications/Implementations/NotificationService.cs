using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using PmsCore.Notifications.Interfaces;
using PmsCore.Notifications.Models;
using System.Text;
using System.Text.Json;
using QuestPDF.Fluent;
using PmsCore.DataAccessRepository.Models;
namespace PmsCommon.Services.Notifications.Implementations
{
    public class NotificationService : INotificationService
    {
        private readonly INotificationRepository _notificationRepository;
        private readonly IEmailService _emailService;
        private readonly IWhatsAppService _whatsAppService;
        private readonly ILogger<NotificationService> _logger;
        private readonly INotificationTrackingService _trackingService;



        public NotificationService(
        INotificationRepository notificationRepository,
        IEmailService emailService,
        IWhatsAppService whatsAppService,
        ILogger<NotificationService> logger,
        INotificationTrackingService trackingService)
        {
            _notificationRepository = notificationRepository;
            _emailService = emailService;
            _whatsAppService = whatsAppService;
            _logger = logger;
            _trackingService = trackingService;
        }

        public async Task SendScheduledReport(string reportType)
        {
            try
            {
                var reportData = await GenerateReportData(reportType);
                if (reportData == null) return;

                var recipients = await _notificationRepository.GetReportRecipients("Reports", "Scheduled");
                var template = await _notificationRepository.GetNotificationTemplate("Reports", reportType);

                if (template == null)
                {
                    _logger.LogWarning("No template found for report type: {ReportType}", reportType);
                    return;
                }

                foreach (var recipient in recipients)
                {
                    try
                    {
                        if (recipient.EnableToEmail)
                        {
                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = template.Subject,
                                Body = reportData.HtmlContent,
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "Reports",
                                RecipientId = recipient.Id,
                                MessageContent = reportData.HtmlContent,
                                MessageType = reportType,
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            var whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                                template.WhatsAppProviderTemplateId,
                                recipient.MobileNumbers,
                                reportData.WhatsAppParameters
                            );

                            // Generate a unique notification message ID for WhatsApp
                            var whatsAppNotificationMessageId = $"WA-{Guid.NewGuid():N}";

                            // Track the notification for each mobile number individually
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "Reports",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = template.WhatsAppProviderTemplateId ?? 0,
                                    ProviderMessageId = whatsAppResponse?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(reportData.WhatsAppParameters),
                                    ErrorMessage = whatsAppResponse?.ErrorMessage,
                                    MessageType = reportType,
                                    Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationMessageId = whatsAppNotificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send report to recipient {RecipientId}", recipient.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send scheduled report {ReportType}", reportType);
                throw;
            }
        }
        public async Task SendSaleOrderStatusNotification(long saleOrderId, long stageId)
        {
            try
            {
                var (template, parameters) = await _notificationRepository.GetSaleOrderStatusNotification(saleOrderId, stageId);

                if (template == null)
                {
                    _logger.LogWarning("No template found for sale order status notification. OrderId: {SaleOrderId}, Stage: {StageId}",
                        saleOrderId, stageId);
                    return;
                }

                var recipients = await _notificationRepository.GetReportRecipients("SaleOrderStatus", "SaleOrderStatusUpdate");

                foreach (var recipient in recipients)
                {
                    try
                    {
                        if (recipient.EnableToEmail)
                        {
                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = template.Subject,
                                Body = BuildEmailBody(parameters),
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "SaleOrderStatus",
                                RecipientId = recipient.Id,
                                MessageContent = BuildEmailBody(parameters),
                                MessageType = "StatusUpdate",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            var whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                                template.WhatsAppProviderTemplateId.Value,
                                recipient.MobileNumbers,
                                parameters
                            );

                            // Generate a unique notification message ID for WhatsApp
                            var whatsAppNotificationMessageId = $"WA-{Guid.NewGuid():N}";

                            // Track the notification for each mobile number individually
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "SaleOrderStatus",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = template.WhatsAppProviderTemplateId ?? 0,
                                    ProviderMessageId = whatsAppResponse?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = whatsAppResponse?.ErrorMessage,
                                    MessageType = "StatusUpdate",
                                    Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = whatsAppNotificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send sale order status notification to recipient {RecipientId}. OrderId: {SaleOrderId}",
                            recipient.Id, saleOrderId);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send sale order status notification. OrderId: {SaleOrderId}, Stage: {StageId}",
                    saleOrderId, stageId);
                throw;
            }
        }
        private async Task<ReportData> GenerateReportData(string reportType)
        {
            try
            {
                var reportData = await _notificationRepository.GenerateReportData(reportType);
                if (reportData == null)
                {
                    _logger.LogWarning("No data found for report type: {ReportType}", reportType);
                    return null;
                }

                return reportData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to generate report data for type {ReportType}", reportType);
                throw;
            }
        }
        /// <summary>
        /// Sends a notification to a recipient with the specified parameters
        /// </summary>
        /// <param name="notificationType">Type of notification (e.g., "WhatsApp", "Email")</param>
        /// <param name="recipientId">ID of the recipient</param>
        /// <param name="parameters">Dictionary of parameters to include in the notification</param>
        /// <param name="messageType">Type of message (e.g., "Alert", "Report", "OnDemand")</param>
        /// <param name="templateId">ID of the template to use</param>
        /// <returns>Task representing the asynchronous operation</returns>
        public async Task SendNotification(string notificationType, long recipientId, Dictionary<string, string> parameters, string messageType, long templateId)
        {
            try
            {
                _logger.LogInformation("Sending {NotificationType} notification to recipient {RecipientId}", notificationType, recipientId);

                // Get the recipient details
                var recipient = await _notificationRepository.GetRecipientById(recipientId)
                    ?? throw new ArgumentException($"Recipient not found: {recipientId}");

                // Check rate limits
                var withinLimits = await _notificationRepository.CheckRateLimit(notificationType, recipientId);
                if (!withinLimits)
                {
                    var limits = await _notificationRepository.GetNotificationLimits(notificationType);
                    _logger.LogWarning("Rate limit exceeded for {NotificationType} notification to recipient {RecipientId}. " +
                        "Daily limit: {DailyLimit}, Monthly limit: {MonthlyLimit}",
                        notificationType, recipientId, limits.MaxDailyMessages, limits.MaxMonthlyMessages);

                    // Generate a unique notification message ID for rate limit tracking
                    var rateLimitNotificationMessageId = $"RL-{Guid.NewGuid():N}";

                    // Get the recipient details to include contact information
                    var recipientContacts = recipient.MobileNumbers.Any()
                        ? recipient.MobileNumbers.First()
                        : null;

                    // Track the rate limit exceeded event
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = notificationType,
                        RecipientId = recipientId,
                        MessageType = messageType,
                        Status = "RateLimitExceeded",
                        ErrorMessage = $"Daily/Monthly limit exceeded for {notificationType}",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = recipientId,
                        NotificationMessageId = rateLimitNotificationMessageId,
                        RecipientMobileNumber = recipientContacts,
                        RecipientEmail = recipient.EmailId
                    });

                    return; // Exit without sending notification
                }

                // Validate template access if applicable
                if (templateId > 0)
                {
                    var allowedTemplates = await _notificationRepository.GetRecipientTemplateIds(recipientId, notificationType);
                    if (!allowedTemplates.Contains(templateId))
                    {
                        _logger.LogWarning("Template {TemplateId} is not allowed for recipient {RecipientId}", templateId, recipientId);
                        throw new ArgumentException($"Template {templateId} is not allowed for recipient {recipientId}");
                    }
                }

                // Send the notification based on recipient preferences
                WhatsAppResponse whatsAppResponse = null;

                if (recipient.IsWhatsAppNotificationEnabled &&
                    (notificationType.Equals("WhatsApp", StringComparison.OrdinalIgnoreCase) ||
                     notificationType.Equals("All", StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogInformation("Sending WhatsApp notification to recipient {RecipientId}", recipient.Id);

                    whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                        templateId,
                        recipient.MobileNumbers,
                        parameters
                    );

                    // Generate a unique notification message ID for WhatsApp
                    var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                    // Track the notification for each mobile number
                    foreach (var mobileNumber in recipient.MobileNumbers)
                    {
                        await _trackingService.TrackNotification(new NotificationTrackingModel
                        {
                            NotificationType = notificationType,
                            RecipientId = recipient.Id,
                            MasterTemplateId = templateId,
                            ProviderMessageId = whatsAppResponse?.MessageId,
                            MessageContent = JsonSerializer.Serialize(parameters),
                            ErrorMessage = whatsAppResponse?.ErrorMessage,
                            MessageType = messageType,
                            Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                            SentTime = DateTime.UtcNow,
                            NotificationGroupUserId = recipient.Id,
                            NotificationMessageId = notificationMessageId,
                            RecipientMobileNumber = mobileNumber
                        });
                    }

                    if (!whatsAppResponse.Success)
                    {
                        _logger.LogError("Failed to send WhatsApp notification. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                    }
                }

                if (recipient.EnableToEmail &&
                    (notificationType.Equals("Email", StringComparison.OrdinalIgnoreCase) ||
                     notificationType.Equals("All", StringComparison.OrdinalIgnoreCase)))
                {
                    _logger.LogInformation("Sending email notification to recipient {RecipientId}", recipient.Id);

                    await _emailService.SendEmailAsync(new EmailMessage
                    {
                        To = new[] { recipient.EmailId },
                        Subject = $"{notificationType} Notification",
                        Body = BuildEmailBody(parameters),
                        IsHtml = true
                    });

                    // Generate a unique notification message ID for email
                    var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                    // Track email notification
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = notificationType,
                        RecipientId = recipient.Id,
                        MessageContent = BuildEmailBody(parameters),
                        MessageType = messageType,
                        Status = "Sent",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = recipient.Id,
                        NotificationMessageId = emailNotificationMessageId,
                        RecipientEmail = recipient.EmailId
                    });
                }

                // Note: Rate limit tracking is handled by the tracking service

                _logger.LogInformation("Successfully sent {NotificationType} notification to recipient {RecipientId}",
                    notificationType, recipientId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send {NotificationType} notification to recipient {RecipientId}",
                    notificationType, recipientId);
                throw;
            }
        }
        public async Task SendLowStockNotification(long productId)
        {
            try
            {
                var stockAvail = await _notificationRepository.GetProductStockAvailabilty(productId);
                if (stockAvail != null)
                {
                    var recipients = await _notificationRepository.GetReportRecipients("LowStock", "Event");
                    if (recipients == null)
                    {
                        _logger.LogWarning("No recipients found for low stock notification. ProductId: {ProductId}", productId);
                        return;
                    }
                    if (recipients.Any(r => r.WhatsAppTemplateMasterId == 0))
                    {
                        _logger.LogWarning("No WhatsApp template found for low stock notification. ProductId: {ProductId}", productId);
                        return;
                    }

                    foreach (var recipient in recipients.Where(r => r.NotificationType == "LowStock"))
                    {
                        try
                        {
                            var (providerTemplate, config, settings) = await _notificationRepository.GetWhatsAppTemplateAndConfig(recipient.WhatsAppTemplateMasterId);


                            WhatsAppResponse whatsAppResponse = null;
                            if (recipient.EnableToEmail)
                            {
                                var parameters = await _notificationRepository.GenerateLowStockParameters(productId, recipient.WhatsAppTemplateMasterId, stockAvail);
                                await _emailService.SendEmailAsync(new EmailMessage
                                {
                                    To = new[] { recipient.EmailId },
                                    Subject = $"Low Stock Alert - {parameters["ProductName"]}",
                                    Body = BuildEmailBody(parameters),
                                    IsHtml = true
                                });

                                // Generate a unique notification message ID for email
                                var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                                // Track email notification with the recipient's email address
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "LowStock",
                                    RecipientId = recipient.Id,
                                    MessageContent = BuildEmailBody(parameters),
                                    MessageType = "Alert",
                                    Status = "Sent", // Email status is always "Sent" at this point
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = emailNotificationMessageId,
                                    RecipientEmail = recipient.EmailId
                                });
                            }

                            if (recipient.IsWhatsAppNotificationEnabled)
                            {
                                // Use enhanced parameter collection for provider-specific handling
                                var parameterCollection = _notificationRepository.GenerateLowStockParametersEnhanced(recipient.WhatsAppTemplateMasterId, stockAvail);

                                whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                                    recipient.WhatsAppTemplateMasterId,
                                    recipient.MobileNumbers,
                                    parameterCollection
                                );
                                if (!whatsAppResponse.Success)
                                {
                                    _logger.LogError("Failed to send WhatsApp template message. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                                }

                                // Generate a unique notification message ID for WhatsApp
                                var whatsAppNotificationMessageId = $"WA-{Guid.NewGuid():N}";

                                // Track the notification for each mobile number individually
                                foreach (var mobileNumber in recipient.MobileNumbers)
                                {
                                    await _trackingService.TrackNotification(new NotificationTrackingModel
                                    {
                                        NotificationType = "LowStock",
                                        RecipientId = recipient.Id,
                                        MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                        ProviderMessageId = whatsAppResponse?.MessageId,
                                        MessageContent = JsonSerializer.Serialize(parameterCollection),
                                        ErrorMessage = whatsAppResponse?.ErrorMessage,
                                        MessageType = "Alert",
                                        Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                                        SentTime = DateTime.UtcNow,
                                        NotificationGroupUserId = recipient.Id,
                                        NotificationMessageId = whatsAppNotificationMessageId,
                                        RecipientMobileNumber = mobileNumber
                                    });
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Failed to send low stock notification to recipient {RecipientId}", recipient.Id);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send low stock notification for ProductId: {ProductId}", productId);
                throw;
            }
        }
        public async Task SendYieldReportSummaryWhatsApp(DateTime fromDate, DateTime toDate, string TriggerType)
        {
            try
            {
                _logger.LogInformation("Generating and sending yield report summary for period {FromDate} to {ToDate}",
                    fromDate.ToString("yyyy-MM-dd"), toDate.ToString("yyyy-MM-dd"));

                // Generate the PDF and get the URL
                var pdfUrl = await _notificationRepository.GenerateYieldSummaryReportPdfAndUploadToStorageAsync(fromDate, toDate);
                if (pdfUrl == null)
                {
                    _logger.LogWarning("Failed to generate yield report PDF");
                    return;
                }

                _logger.LogInformation("Successfully generated yield report PDF: {PdfUrl}", pdfUrl);

                // Get recipients for YieldReportSummary notification type
                var recipients = await _notificationRepository.GetReportRecipients("YieldReportSummary", TriggerType);
                if (recipients == null || !recipients.Any())
                {
                    _logger.LogWarning("No recipients found for yield report summary notification");
                    return;
                }

                // Send to each recipient
                foreach (var recipient in recipients)
                {
                    try
                    {
                        //Fetch parameters from template parameter master
                        var parameters = await _notificationRepository.GenerateYieldSummaryReportParameters(recipient.WhatsAppTemplateMasterId, fromDate, toDate);
                        if (parameters == null)
                        {
                            _logger.LogWarning("Failed to generate parameters for yield report summary notification");
                            continue;
                        }

                        if (recipient.IsWhatsAppNotificationEnabled)
                        {
                            _logger.LogInformation("Sending yield report to recipient {RecipientId} via WhatsApp", recipient.Id);

                            var response = await _whatsAppService.SendTemplateDocumentMessageAsync(
                                recipient.WhatsAppTemplateMasterId,
                                recipient.MobileNumbers,
                                parameters,
                                pdfUrl
                            );

                            // Generate a unique notification message ID for WhatsApp
                            var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                            // Track the notification for each mobile number
                            foreach (var mobileNumber in recipient.MobileNumbers)
                            {
                                await _trackingService.TrackNotification(new NotificationTrackingModel
                                {
                                    NotificationType = "YieldReportSummary",
                                    RecipientId = recipient.Id,
                                    MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                                    ProviderMessageId = response?.MessageId,
                                    MessageContent = JsonSerializer.Serialize(parameters),
                                    ErrorMessage = response?.ErrorMessage,
                                    MessageType = "Report",
                                    Status = response?.Success ?? false ? "Sent" : "Failed",
                                    SentTime = DateTime.UtcNow,
                                    NotificationGroupUserId = recipient.Id,
                                    NotificationMessageId = notificationMessageId,
                                    RecipientMobileNumber = mobileNumber
                                });
                            }

                            if (!response.Success)
                            {
                                _logger.LogError("Failed to send WhatsApp document message. Error: {ErrorMessage}", response.ErrorMessage);
                            }
                        }

                        // Also send via email if enabled
                        if (recipient.EnableToEmail)
                        {
                            _logger.LogInformation("Sending yield report to recipient {RecipientId} via Email", recipient.Id);

                            await _emailService.SendEmailAsync(new EmailMessage
                            {
                                To = new[] { recipient.EmailId },
                                Subject = $"Yield Report Summary ({fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy})",
                                Body = $"<p>Please find the Yield Report Summary for the period {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}.</p>" +
                                       $"<p>You can download the report from <a href='{pdfUrl}'>here</a>.</p>",
                                IsHtml = true
                            });

                            // Generate a unique notification message ID for email
                            var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                            // Track email notification
                            await _trackingService.TrackNotification(new NotificationTrackingModel
                            {
                                NotificationType = "YieldReportSummary",
                                RecipientId = recipient.Id,
                                MessageContent = $"Yield Report Summary for {fromDate:dd-MMM-yyyy} to {toDate:dd-MMM-yyyy}",
                                MessageType = "Report",
                                Status = "Sent",
                                SentTime = DateTime.UtcNow,
                                NotificationGroupUserId = recipient.Id,
                                NotificationMessageId = emailNotificationMessageId,
                                RecipientEmail = recipient.EmailId
                            });
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to send yield report to recipient {RecipientId}", recipient.Id);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send yield report WhatsApp notification");
                throw;
            }
        }



        public async Task TriggerOnDemandNotification(long notificationGroupId, Dictionary<string, string> parameters)
        {
            try
            {
                _logger.LogInformation("Triggering on-demand notification for group {NotificationGroupId}", notificationGroupId);

                // Get the notification group details
                var recipient = await _notificationRepository.GetRecipientById(notificationGroupId)
                    ?? throw new ArgumentException($"Notification group not found: {notificationGroupId}");

                // Verify this is an on-demand notification
                // The TriggerType might be stored in NotificationType or as a separate property
                // depending on the database schema
                var triggerType = recipient.NotificationType;
                if (triggerType != "OnDemand" && !triggerType.Contains("OnDemand"))
                {
                    _logger.LogWarning("Notification group {NotificationGroupId} is not configured for on-demand notifications. Type: {TriggerType}",
                        notificationGroupId, triggerType);
                    throw new ArgumentException($"Notification group {notificationGroupId} is not configured for on-demand notifications");
                }

                // Send the notification based on recipient preferences
                WhatsAppResponse whatsAppResponse = null;

                if (recipient.IsWhatsAppNotificationEnabled)
                {
                    _logger.LogInformation("Sending on-demand WhatsApp notification to recipient {RecipientId}", recipient.Id);

                    whatsAppResponse = await _whatsAppService.SendTemplateMessageAsync(
                        recipient.WhatsAppTemplateMasterId,
                        recipient.MobileNumbers,
                        parameters
                    );

                    // Generate a unique notification message ID for WhatsApp
                    var notificationMessageId = $"WA-{Guid.NewGuid():N}";

                    // Track the notification for each mobile number
                    foreach (var mobileNumber in recipient.MobileNumbers)
                    {
                        await _trackingService.TrackNotification(new NotificationTrackingModel
                        {
                            NotificationType = "OnDemand",
                            RecipientId = recipient.Id,
                            MasterTemplateId = recipient.WhatsAppTemplateMasterId,
                            ProviderMessageId = whatsAppResponse?.MessageId,
                            MessageContent = JsonSerializer.Serialize(parameters),
                            ErrorMessage = whatsAppResponse?.ErrorMessage,
                            MessageType = "OnDemand",
                            Status = whatsAppResponse?.Success ?? false ? "Sent" : "Failed",
                            SentTime = DateTime.UtcNow,
                            NotificationGroupUserId = notificationGroupId,
                            NotificationMessageId = notificationMessageId,
                            RecipientMobileNumber = mobileNumber
                        });
                    }

                    if (!whatsAppResponse.Success)
                    {
                        _logger.LogError("Failed to send on-demand WhatsApp notification. Error: {ErrorMessage}", whatsAppResponse.ErrorMessage);
                    }
                }

                if (recipient.EnableToEmail)
                {
                    _logger.LogInformation("Sending on-demand email notification to recipient {RecipientId}", recipient.Id);

                    await _emailService.SendEmailAsync(new EmailMessage
                    {
                        To = new[] { recipient.EmailId },
                        Subject = "On-Demand Notification",
                        Body = BuildEmailBody(parameters),
                        IsHtml = true
                    });

                    // Generate a unique notification message ID for email
                    var emailNotificationMessageId = $"EM-{Guid.NewGuid():N}";

                    // Track email notification
                    await _trackingService.TrackNotification(new NotificationTrackingModel
                    {
                        NotificationType = "OnDemand",
                        RecipientId = recipient.Id,
                        MessageContent = BuildEmailBody(parameters),
                        MessageType = "OnDemand",
                        Status = "Sent",
                        SentTime = DateTime.UtcNow,
                        NotificationGroupUserId = notificationGroupId,
                        NotificationMessageId = emailNotificationMessageId,
                        RecipientEmail = recipient.EmailId
                    });
                }

                // Update the LastTriggeredBy and LastTriggeredDate in the database
                await _notificationRepository.UpdateOnDemandNotificationStatus(notificationGroupId);

                _logger.LogInformation("Successfully triggered on-demand notification for group {NotificationGroupId}", notificationGroupId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to trigger on-demand notification for group {NotificationGroupId}", notificationGroupId);
                throw;
            }
        }

        private string BuildEmailBody(Dictionary<string, string> parameters)
        {
            try
            {
                var sb = new StringBuilder();
                sb.Append("<html><body>");
                sb.Append("<h2>Notification Details</h2>");
                sb.Append("<table border='1' cellpadding='5' cellspacing='0' style='border-collapse: collapse;'>");
                sb.Append("<tr><th>Parameter</th><th>Value</th></tr>");

                foreach (var param in parameters)
                {
                    sb.Append("<tr>");
                    sb.Append($"<td>{param.Key}</td>");

                    // Check if the value is a URL
                    if (Uri.TryCreate(param.Value, UriKind.Absolute, out Uri uriResult) &&
                        (uriResult.Scheme == Uri.UriSchemeHttp || uriResult.Scheme == Uri.UriSchemeHttps))
                    {
                        sb.Append($"<td><a href='{param.Value}'>View/Download</a></td>");
                    }
                    else
                    {
                        sb.Append($"<td>{param.Value}</td>");
                    }

                    sb.Append("</tr>");
                }

                sb.Append("</table>");
                sb.Append("<p>This is an automated notification from the PMS system.</p>");
                sb.Append("</body></html>");

                return sb.ToString();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error building email body");
                return "<p>Notification details could not be displayed.</p>";
            }
        }
    }
}
