# PMS (Production Management System) Architecture Documentation

## Table of Contents

1. [Project Overview](#project-overview)
2. [Solution Structure](#solution-structure)
3. [Project Details](#project-details)
4. [Architectural Principles](#architectural-principles)
5. [System Architecture](#system-architecture)
6. [Technology Stack](#technology-stack)
7. [Development Guidelines](#development-guidelines)
8. [Integration Points](#integration-points)

## Project Overview

The Production Management System (PMS) is a comprehensive enterprise solution designed for Zaibunco's Rexine Division. It manages the complete manufacturing lifecycle including inventory management, production planning, sales/purchase operations, logistics, reporting, and administration for a rexine manufacturing business.

### Key Business Domains

- **Inventory Management**: Stock tracking, product allocation, inspection workflows
- **Manufacturing**: Production planning, yield tracking, quality control
- **Sales & Purchase**: Order management, pricing, customer relations
- **Logistics**: Gate management, transportation, delivery tracking
- **Reporting**: Yield reports, production analytics, business intelligence
- **Administration**: User management, configuration, audit trails

## Solution Structure

The PMS solution follows a layered architecture with clear separation of concerns:

```
PMS Solution
├── PmsAPI (Azure Functions - API Layer)
├── PmsBusiness (Business Logic Layer)
├── PmsData (Data Access Layer)
├── PmsEntity (View Models & DTOs)
├── PmsCommon (Common Utilities & Services)
├── PmsCore (Models & Interfaces)
├── PmsReportingAPI (Dedicated Reporting Functions)
├── PMSDatabase (Database Project)
└── PmsUI (Angular Frontend)
```

## Project Details

### 1. PmsAPI (Azure Functions)

**Purpose**: Serverless API layer providing HTTP endpoints for the frontend and external integrations.

**Key Components**:

- Azure Functions with HTTP triggers
- OpenAPI documentation support
- Authentication and authorization
- Request/response handling
- Scheduled functions (timers)

**Dependencies**: PmsBusiness, PmsCommon, PmsEntity, PmsData, PmsCore

**Technology Stack**:

- .NET 6.0
- Azure Functions v4
- Microsoft.Azure.Functions.Worker
- OpenAPI/Swagger integration
- Application Insights for telemetry

**Design Patterns**:

- Dependency Injection
- Repository Pattern (via interfaces)
- Service Layer Pattern

### 2. PmsBusiness (Business Logic Layer)

**Purpose**: Contains all business logic and orchestrates operations between the API and data layers.

**Key Components**:

- Business function classes (e.g., `StorageFunctions`, `IssueProductFunctions`)
- Business rule validation
- Workflow orchestration
- Cross-cutting concerns coordination

**Dependencies**: PmsCommon, PmsData, PmsEntity

**Responsibilities**:

- Business rule enforcement
- Data transformation and validation
- Workflow coordination
- Service orchestration

**Example Classes**:

```csharp
public class StorageFunctions
{
    private readonly GlobalDataEntity _globalData;

    public StorageTokenVm GetStorageContainerToken(string containername)
    {
        var data = new StorageDataFn(_globalData);
        return data.GetStorageContainerToken(containername);
    }
}
```

### 3. PmsData (Data Access Layer)

**Purpose**: Handles all database operations and external data source interactions.

**Key Components**:

- Entity Framework DbContext (`pmsdbContext`)
- Data access functions (DataFn classes)
- Repository implementations
- Database entity models

**Dependencies**: PmsCommon, PmsEntity

**Technology Stack**:

- Entity Framework Core 6.0
- SQL Server
- Microsoft.Data.SqlClient
- LINQ for data queries

**Design Patterns**:

- Repository Pattern
- Unit of Work Pattern
- Database-First Approach

**Example Structure**:

```csharp
public class CommonDataFn
{
    public GlobalDataEntity GlobalData;

    public List<MeasureUnitMasterVm> GetMeasureUnits()
    {
        using (var db = new pmsdbContext())
        {
            return db.MeasureUnitMasters
                .Where(a => a.Disabled == false)
                .Select(a => new MeasureUnitMasterVm { ... })
                .ToList();
        }
    }
}
```

### 4. PmsEntity (View Models & DTOs)

**Purpose**: Contains data transfer objects, view models, and request/response models.

**Key Components**:

- View Models for UI binding
- API request/response models
- Data transfer objects
- Validation attributes

**Dependencies**: PmsCommon

**Responsibilities**:

- Data contract definitions
- Input validation
- Type safety between layers

### 5. PmsCommon (Common Utilities & Services)

**Purpose**: Shared utilities, services, and common functionality used across all layers.

**Key Components**:

- `CommonFunctions` - Utility methods
- `GlobalDataEntity` - Global state management
- PDF generation services
- Storage services
- Notification services

**Dependencies**: PmsCore

**Technology Stack**:

- QuestPDF for PDF generation
- Azure Storage SDK
- RestSharp for HTTP clients
- MimeKit for email handling

**Critical Rule**: **PmsCommon must never interact directly with the database** to avoid circular dependencies with PmsData.

### 6. PmsCore (Models & Interfaces)

**Purpose**: Core domain models, interfaces, and contracts that define the system's architecture.

**Key Components**:

- Service interfaces (`INotificationService`, `IPdfService`)
- Repository interfaces (`IDataAccessRepository`, `IStorageRepository`)
- Domain models and DTOs
- Configuration models

**Dependencies**: None (pure interfaces and models)

**Example Interfaces**:

```csharp
public interface INotificationService
{
    Task SendSaleOrderStatusNotification(long saleOrderId, long stageId);
    Task SendLowStockNotification(long productId);
    Task SendScheduledReport(string reportType);
}

public interface IPdfService
{
    Task<byte[]> GeneratePdfAsync(IPdfDocumentData data);
    Task<string> GeneratePdfAndUploadToStorageAsync(IPdfDocumentData data, string fileName);
}
```

### 7. PmsReportingAPI (Dedicated Reporting Functions)

**Purpose**: Specialized Azure Functions for reporting and analytics operations.

**Key Components**:

- Report generation functions
- Scheduled report processing
- Data export capabilities
- Analytics endpoints

**Dependencies**: Similar to PmsAPI

### 8. PMSDatabase (Database Project)

**Purpose**: SQL Server database project for schema management and deployment.

**Key Components**:

- Table definitions
- Stored procedures
- Views and functions
- Database deployment scripts

**Technology Stack**:

- SQL Server Azure V12
- Microsoft.Build.Sql SDK

## Architectural Principles

### 1. Separation of Concerns

Each layer has distinct responsibilities:

- **API Layer**: HTTP handling, authentication, request routing
- **Business Layer**: Business logic, validation, workflow orchestration
- **Data Layer**: Database operations, data persistence
- **Common Layer**: Shared utilities (no database access)
- **Core Layer**: Interfaces and contracts

### 2. Database-First Approach

The system uses Entity Framework's database-first approach:

1. Database changes are made directly in SQL Server
2. Entity Framework scaffolding commands sync changes to code
3. DbContext and entity models are auto-generated
4. Manual modifications to generated code are avoided

**Scaffolding Command Example**:

```bash
Scaffold-DbContext "Server=...;Database=pmsdb;..." Microsoft.EntityFrameworkCore.SqlServer -OutputDir Models -Force
```

### 3. Dependency Rules

**Allowed Dependencies**:

- API → Business → Data
- API → Common, Core
- Business → Common, Data, Entity
- Data → Common, Entity
- Common → Core (only)

**Forbidden Dependencies**:

- Common → Data (prevents circular dependencies)
- Core → Any other project (maintains purity)
- Data → Business (maintains layering)

### 4. Business Logic Separation

- **Business rules** reside only in PmsBusiness
- **Data access logic** stays in PmsData
- **API logic** limited to HTTP concerns in PmsAPI
- **Common utilities** provide shared functionality without business logic

## System Architecture

### Layer Dependencies Diagram

```
┌─────────────────┐    ┌─────────────────┐
│   PmsAPI        │    │ PmsReportingAPI │
│ (Azure Functions)│    │ (Azure Functions)│
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────────────────────────────┐
│           PmsBusiness                   │
│        (Business Logic)                 │
└─────────┬───────────────────────────────┘
          │
          ▼
┌─────────────────┐    ┌─────────────────┐
│    PmsData      │    │   PmsCommon     │
│ (Data Access)   │    │  (Utilities)    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          ▼                      ▼
┌─────────────────┐    ┌─────────────────┐
│   PmsEntity     │    │    PmsCore      │
│ (View Models)   │    │ (Interfaces)    │
└─────────────────┘    └─────────────────┘
```

### Data Flow

1. **Request Flow**: Frontend → PmsAPI → PmsBusiness → PmsData → Database
2. **Response Flow**: Database → PmsData → PmsBusiness → PmsAPI → Frontend
3. **Cross-cutting**: PmsCommon provides utilities at each layer
4. **Contracts**: PmsCore defines interfaces used throughout

### Integration Points

#### External Services

- **Azure Storage**: Document and file storage
- **Azure Communication Services**: WhatsApp messaging
- **Azure Key Vault**: Configuration and secrets
- **Application Insights**: Telemetry and monitoring
- **Azure Active Directory**: Authentication and authorization

#### Frontend Integration

- **Angular Application**: TypeScript/Angular frontend
- **HTTP API**: RESTful communication with Azure Functions
- **Azure Blob Storage**: Direct file uploads and downloads
- **MSAL Authentication**: Azure AD integration

## Technology Stack

### Backend (.NET 6.0)

- **Runtime**: .NET 6.0
- **Hosting**: Azure Functions v4
- **Database**: Entity Framework Core 6.0 + SQL Server
- **Authentication**: Azure Active Directory
- **Storage**: Azure Blob Storage
- **Messaging**: Azure Communication Services
- **PDF Generation**: QuestPDF
- **Scheduling**: Cronos (cron expressions)

### Frontend (Angular)

- **Framework**: Angular (version to be upgraded from 15 to 19)
- **UI Library**: Ant Design (ng-zorro)
- **Authentication**: MSAL Angular
- **HTTP Client**: Angular HttpClient
- **State Management**: Services with RxJS

### Database

- **Engine**: SQL Server (Azure SQL Database)
- **ORM**: Entity Framework Core 6.0
- **Approach**: Database-First with scaffolding
- **Migration**: Direct database changes + scaffolding

### DevOps & Infrastructure

- **Cloud Platform**: Microsoft Azure
- **CI/CD**: Azure DevOps (implied)
- **Monitoring**: Application Insights
- **Configuration**: Azure Key Vault
- **Storage**: Azure Blob Storage

## Development Guidelines

### 1. Coding Standards

- Follow C# naming conventions
- Use dependency injection throughout
- Implement proper error handling and logging
- Write unit tests for business logic
- Document public APIs with XML comments

### 2. Database Changes Process

1. Make changes directly in SQL Server database
2. Test changes in development environment
3. Run Entity Framework scaffolding command:
   ```bash
   Scaffold-DbContext "ConnectionString" Microsoft.EntityFrameworkCore.SqlServer -OutputDir Models -Force
   ```
4. Review generated changes
5. Update any dependent code
6. Deploy database changes to higher environments

### 3. Testing Strategy

- **Unit Tests**: Business logic in PmsBusiness
- **Integration Tests**: API endpoints and data access
- **End-to-End Tests**: Critical user workflows
- **Performance Tests**: Report generation and data processing

### 4. Security Guidelines

- Use Azure AD for authentication
- Store secrets in Azure Key Vault
- Implement proper authorization checks
- Validate all inputs
- Use HTTPS for all communications
- Follow OWASP security guidelines

### 5. Performance Considerations

- Use async/await for I/O operations
- Implement proper caching strategies
- Optimize database queries
- Use pagination for large datasets
- Monitor performance with Application Insights

## Design Patterns Implementation

### 1. Repository Pattern

**Implementation**: Interfaces in PmsCore, implementations in PmsData

```csharp
// Interface in PmsCore
public interface IDataAccessRepository
{
    Task<PdfConfiguration> GetPdfConfiguration();
    Task<object> GetPurchaseOrderById(long id);
}

// Implementation in PmsData
public class CommonDataAccessFn : IDataAccessRepository
{
    public async Task<object> GetPurchaseOrderById(long id)
    {
        PurchaseOrderDataFn purchaseOrderDataFn = new(_globalData, null);
        return purchaseOrderDataFn.GetPurchaseOrderById(id);
    }
}
```

### 2. Dependency Injection Pattern

**Configuration in Program.cs**:

```csharp
services.AddScoped<IDataAccessRepository, CommonDataAccessFn>();
services.AddScoped<INotificationService, NotificationService>();
services.AddScoped<IPdfService, QuestPdfService>();
```

### 3. Factory Pattern

**PDF Document Factory**:

```csharp
private IDocument GetDocumentByType(IPdfDocumentData data)
{
    return data switch
    {
        IPurchaseOrderPdfData po => new PurchaseOrderDocument(po, _config, _logger),
        IYieldReportSummeryModel yr => new YieldReportSummaryDocument(yr.AllRecords.ToList(), _config, yr.FromDate, yr.ToDate),
        _ => throw new ArgumentException($"Unsupported document type: {data.GetType().Name}")
    };
}
```

### 4. Strategy Pattern

**Notification Service Providers**:

- Multiple WhatsApp service providers (Azure Communication Services, Brevo)
- Provider-specific implementations with common interface
- Runtime provider selection based on configuration

## Data Access Patterns

### 1. Entity Framework DbContext

**Database-First Approach**:

```csharp
public partial class pmsdbContext : DbContext
{
    public virtual DbSet<SaleOrderTable> SaleOrderTables { get; set; }
    public virtual DbSet<ProductMaster> ProductMasters { get; set; }
    public virtual DbSet<StockProductTable> StockProductTables { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        // Auto-generated entity configurations
    }
}
```

### 2. Data Access Functions Pattern

**Consistent Data Access Layer**:

```csharp
public class CommonDataFn
{
    public GlobalDataEntity GlobalData;

    public CommonDataFn(GlobalDataEntity gd)
    {
        GlobalData = gd;
    }

    public List<MeasureUnitMasterVm> GetMeasureUnits()
    {
        using (var db = new pmsdbContext())
        {
            return db.MeasureUnitMasters
                .Where(a => a.Disabled == false)
                .Select(a => new MeasureUnitMasterVm { ... })
                .ToList();
        }
    }
}
```

### 3. Global Data Entity Pattern

**Shared Context Across Layers**:

```csharp
public class GlobalDataEntity
{
    public string loggedInUser { get; set; }
    public long UserId { get; set; }
    public string UserRole { get; set; }
    // Other global state properties
}
```

## Security Architecture

### 1. Authentication Flow

1. **Frontend**: MSAL Angular handles Azure AD authentication
2. **Token Acquisition**: JWT tokens obtained from Azure AD
3. **API Authorization**: Bearer tokens validated in Azure Functions
4. **Token Refresh**: Automatic token refresh with MSAL interceptor

### 2. Authorization Model

**Role-Based Access Control**:

```typescript
// Frontend role checking
CheckModule(Module: string) {
    if (UserInfo.MyRoles?.filter(x => x.Module.includes(Module) && x.IsChecked).length > 0) {
        return true;
    }
    return false;
}

CheckResponsibility(Module: string, Responsibility: string) {
    return UserInfo.MyRoles?.filter(x => x.Module == Module)[0]?.Responsibilities?.filter(y => y.ResponsibilityName == Responsibility)[0]?.IsChecked
}
```

### 3. Data Protection

- **Secrets Management**: Azure Key Vault for connection strings and API keys
- **Data Encryption**: TLS 1.2+ for data in transit
- **Database Security**: SQL Server encryption at rest
- **Access Control**: Principle of least privilege

## Notification System Architecture

### 1. Notification Types

- **Event-Based**: Triggered by system events (low stock, order status changes)
- **Schedule-Based**: Cron-scheduled reports and alerts
- **On-Demand**: User-initiated notifications

### 2. Multi-Provider Support

**WhatsApp Service Abstraction**:

```csharp
public interface IWhatsAppService
{
    Task<WhatsAppResponse> SendTemplateMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters);
    Task<WhatsAppResponse> SendTemplateDocumentMessageAsync(long? templateId, List<string> mobileNumbers, Dictionary<string, string> parameters, string documentUrl);
}
```

### 3. Notification Tracking

- **Unique Message IDs**: Channel-specific prefixes (WA-, EM-)
- **Delivery Status**: Track sent, delivered, read status
- **Rate Limiting**: Prevent notification spam
- **Audit Trail**: Complete notification history

## Reporting Architecture

### 1. PDF Generation System

**QuestPDF Integration**:

```csharp
public class QuestPdfService : IPdfService
{
    public async Task<byte[]> GeneratePdfAsync(IPdfDocumentData data)
    {
        var document = GetDocumentByType(data);
        return document.GeneratePdf();
    }

    public async Task<string> GeneratePdfAndUploadToWhatsAppStorageAsync(IPdfDocumentData data, string fileName, string documentType)
    {
        var pdfBytes = await GeneratePdfAsync(data);
        var blobUri = CommonFunctions.UploadToBlob(new MemoryStream(pdfBytes), fileName, documentType, appendSasToken: false);
        return blobUri;
    }
}
```

### 2. Report Scheduling

**Cron-Based Scheduling**:

```csharp
public async Task ProcessYieldReportNotifications()
{
    var yieldReportSchedules = await _dbContext.NotificationReportScheduleMappingTables
        .Where(s => s.IsActive && !s.Disabled && s.ReportType == "YieldReportSummary")
        .ToListAsync();

    foreach (var schedule in yieldReportSchedules)
    {
        var cronExpression = CronExpression.Parse(schedule.CronExpression);
        // Process scheduling logic
    }
}
```

### 3. Document Storage Strategy

**WhatsApp-Compatible Storage**:

- **Public Container**: Anonymous read access for WhatsApp compatibility
- **Hierarchical Organization**: `/documenttype/YYYY/MM/DD/filename.pdf`
- **No SAS Tokens**: Direct blob URLs for WhatsApp document delivery
- **Automatic Cleanup**: Lifecycle policies for temporary documents

## Performance Optimization

### 1. Database Optimization

- **Indexing Strategy**: Composite indexes on frequently queried columns
- **Query Optimization**: LINQ query analysis and optimization
- **Connection Pooling**: Entity Framework connection management
- **Async Operations**: Non-blocking database operations

### 2. Caching Strategy

- **Application-Level Caching**: In-memory caching for reference data
- **HTTP Caching**: Browser caching for static resources
- **Azure CDN**: Content delivery network for global performance

### 3. Scalability Considerations

- **Serverless Architecture**: Azure Functions auto-scaling
- **Database Scaling**: Azure SQL Database elastic pools
- **Storage Scaling**: Azure Blob Storage automatic scaling
- **Load Distribution**: Geographic distribution capabilities

## Monitoring and Observability

### 1. Application Insights Integration

```csharp
services.AddApplicationInsightsTelemetryWorkerService();
services.AddLogging(builder =>
{
    builder.AddConsole();
    builder.AddApplicationInsights();
});
```

### 2. Logging Strategy

- **Structured Logging**: JSON-formatted logs with correlation IDs
- **Log Levels**: Appropriate use of Debug, Info, Warning, Error
- **Performance Metrics**: Request duration, database query times
- **Business Metrics**: Order processing times, yield calculations

### 3. Health Monitoring

- **Function Health**: Azure Functions monitoring and alerting
- **Database Health**: SQL Server performance monitoring
- **External Service Health**: WhatsApp service availability
- **Storage Health**: Blob storage performance metrics

## Deployment Architecture

### 1. Environment Strategy

- **Development**: Local development with Azure services
- **Staging**: Pre-production environment for testing
- **Production**: Live environment with high availability

### 2. Configuration Management

**Environment-Specific Settings**:

```typescript
// Frontend environment configuration
export const environment = {
  production: false,
  Api_Url: "http://localhost:7071/api/",
  BlobURL: "https://pmsdocumentstordev.blob.core.windows.net/",
  DivisionName: "Local Rexine Division",
};
```

### 3. Database Deployment

- **Schema Versioning**: SQL Server Database Project
- **Migration Scripts**: Automated database updates
- **Rollback Strategy**: Database backup and restore procedures

This comprehensive architecture provides a robust, scalable, and maintainable foundation for the PMS system while supporting complex manufacturing management requirements.
