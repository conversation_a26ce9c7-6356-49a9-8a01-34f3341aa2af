# PMS Backend API Debugging Guide

This guide provides comprehensive instructions for debugging both Azure Functions projects in the PMS Backend API repository.

## Project Overview

The PMS Backend API consists of two Azure Functions projects:

1. **PmsAPI** (Main API) - Port 7071
   - Location: `PmsAPI/PmsAPI/`
   - Contains main business logic functions
   - Handles core PMS operations

2. **PmsReportingAPI** (Reporting API) - Port 7072
   - Location: `PmsAPI/PmsReportingAPI/`
   - Contains reporting and dashboard functions
   - Handles report generation and analytics

## Prerequisites

Before debugging, ensure you have:

1. **.NET 6.0 SDK** installed
2. **Azure Functions Core Tools** installed (`npm install -g azure-functions-core-tools@4`)
3. **Visual Studio Code** with C# extension
4. **Azure Functions extension** for VS Code

## Available Debug Configurations

### Individual Project Debugging

#### 1. Debug PmsAPI (Main API)
- **Configuration**: "Debug PmsAPI with Azure Functions Core Tools"
- **Port**: 7071
- **Usage**: Select this configuration and press F5
- **URL**: http://localhost:7071/api

#### 2. Debug PmsReportingAPI
- **Configuration**: "Debug PmsReportingAPI with Azure Functions Core Tools"
- **Port**: 7072
- **Usage**: Select this configuration and press F5
- **URL**: http://localhost:7072/api

### Parallel Debugging (Recommended)

#### 3. Debug Both APIs Simultaneously
- **Configuration**: "Debug Both APIs (Parallel)"
- **Ports**: 7071 (PmsAPI) + 7072 (PmsReportingAPI)
- **Usage**: Select this compound configuration and press F5
- **Benefits**: 
  - Test interactions between both APIs
  - Full system debugging
  - No port conflicts

#### 4. Full Stack Debugging
- **Configuration**: "Debug All (APIs + Frontend)"
- **Includes**: Both APIs + Chrome browser for frontend
- **Usage**: Select this compound configuration and press F5

### Process Attachment

#### 5. Attach to Running Process
- **Configurations**: 
  - "Attach to PmsAPI Process"
  - "Attach to PmsReportingAPI Process"
- **Usage**: For attaching to already running processes

## Step-by-Step Debugging Instructions

### Option 1: Debug Single Project

1. **Open VS Code** in the `pms-backend-api` directory
2. **Set breakpoints** in your code
3. **Open Debug Panel** (Ctrl+Shift+D)
4. **Select configuration**:
   - "Debug PmsAPI with Azure Functions Core Tools" for main API
   - "Debug PmsReportingAPI with Azure Functions Core Tools" for reporting API
5. **Press F5** or click the green play button
6. **Wait for startup** - you'll see console output indicating the function host is running
7. **Test your endpoints** using your preferred HTTP client

### Option 2: Debug Both Projects Simultaneously (Recommended)

1. **Open VS Code** in the `pms-backend-api` directory
2. **Set breakpoints** in both projects as needed
3. **Open Debug Panel** (Ctrl+Shift+D)
4. **Select "Debug Both APIs (Parallel)"**
5. **Press F5** or click the green play button
6. **Monitor both terminals** - you'll see two separate console outputs
7. **Test endpoints** on both ports:
   - PmsAPI: http://localhost:7071/api
   - PmsReportingAPI: http://localhost:7072/api

## Available Build Tasks

### Individual Project Tasks
- `build (PmsAPI)` - Build main API project
- `build (PmsReportingAPI)` - Build reporting API project
- `clean (PmsAPI)` - Clean main API project
- `clean (PmsReportingAPI)` - Clean reporting API project

### Combined Tasks
- `build (All Projects)` - Build both projects in parallel
- `clean (All Projects)` - Clean both projects in parallel

### Legacy Tasks (Backward Compatibility)
- `build (functions)` - Legacy build task for PmsAPI
- `clean (functions)` - Legacy clean task for PmsAPI

## Troubleshooting

### Common Issues and Solutions

#### 1. Port Already in Use
**Error**: "Port 7071 is already in use"
**Solution**: 
- Kill existing processes: `taskkill /f /im func.exe` (Windows) or `pkill func` (Mac/Linux)
- Or use different ports in local.settings.json

#### 2. Build Failures
**Error**: Build errors during debug startup
**Solution**:
- Run `dotnet clean` and `dotnet build` manually
- Check for missing dependencies
- Ensure .NET 6.0 SDK is installed

#### 3. Breakpoints Not Hit
**Error**: Breakpoints are not being triggered
**Solution**:
- Ensure you're using the correct debug configuration
- Verify the project is built in Debug mode
- Check that the debugger is attached properly

#### 4. Function Host Startup Issues
**Error**: Azure Functions host fails to start
**Solution**:
- Check local.settings.json configuration
- Verify Azure Functions Core Tools installation
- Review console output for specific error messages

### Environment Variables

Both projects use these environment variables:
- `FUNCTIONS_WORKER_RUNTIME`: "dotnet-isolated"
- `ASPNETCORE_ENVIRONMENT`: "Development"

Additional configuration is loaded from:
- `local.settings.json` in each project directory
- Azure Key Vault (for production settings)

## Testing Endpoints

### PmsAPI (Port 7071)
- Health Check: `GET http://localhost:7071/api/health`
- Swagger UI: `GET http://localhost:7071/api/swagger/ui`

### PmsReportingAPI (Port 7072)
- Health Check: `GET http://localhost:7072/api/health`
- Dashboard: `GET http://localhost:7072/api/dashboard`

## Performance Tips

1. **Use Parallel Debugging** for full system testing
2. **Set specific breakpoints** rather than stepping through entire flows
3. **Use conditional breakpoints** for specific scenarios
4. **Monitor memory usage** during long debugging sessions
5. **Restart debug sessions** periodically to clear memory

## Advanced Debugging

### Debugging with External Tools
- Use **Postman** or **Thunder Client** for API testing
- Use **Application Insights** for production debugging
- Use **Azure Storage Explorer** for storage-related issues

### Logging and Monitoring
- Console logs appear in VS Code terminal
- Application Insights logs (when configured)
- Custom logging through ILogger interface

## Quick Reference

| Action | Shortcut | Description |
|--------|----------|-------------|
| Start Debugging | F5 | Start selected debug configuration |
| Stop Debugging | Shift+F5 | Stop all debugging sessions |
| Restart Debugging | Ctrl+Shift+F5 | Restart current debug session |
| Toggle Breakpoint | F9 | Add/remove breakpoint at current line |
| Step Over | F10 | Execute next line without entering functions |
| Step Into | F11 | Enter function calls |
| Step Out | Shift+F11 | Exit current function |

## Support

For additional support:
1. Check the console output for detailed error messages
2. Review the Azure Functions documentation
3. Consult the PMS project documentation
4. Contact the development team for project-specific issues
