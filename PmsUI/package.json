{"name": "pms-ui", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@amcharts/amcharts5": "^5.2.2", "@angular/animations": "^15.2.2", "@angular/common": "^15.2.2", "@angular/compiler": "^15.2.2", "@angular/core": "^15.2.2", "@angular/forms": "^15.2.2", "@angular/platform-browser": "^15.2.2", "@angular/platform-browser-dynamic": "^15.2.2", "@angular/router": "^15.2.2", "@ant-design/icons-angular": "^15.0.0", "@azure/msal-angular": "^2.5.8", "@azure/msal-browser": "^2.31.1", "@ericblade/quagga2": "^1.8.4", "@microsoft/microsoft-graph-client": "^3.0.4", "@microsoft/microsoft-graph-types": "^2.25.0", "angular-csv-ext": "^1.0.5", "cronstrue": "^2.52.0", "html2canvas": "^1.4.1", "jsbarcode": "^3.11.6", "jspdf": "^2.5.1", "lodash": "^4.17.21", "moment": "^2.29.4", "ng-zorro-antd": "^15.0.3", "ngx-barcode-scanner": "^14.0.1", "ngx-print": "1.3.1", "node-cache": "^5.1.2", "number-to-words": "^1.2.4", "rxjs": "~7.5.0", "tslib": "^2.3.0", "xlsx": "^0.18.5", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^15.2.2", "@angular/cli": "^15.2.2", "@angular/compiler-cli": "^15.2.2", "@types/jasmine": "~3.10.0", "@types/node": "^12.11.1", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.9.5"}}