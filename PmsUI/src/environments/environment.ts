import AzureData from 'src/Config.json';

// This file can be replaced during build by using the `fileReplacements` array.
// `ng build` replaces `environment.ts` with `environment.prod.ts`.
// The list of file replacements can be found in `angular.json`.

export const environment = {
  production: false,
  Api_Url: "https://devapi.pms.zaibunco.com/api/",
  //Api_Url: "http://localhost:7071/api/",
  BlobSASToken: '?sv=2020-08-04&ss=bfqt&srt=sco&sp=rwdlacupitfx&se=2022-07-30T21:11:05Z&st=2022-06-08T13:11:05Z&spr=https&sig=LHfaneGFqgoR3j799DANXbdkZWo47L3KuBVkZihdn2o%3D',
  BlobURL: 'https://pmsdocumentstordev.blob.core.windows.net/',
  localCacheTtl: 60,
  switchURL: 'https://dev.belt.pms.zaibunco.com/login',
  switchLeatherURL: 'https://dev.leather.pms.zaibunco.com/login',
  DivisionName: 'Local Rexine Division',
  EnvironmentName: 'local'
};

export const ActiveX = {
  AppName: AzureData.Local.AppName,
  tenentId: AzureData.Local.tenantId,
  clientId: AzureData.Local.clientId,
  authority: AzureData.Local.authority,
  redirectUri: AzureData.Local.redirectUri,
  postLogoutRedirectUri: AzureData.Local.postLogoutRedirectUri
}
/*
 * For easier debugging in development mode, you can import the following file
 * to ignore zone related error stack frames such as `zone.run`, `zoneDelegate.invokeTask`.
 *
 * This import should be commented out in production mode because it will have a negative impact
 * on performance if an error is thrown.
 */
// import 'zone.js/plugins/zone-error';  // Included with Angular CLI.
