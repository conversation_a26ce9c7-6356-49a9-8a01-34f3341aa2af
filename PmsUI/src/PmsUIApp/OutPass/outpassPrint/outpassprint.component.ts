import { HttpClient } from "@angular/common/http";
import { Component, OnInit } from "@angular/core";
import { ActivatedRoute, Params, Router } from "@angular/router";
import { Subscription } from "rxjs";
import { environment } from "../../../environments/environment";
import { OutPassModel, OutpassItemModel } from "../../Models/OutPassModel";
import { CompanyInfo } from "src/PmsUIApp/Authentication/UserInfo";
import { Modules, Responsibility } from "src/PmsUIApp/Models/Enums";
import { AuthService } from "src/PmsUIApp/Services/auth.service";

interface GroupedOutpassItem {
  productId: number;
  productName: string;
  totalQuantity: number;
  totalBatchAmount: number;
  units: string;
  totalAmount: number;
  batches: {
    batchNo: string;
    quantity: number;
  }[];
}

interface UnitGroupedItems {
  unitType: string;
  items: GroupedOutpassItem[];
  totalQuantity: number;
  totalAmount: number;
}

@Component({
  selector: "app-outpassprint",
  templateUrl: "./outpassprint.component.html",
  styleUrls: ["./outpassprint.component.css"],
})
export class OutPassPrintComponent implements OnInit {
  ApiUrl = environment.Api_Url;
  OutPass!: OutPassModel;
  OutpassId = 0;
  private route$: Subscription = new Subscription();
  count: number;
  PrintAllowed: boolean = false;
  Company = {
    CompanyName: '',
    HeadOfficeAddress: '',
    FactoryAddress: ''
  };
  groupedOutpassItems: GroupedOutpassItem[] = [];
  unitGroupedItems: UnitGroupedItems[] = [];

  constructor(public http: HttpClient, private route: ActivatedRoute) { }

  ngOnInit() {
    this.route$ = this.route.params.subscribe((params: Params) => {
      this.OutpassId = params['id'];
    });
    this.Company.CompanyName = CompanyInfo.CompanyName;
    this.Company.HeadOfficeAddress = CompanyInfo.HeadOfficAddress;
    this.Company.FactoryAddress = CompanyInfo.FactoryAddress;
    this.GetOutPassById(this.OutpassId)
  }

  GetOutPassById(outpassId: number) {
    let url = this.ApiUrl + 'Outpass/getoutpassbyid?outpassid=' + outpassId;
    this.http.get<OutPassModel>(url).subscribe(
      (res) => {
        this.OutPass = res;
        this.PrintAllowed = this.OutPass.Status == 'Approved' || this.OutPass.Status == 'Approval Pending'
        this.OutPass.OutpassItems.forEach(y => {
          y.Total = (y.Quantity ?? 0) * (y.Amount ?? 0);
        });

        // Group items by ProductId
        this.groupOutpassItems();

        setTimeout(function () {
          window.print();
          window.onafterprint = close;
          function close() {
            window.close();
          }
        }, 1)
      },
      (res) => {
        this.count++;
        if (this.count < 2) {
          this.GetOutPassById(outpassId);
        }
      }
    );
  }

  groupOutpassItems() {
    // Create a map to group items by composite key (ProductId or ProductName based)
    const groupedMap = new Map<string, OutpassItemModel[]>();

    // Group items using dual-key strategy
    this.OutPass.OutpassItems.forEach(item => {
      // Create composite grouping key
      let groupingKey: string;
      if (item.ProductId != null && item.ProductId !== undefined) {
        // Primary grouping: Use ProductId when available
        groupingKey = `pid-${item.ProductId}`;
      } else {
        // Fallback grouping: Use ProductName when ProductId is null/undefined
        groupingKey = `pname-${item.ProductName || 'unknown'}`;
      }

      if (!groupedMap.has(groupingKey)) {
        groupedMap.set(groupingKey, []);
      }
      groupedMap.get(groupingKey)?.push(item);
    });

    // Convert the map to the grouped format
    this.groupedOutpassItems = Array.from(groupedMap.entries()).map(([groupingKey, items]) => {
      // Extract productId from the grouping key for display purposes
      let productId: number;
      if (groupingKey.startsWith('pid-')) {
        productId = parseInt(groupingKey.substring(4));
      } else {
        // For ProductName-based grouping, use 0 as productId for display
        productId = 0;
      }

      // Get unique units
      const uniqueUnits = [...new Set(items.map(item => item.Unit))];
      const units = uniqueUnits.join(', ');

      // Calculate total quantity and amount
      const totalQuantity = items.reduce((sum, item) => sum + (item.Quantity ?? 0), 0);

      // Calculate average amount only considering items with non-zero Amount values
      const itemsWithNonZeroAmount = items.filter(item => (item.Amount ?? 0) > 0);
      const totalBatchAmount = itemsWithNonZeroAmount.length > 0
        ? itemsWithNonZeroAmount.reduce((sum, item) => sum + (item.Amount ?? 0), 0) / itemsWithNonZeroAmount.length
        : 0; // Average amount excluding zero values

      const totalAmount = totalBatchAmount * totalQuantity; // Sum of individual totals

      // Get batch information
      const batches = items
        .filter(item => item.BatchNo && item.BatchNo.trim() !== '') // Filter out empty batch numbers
        .map(item => ({
          batchNo: item.BatchNo ?? '',
          quantity: item.Quantity ?? 0
        }));

      // If no batches with batch numbers, add a default entry with empty batch number
      if (batches.length === 0 && items.length > 0) {
        batches.push({
          batchNo: '',
          quantity: 0
        });
      }

      return {
        productId,
        productName: items[0].ProductName,
        totalQuantity,
        totalBatchAmount,
        units,
        totalAmount,
        batches
      };
    });

    // Now group items by unit type
    this.groupItemsByUnitType();
  }

  groupItemsByUnitType() {
    // Create a map to group items by unit type
    const unitGroupMap = new Map<string, GroupedOutpassItem[]>();

    // Group items by unit type
    this.groupedOutpassItems.forEach(item => {
      // Use the first unit if there are multiple units (comma-separated)
      let unitType = item.units.split(',')[0].trim();

      // If unit type is empty, use "N/A" as the unit type
      if (!unitType) {
        unitType = "N/A";
      }

      if (!unitGroupMap.has(unitType)) {
        unitGroupMap.set(unitType, []);
      }
      unitGroupMap.get(unitType)?.push(item);
    });

    // Convert the map to the unit grouped format
    this.unitGroupedItems = Array.from(unitGroupMap.entries())
      .map(([unitType, items]) => {
        // Calculate total quantity and amount for this unit group
        const totalQuantity = items.reduce((sum, item) => sum + item.totalQuantity, 0);
        const totalAmount = items.reduce((sum, item) => sum + item.totalAmount, 0);

        return {
          unitType,
          items,
          totalQuantity,
          totalAmount
        };
      })
      // Sort unit groups alphabetically by unit type
      .sort((a, b) => a.unitType.localeCompare(b.unitType));

    // If there are no unit groups, create a default empty one
    if (this.unitGroupedItems.length === 0) {
      this.unitGroupedItems = [{
        unitType: "N/A",
        items: [],
        totalQuantity: 0,
        totalAmount: 0
      }];
    }
  }
}
