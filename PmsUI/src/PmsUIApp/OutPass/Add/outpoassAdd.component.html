<div class="card card-flush h-xl-100">
  <div class="card-body pt-5">
    <nz-page-header [nzGhost]="false">
      <nz-page-header-title>Out Pass </nz-page-header-title>
      <nz-page-header-subtitle>Add new</nz-page-header-subtitle>
      <nz-page-header-extra>

      </nz-page-header-extra>

    </nz-page-header>
    <nz-divider></nz-divider>
    <form nz-form [formGroup]="validateForm" (ngSubmit)="handleOk()">
      <div nz-row [nzGutter]="24">
        <div nz-col [nzSpan]="8">
          <nz-form-item>
            <nz-form-label [nzSpan]="10">Out Pass To </nz-form-label>
            <nz-form-control [nzSpan]="18">

              <input *ngIf="!CustomerMaster" nz-input name="OutPassTo" [(ngModel)]="NewOutPass.OutpassTo"
                [ngModelOptions]="{standalone: true}" />
              <nz-select class="form-select mb-2" nzSize="default" *ngIf="CustomerMaster" name="OutPassToCustomerId"
                [(ngModel)]="SelectedCustomer" nzShowSearch nzPlaceHolder="Choose"
                [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let data of CustomerList" [nzValue]="data" [nzLabel]="data.CustomerName"></nz-option>
              </nz-select>
              <nz-switch [(ngModel)]="CustomerMaster" nzCheckedChildren="Master Customer Name"
                nzUnCheckedChildren="Custom Name" [ngModelOptions]="{standalone: true}"></nz-switch>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSpan]="10">Purpose </nz-form-label>
            <nz-form-control [nzSpan]="18" nzErrorTip="Out Pass To ">
              <nz-select class="form-select mb-2" nzSize="default" [(ngModel)]="SelectedPurpose" nzPlaceHolder="Choose"
                [ngModelOptions]="{standalone: true}" nzShowSearch nzPlaceHolder="Choose">
                <nz-option *ngFor="let s of this.OutPassPurposeList;" [nzValue]="s"
                  [nzLabel]="s.PurposeName"></nz-option>
              </nz-select>

            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSpan]="10">Transport</nz-form-label>
            <nz-form-control [nzSpan]="18">
              <nz-select nzShowSearch class="form-select mb-2" (ngModelChange)="onSelectedTransportChange($event)"
                [(ngModel)]="NewOutPass.TransportId" nzSize="default" nzAllowClear nzPlaceHolder="Choose"
                [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.TransportList;" [nzValue]="s.TransportId"
                  [nzLabel]="s.TransportCompanyName"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>
        </div>
        <div nz-col [nzSpan]="8">

          <nz-form-item>
            <nz-form-label nzRequired [nzSpan]="10">Out Pass Type</nz-form-label>
            <nz-form-control [nzSpan]="18">
              <nz-select class="form-select" nzShowSearch name="VehicleId" [(ngModel)]="NewOutPass.OutpassType"
                [ngModelOptions]="{standalone: true}" nzSize="default" nzPlaceHolder="Choose">
                <nz-option nzValue="Non-Returnable" nzLabel="Non-Returnable"></nz-option>
                <nz-option nzValue="Returnable" nzLabel="Returnable"></nz-option>
              </nz-select>
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label [nzSpan]="10">Remark</nz-form-label>
            <nz-form-control [nzSpan]="18" nzErrorTip="Select Payment Terms">
              <textarea nz-input name="SupplierContactNumber" [(ngModel)]="NewOutPass.Remark"
                [ngModelOptions]="{standalone: true}"></textarea>


            </nz-form-control>
          </nz-form-item>
          <nz-form-item style="margin-top: 43px;">
            <nz-form-label nzRequired [nzSpan]="10">Vehicle Number</nz-form-label>
            <nz-form-control [nzSpan]="18">
              <nz-select class="form-select" nzShowSearch name="Rack" nzSize="default"
                (ngModelChange)="onSelectedVehicleChange($event)" [(ngModel)]="NewOutPass.VehicleId" nzAllowClear
                nzPlaceHolder="Choose" [ngModelOptions]="{standalone: true}">
                <nz-option *ngFor="let s of this.TransportVehicleList;" [nzValue]="s.VehicleId"
                  [nzLabel]="s.VehicleNumber"></nz-option>
                <nz-option nzValue="0" nzLabel="Others"></nz-option>
              </nz-select>
              <span style="color: crimson;"><b>{{this.VehicleStatus}}</b></span>
            </nz-form-control>
          </nz-form-item>

        </div>
        <div nz-col [nzSpan]="8">
          <nz-form-item>
            <nz-form-label nzRequired [nzSpan]="10">Out Pass Date</nz-form-label>
            <nz-form-control [nzSpan]="18" nzErrorTip="Out Pass Date required">
              <nz-date-picker class="form-select" [(ngModel)]="NewOutPass.OutpassDate"
                [ngModelOptions]="{standalone: true}"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item *ngIf="NewOutPass.OutpassType == 'Returnable'">
            <nz-form-label nzRequired [nzSpan]="10">Expected Return Date</nz-form-label>
            <nz-form-control [nzSpan]="18">
              <nz-date-picker class="form-select" nzShowTime nzFormat="dd-MMM-yyy hh:mm a"
                [(ngModel)]="NewOutPass.ExpectedReturnDate" [ngModelOptions]="{standalone: true}"></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>

      <nz-divider></nz-divider>
      <a style="float:right" nz-button nzType="primary" (click)="showModal()">Add New</a>
      <nz-divider></nz-divider>

      <nz-table nzSize="small" [nzPageSize]="100" style="width: 100%;" [nzData]="['']" #basicTable1 nzBordered>
        <thead>
          <tr>
            <th>S.No</th>
            <th>Store</th>
            <th>Rack</th>
            <th>Batch</th>
            <th>Product</th>
            <th>Quantity</th>
            <th>Amount</th>
            <th>Unit</th>
            <th>Total Amount</th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let data of this.NewOutPass.OutpassItems;let i=index">
            <td>{{i+1}}</td>
            <td>{{ data.StoreName }}</td>
            <td>{{ data.RackName }}</td>
            <td>{{ data.BatchNo }}</td>
            <td>{{ data.ProductName }}</td>
            <td>{{ data.Quantity }}</td>
            <td>{{ data.Amount }}</td>
            <td>{{ data.Unit }}</td>
            <td>{{ data.Total }}</td>
            <td>
              <a class="btn btn-sm btn-light-danger" style="float:right  " (click)="handleRemoveRow(data)">
                <i nz-icon nzType="minus-circle" nzTheme="fill" style=" margin-bottom: 3px;">
                </i>Remove
              </a>
            </td>
          </tr>

        </tbody>
        <tfoot *ngIf="this.NewOutPass.OutpassItems.length>0">
        </tfoot>
      </nz-table>
      <nz-divider></nz-divider>



      <div class="text-center">
        <button nz-button nzType="primary" [nzLoading]="isLoading">Save</button>
      </div>

    </form>

    <nz-modal [nzWidth]="700" [nzStyle]="{ top: '20px' }" [(nzVisible)]="isVisible" [nzTitle]="modalTitle"
      [nzContent]="modalContent" [nzFooter]="modalFooter" (nzOnCancel)="handleCancel()">
      <ng-template #modalTitle>{{PopUpTitle}}</ng-template>

      <ng-template #modalContent>
        <!-- Product Type -->
        <nz-form-item>
          <nz-form-label [nzSpan]="8">Product Type</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <nz-select class="form-select mb-2" name="ProductType" nzSize="default" nzPlaceHolder="Select Product Type"
              [(ngModel)]="SelectedProductType" (ngModelChange)="onSelectedProductTypeChange()">
              <nz-option *ngFor="let type of typeList" [nzValue]="type.name" [nzLabel]="type.name"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
        <nz-collapse class="filter-collapse" nzGhost>
          <nz-collapse-panel [nzHeader]="filterPanelHeader" [nzActive]="false" [nzExpandedIcon]="expandIcon">
            <ng-template #expandIcon let-active>
              <i nz-icon [nzType]="active ? 'up' : 'down'" class="collapse-icon"></i>
            </ng-template>
            <ng-template #filterPanelHeader>
              <div class="filter-header">
                <i nz-icon nzType="filter" nzTheme="outline" style="margin-right: 8px;"></i>
                <span style="margin-right: 8px;">Optional Product Category Filters</span>
              </div>
            </ng-template>

            <!-- Category filters with improved spacing -->
            <div class="filter-content">
              <!-- Category dropdown -->
              <nz-form-item>
                <nz-form-label [nzSpan]="8">Category</nz-form-label>
                <nz-form-control [nzSpan]="14">
                  <nz-select class="form-select" name="Category" nzSize="default" nzPlaceHolder="Select Category"
                    [(ngModel)]="SelectedCategory" (ngModelChange)="GetAllFirstCategory($event)">
                    <nz-option *ngFor="let category of ProductCategoryList" [nzValue]="category.ProductCategoryId"
                      [nzLabel]="category.ProductCategory"></nz-option>
                  </nz-select>
                  <div class="form-help-text">Select a category to narrow down product options</div>
                </nz-form-control>
              </nz-form-item>

              <!-- Sub Category with improved spacing -->
              <nz-form-item>
                <nz-form-label [nzSpan]="8">Sub Category</nz-form-label>
                <nz-form-control [nzSpan]="14">
                  <nz-select class="form-select" name="SubCategory" nzSize="default" nzPlaceHolder="Select Sub Category"
                    [(ngModel)]="SelectedFirstSubCategory" (ngModelChange)="GetAllSecondCategory($event)">
                    <nz-option *ngFor="let subCategory of ProductFirstSubCategoryList"
                      [nzValue]="subCategory.ProductFirstSubCategoryId"
                      [nzLabel]="subCategory.ProductFirstSubCategory"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>

              <!-- 2nd Sub Category with improved spacing -->
              <nz-form-item>
                <nz-form-label [nzSpan]="8">2nd Sub Category</nz-form-label>
                <nz-form-control [nzSpan]="14">
                  <nz-select class="form-select" name="SecondSubCategory" nzSize="default"
                    nzPlaceHolder="Select 2nd Sub Category" [(ngModel)]="SelectedSecondSubCategory"
                    (ngModelChange)="FilterforSecSubCategory($event)">
                    <nz-option *ngFor="let secSubCategory of ProductSecSubCategoryList"
                      [nzValue]="secSubCategory.ProductSecSubCategoryId"
                      [nzLabel]="secSubCategory.ProductSecSubCategory"></nz-option>
                  </nz-select>
                </nz-form-control>
              </nz-form-item>
            </div>
          </nz-collapse-panel>
        </nz-collapse>

        <!-- Improved product selection with visual emphasis -->
        <nz-form-item class="primary-selection">
          <nz-form-label [nzSpan]="8">Select Product</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <div class="product-selection-container">
              <nz-select class="form-select" nzShowSearch name="ProductSelect" nzSize="default"
                nzPlaceHolder="Select Product" [(ngModel)]="SelectedProductId" (ngModelChange)="OnProductChange()">
                <nz-option *ngFor="let product of FilteredProductList" [nzValue]="product.ProductId"
                  [nzLabel]="product.ProductName"></nz-option>
              </nz-select>

              <!-- Product Info Tooltip -->
              <i *ngIf="selectedProductDetails" nz-icon nzType="info-circle" nzTheme="outline" class="product-info-icon"
                nz-tooltip [nzTooltipTitle]="productInfoTemplate" nzTooltipPlacement="right"
                [nzTooltipOverlayClassName]="'product-tooltip-overlay'"
                [nzTooltipOverlayStyle]="{ 'max-width': '400px', 'word-wrap': 'break-word' }"></i>

              <!-- Tooltip Template -->
              <ng-template #productInfoTemplate>
                <div class="product-info-tooltip">
                  <h4>Product Details</h4>
                  <div class="info-row">
                    <span class="info-label">Product Type:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductType}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Product Name:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductName}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Product Code:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductCode}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Category:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductCategory}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">First Sub Category:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductFirstSubCategory}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Second Sub Category:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductSecSubCategory}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Unit:</span>
                    <span class="info-value">{{selectedProductDetails?.Unit}}</span>
                  </div>
                  <div class="info-row">
                    <span class="info-label">Minimum Quantity:</span>
                    <span class="info-value">{{selectedProductDetails?.MinimumQuantity}}</span>
                  </div>
                  <div *ngIf="selectedProductDetails?.ProductDescription" class="info-row description-row">
                    <span class="info-label">Description:</span>
                    <span class="info-value">{{selectedProductDetails?.ProductDescription}}</span>
                  </div>
                </div>
              </ng-template>
            </div>
            <div class="form-help-text">Select a product from the list or use filters to narrow down options</div>
          </nz-form-control>
        </nz-form-item>

        <!-- Store Selection (only shown if product has stock) -->
        <nz-form-item *ngIf="HasStock">
          <nz-form-label [nzSpan]="8">Select Store</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <nz-select class="form-select mb-2" nzShowSearch name="StoreSelect" nzSize="default"
              nzPlaceHolder="Select Store" [(ngModel)]="SelectedStoreID" (ngModelChange)="OnStoreChange($event)">
              <nz-option *ngFor="let store of ProductStores" [nzValue]="store.StoreId"
                [nzLabel]="store.StoreName"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <!-- Rack Selection (only shown if product has stock and store is selected) -->
        <nz-form-item *ngIf="HasStock && SelectedStoreID > 0">
          <nz-form-label [nzSpan]="8">Select Rack</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <nz-select class="form-select mb-2" nzShowSearch name="RackSelect" nzSize="default"
              nzPlaceHolder="Select Rack" [(ngModel)]="SelectedRackID" (ngModelChange)="OnRackChange()">
              <nz-option *ngFor="let rack of ProductRacks" [nzValue]="rack.RackId"
                [nzLabel]="rack.RackName"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <!-- Batch Selection (only shown if product has stock and store and rack are selected) -->
        <nz-form-item *ngIf="HasStock && StockBatchList.length > 0">
          <nz-form-label [nzSpan]="8">Select Batch</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <nz-select class="form-select mb-2" nzShowSearch name="BatchSelect" nzSize="default"
              nzPlaceHolder="Select Batch" [(ngModel)]="SelectedBatch" (ngModelChange)="OnBatchChange()">
              <nz-option *ngFor="let batch of StockBatchList" [nzValue]="batch"
                [nzLabel]="batch.Batch"></nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>

        <!-- Manual Product Entry (shown if no product is selected) -->
        <!-- <nz-form-item *ngIf="!SelectedStockProductId">
          <nz-form-label [nzSpan]="8">Enter Product Name</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <input nz-input nzType="text" nzMaxLength="100" name="ProductName" [(ngModel)]="ProductName"
              (ngModelChange)="OnProductNameChange()" placeholder="Enter product name if not in list" />
          </nz-form-control>
        </nz-form-item> -->
        <nz-form-item>
          <nz-form-label [nzSpan]="8">Enter Quantity</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <input nz-input type="number" [(ngModel)]="SelectedQty" />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label [nzSpan]="8">Enter Amount</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <input nz-input type="number" [(ngModel)]="SelectedAmount" />
            <div class="form-help-text">Price per unit as per purchase order: {{InventoryPrice}}</div>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label [nzSpan]="8">Select Unit</nz-form-label>
          <nz-form-control [nzSpan]="14">
            <label nzSize="default" class="form-control " style="padding:5px">{{selectedunit}}</label>
          </nz-form-control>
        </nz-form-item>

      </ng-template>
      <ng-template #modalFooter>
        <div class="text-center">
          <a nz-button nzType="primary" (click)="AddStock()">Add</a>
        </div>
      </ng-template>

    </nz-modal>
  </div>
</div>