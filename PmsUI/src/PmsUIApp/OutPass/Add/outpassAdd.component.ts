import { HttpClient } from '@angular/common/http';
import { Component, OnInit } from '@angular/core';
import {
  UntypedFormBuilder,
  UntypedFormGroup,
} from '@angular/forms';

import { NzModalService } from 'ng-zorro-antd/modal';
import { environment } from '../../../environments/environment';
import { AlertMessageService } from '../../Services/AlertMessageService';
import { Router, ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { OutpassItemModel, OutPassModel } from 'src/PmsUIApp/Models/OutPassModel';
import { AdminStoreModel, OutPassPurposeModel, ProductCategoryModel, ProductFirstSubCategoryModel, ProductSecSubCategoryModel, RackModel, StoreModel } from '../../Models/MasterModel';
import { UserInfo } from '../../Authentication/UserInfo';
import { IssueSaleOrderProductsStockModel, ProductStockStoreReportModel } from '../../Models/StockProductModel';
import { MeasureUnitModel } from '../../Models/MeasureUnitModel';
import { Modules, Responsibility } from '../../Models/Enums';
import { AuthService } from '../../Services/auth.service';
import { CustomerModel } from 'src/PmsUIApp/Models/SupplierModel';
import { TransportModel, TransportVehicleModel } from 'src/PmsUIApp/Models/TransportModel';
import { LoadingService } from 'src/PmsUIApp/Services/loadingService';
import { GateInModel } from 'src/PmsUIApp/Models/GateInModel';
import { ProductModel } from 'src/PmsUIApp/Models/ProductModel';
import { CommonService } from 'src/PmsUIApp/Services/CommonService';
import { ProductWiseStockWithSupplierReportModel } from 'src/PmsUIApp/Models/ReportModel';

@Component({
  selector: 'app-outpassAdd',
  templateUrl: './outpoassAdd.component.html',
  styleUrls: ['./outpassAdd.css'],
})
export class OutPassAddComponent implements OnInit {
  validateForm!: UntypedFormGroup;
  ApiUrl = environment.Api_Url;
  isLoading: boolean = false;
  isTableLoading: boolean = true;
  NewOutPass: OutPassModel = new OutPassModel;
  MeasureUnits: MeasureUnitModel[] = [];
  SelectedPurpose: OutPassPurposeModel;
  SelectTransport: TransportModel;
  SelectedCustomer: CustomerModel;
  productItem = {
    CategoryID: 0,
    FirstCategoryID: 0,
    SecondCategoryID: 0,
    ProductID: 0,
    ProductName: '',
    UnitID: 0,
    Unit: '',
    Grade: 'NA',
    IGST: 0,
    Rate: 0,
    Quantity: 0,
    Amount: 0,
  };
  isVisible = false;
  PopUpTitle = 'Add Stock';
  SelectedStoreID = 0;
  SelectedRackID = 0;
  SelectedProductId = 0;
  SelectedStockProductId = 0;
  SelectedQty = 0;
  ActualQtyOnRack = 0;
  InventoryPrice = 0;
  SelectedAmount = 0;
  selectedStoreDn: any;
  SelectedBatch: any;
  selectedunit = '';
  AdminStoreList: AdminStoreModel[] = [];
  StoreWiseStock: IssueSaleOrderProductsStockModel[] = [];
  ProductList: IssueSaleOrderProductsStockModel[] = [];
  RackListOriginal: RackModel[] = [];
  RackList: RackModel[] = [];
  StockBatchList: ProductWiseStockWithSupplierReportModel[] = [];
  ProductName = '';
  private route$: Subscription = new Subscription();
  count: number;
  permission = {
    View: false,
    Add: false,
    Delete: false
  }
  OutPassPurposeList: OutPassPurposeModel[];
  CustomerMaster: boolean = true;
  CustomerList: CustomerModel[];
  TransportList: TransportModel[] = [];
  TransportVehicleList: TransportVehicleModel[] = [];
  VehicleStatus: string = '';

  // Product category related properties
  AllProductList: ProductModel[] = [];
  FilteredProductList: ProductModel[] = [];
  SelectedProductType: string = '';
  SelectedCategory: number = 0;
  SelectedFirstSubCategory: number = 0;
  SelectedSecondSubCategory: number = 0;
  ProductCategoryList: ProductCategoryModel[] = [];
  ProductFirstSubCategoryList: ProductFirstSubCategoryModel[] = [];
  ProductSecSubCategoryList: ProductSecSubCategoryModel[] = [];
  ProductStores: StoreModel[] = [];
  ProductRacks: RackModel[] = [];

  // Selected product details for tooltip
  selectedProductDetails: ProductModel | null = null;
  HasStock: boolean = false;

  typeList = [
    { name: "Raw" },
    { name: "Finished" },
    { name: "Internal-Use" }
  ];
  constructor(
    private fb: UntypedFormBuilder,
    public http: HttpClient,
    private alertService: AlertMessageService,
    private modalService: NzModalService,
    private route: ActivatedRoute,
    private auth: AuthService,
    private router: Router,
    private loader: LoadingService,
    private commonService: CommonService
  ) { }
  GetAllStore() {
    let url = this.ApiUrl + 'store/getallstores';
    this.http.get<StoreModel[]>(url).subscribe(
      (res) => {


        res.forEach((x) => {
          var str = new AdminStoreModel();
          str.StoreId = x.StoreId;
          str.StoreName = x.StoreName;
          this.AdminStoreList.push(str);
        });
        this.GetUserStores(UserInfo.EmailID);
      },
      (res) => {
        this.count++;
        if (this.count < 2) {
          this.GetAllStore();
        }
      }
    );
  }
  GetAllRack() {
    this.isTableLoading = true;
    let url = this.ApiUrl + "rack/getallracks";
    this.http.get<RackModel[]>(url).subscribe(res => {
      this.RackList = res;
      this.RackListOriginal = res;
      this.isTableLoading = false;
    }, res => {
      this.count++;
      if (this.count < 2) { this.GetAllRack(); }
    });
  }
  GetAllUnits() {
    let url = this.ApiUrl + "data/GetMeasureUnits";
    this.http.get<MeasureUnitModel[]>(url).subscribe(res => {
      this.MeasureUnits = res;
    }, res => { this.GetAllUnits() });
  }
  GetUserStores(UserName: string) {
    this.isTableLoading = true;
    this.AdminStoreList.forEach((x) => (x.IsChecked = false));
    let url = this.ApiUrl + 'user/getuserstores/' + UserName;
    this.http.get<number[]>(url).subscribe(
      (res) => {
        console.log(`res is what`, res);
        this.AdminStoreList.forEach((s) => {
          if (res.includes(s.StoreId)) {
            s.IsChecked = true;
          }
        });
        this.AdminStoreList = this.AdminStoreList.filter((x) => x.IsChecked);

        this.isTableLoading = false;
      },
      (res) => { }
    );
  }
  GetStorewiseStock(data: any) {
    this.selectedStoreDn = data;

    if (this.SelectedStoreID > 0) {
      this.SelectedRackID = 0;
      //this.loader.show();
      this.isTableLoading = true;
      let url =
        this.ApiUrl +
        'consume/getallproductstoconsumebystoreid/' +
        this.SelectedStoreID;
      this.http.get<IssueSaleOrderProductsStockModel[]>(url).subscribe(
        (res) => {
          console.log(`check data`, res);
          this.StoreWiseStock = res.filter((x) => x.Quantity > 0);

          this.RackList = this.RackListOriginal.filter(x => x.StoreId == this.SelectedStoreID);
          console.log(this.RackList)
          //this.RackList =  [...new Set(this.rl.map(item => item))];
          this.StoreWiseStock.forEach((x) => {
            this.CheckQty(x);

            //this.exportfields = [];
            //this.NewEdit.forEach((x) => {
            //  this.fields.ProductName = x.ProductName;
            //  this.fields.ProductType = x.ProductType;
            //  this.fields.Quantity = x.Quantity;
            //  this.exportfields.push(this.fields);
            //});
          });
          // this.loader.hide();
          this.isTableLoading = false;
        },
        (res) => { }
      );
    }
  }
  // These methods are replaced by the new implementations below
  CheckQty(data: ProductStockStoreReportModel) {
    if (data.ConsumptionQuantity > data.Quantity) {
      data.ConsumptionQuantity = data.Quantity;
      data.RemainingQuantity = 0;
      this.alertService.error(
        'ConsumptionQuantity cannot be more than ' + data.Quantity
      );
    } else {
      //console.log(data.Quantity);
      //console.log(data.ConsumptionQuantity);

      data.RemainingQuantity =
        (data.Quantity ?? 0) - (data.ConsumptionQuantity ?? 0);
      //console.log(data.RemainingQuantity);
    }

    if (data.SCQuantity > data.Quantity) {
      data.SCQuantity = data.Quantity;
      data.SCRemainingQuantity = 0;
      this.alertService.error(
        'ConsumptionQuantity cannot be more than ' + data.SCQuantity
      );
    } else {
      //console.log(data.SCQuantity);
      //console.log(data.ConsumptionQuantity);

      data.SCRemainingQuantity = (data.Quantity ?? 0) - (data.SCQuantity ?? 0);
      //console.log(data.RemainingQuantity);
    }
  }
  handleOk(): void {
    this.Save()

  }
  handleCancel(): void {
    this.isVisible = false;
  }
  showModal() {
    this.isVisible = true;
  }
  AddStock() {
    var item = new OutpassItemModel;

    if (this.NewOutPass.OutpassItems.length > 0 && this.SelectedStockProductId == 0) {
      if (this.NewOutPass.OutpassItems.filter(x => x.ProductId == this.SelectedProductId).length > 0) {
        this.alertService.error("Product already added.");
        return;
      }
    }
    if (this.NewOutPass.OutpassItems.length > 0 && this.SelectedStockProductId > 0) {
      if (this.NewOutPass.OutpassItems.filter(x => x.StockProductId == this.SelectedStockProductId).length > 0) {
        this.alertService.error("Product already added with same batch.");
        return;
      }
    }
    // Get the selected product details
    const selectedProduct = this.FilteredProductList.find(p => p.ProductId === this.SelectedProductId);
    if (!selectedProduct) {
      this.alertService.error("Please select a valid product.");
      return;
    }
    // Check if we're using a stock product
    if (this.SelectedStockProductId > 0 && this.HasStock) {
      // Check if the selected quantity is more than the actual quantity on the rack
      if (this.ActualQtyOnRack < this.SelectedQty) {
        this.alertService.error("Quantity cannot be more than inventory quantity. Inventory quantity is " + this.ActualQtyOnRack);
        return;
      }
      // Using a stock product with available stock
      if (this.SelectedStoreID > 0 && this.SelectedRackID > 0) {
        // Get store and rack information
        const store = this.ProductStores.find(x => x.StoreId == this.SelectedStoreID);
        const rack = this.ProductRacks.find(x => x.RackId == this.SelectedRackID);


        if (store && rack && selectedProduct) {
          item.RackId = this.SelectedRackID;
          item.StockProductId = this.SelectedStockProductId;
          item.Quantity = this.SelectedQty;
          item.Amount = this.SelectedAmount;
          item.Total = item.Quantity * item.Amount;
          item.ProductName = selectedProduct.ProductName;
          item.StoreName = store.StoreName;
          item.RackName = rack.RackName;
          item.BatchNo = this.SelectedBatch.Batch;
          item.Unit = this.selectedunit;
        } else {
          this.alertService.error("Please select a valid product, store, and rack combination.");
          return;
        }
      } else {
        this.alertService.error("Please select a store and rack for the selected product.");
        return;
      }
    } else {
      // For product which has no stock
      if (this.SelectedQty <= 0) {
        this.alertService.error("Please enter a valid quantity.");
        return;
      }

      item.Quantity = this.SelectedQty;
      item.Amount = this.SelectedAmount;
      item.ProductName = selectedProduct.ProductName;
      item.Total = item.Quantity * item.Amount;
      item.Unit = this.selectedunit || 'Nos'; // Default to 'Nos' if no unit is selected
    }

    this.NewOutPass.OutpassItems.push(item);
    this.handleCancel();
    this.reset();
  }
  reset() {
    // Reset all form fields
    // this.SelectedProductType = '';
    // this.SelectedCategory = 0;
    // this.SelectedFirstSubCategory = 0;
    // this.SelectedSecondSubCategory = 0;
    this.SelectedProductId = 0;
    this.SelectedStockProductId = 0;
    this.SelectedStoreID = 0;
    this.SelectedRackID = 0;
    this.SelectedQty = 0;
    this.ActualQtyOnRack = 0;
    this.InventoryPrice = 0;
    this.SelectedAmount = 0;
    this.ProductName = '';
    this.selectedunit = '';
    this.HasStock = false;

    // Reset filtered lists
    // this.FilteredProductList = this.AllProductList;
    this.ProductStores = [];
    this.ProductRacks = [];
  }
  Save() {
    if (this.SelectedCustomer == null && this.CustomerMaster == true) {
      this.alertService.error("Select Customer from Drop Down.");
      return;
    }
    if (this.NewOutPass.OutpassTo == "" && this.CustomerMaster == false) {
      this.alertService.error("Enter Name in Out Pass To");
      return;
    }
    if (this.NewOutPass.OutpassType == "") {
      this.alertService.error("Please select Out Pass Type.");
      return;
    }
    if (this.NewOutPass.OutpassType == "Returnable" && this.NewOutPass.ExpectedReturnDate == null) {
      this.alertService.error("Please select expected return date.");
      return;
    }
    if (this.NewOutPass.Remark.length > 150) {
      this.alertService.error("Remark cannot be more than 150 characters");
      return;
    }
    if (this.NewOutPass.Purpose.length > 150) {
      this.alertService.error("Purpose cannot be more than 150 characters");
      return;
    }
    if (this.SelectedPurpose == null) {
      this.alertService.error("Please select appropriate purpose.");
      return;
    }
    if (this.NewOutPass.OutpassItems.length < 1) {
      this.alertService.error("Please add atleast one item.");
      return;
    }
    if (this.CustomerMaster) {
      this.NewOutPass.OutpassTo = this.SelectedCustomer.CustomerName;
      this.NewOutPass.OutpassToCustomerId = this.SelectedCustomer.CustomerId;
    }
    this.NewOutPass.Purpose = this.SelectedPurpose.PurposeName;
    this.NewOutPass.PurposeId = this.SelectedPurpose.PurposeId;
    this.isLoading = true;
    this.loader.show();
    let url = this.ApiUrl + 'outpass/addoutpass';
    this.http.post<any>(url, this.NewOutPass).subscribe({
      next: (res) => {
        this.alertService.success('Out Pass Saved Successfully');
        this.isLoading = false;
        this.loader.hide();

        this.router.navigate(['/home/<USER>/list']);
      },
      error: (res) => {
        this.alertService.error(res.error);
        this.isLoading = false;
        this.loader.hide();
      },
    });
  }
  ngOnInit(): void {
    this.permission.View = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.View);
    this.permission.Add = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Add);
    this.permission.Delete = this.auth.CheckResponsibility(Modules.OutPass, Responsibility.Delete);
    if (this.permission.Add != true) {
      this.router.navigate(['/home/<USER>']);
    }
    this.validateForm = this.fb.group({

    });
    this.GetAllCustomer();
    this.GetAllUnits();
    this.GetAllStore();
    this.GetAllRack();
    this.GetAllOutPassPurposes();
    this.GetAllTransport();
    this.GetAllProducts();
    this.GetAllProductCategory();
    this.NewOutPass.OutpassDate = new Date()
  }

  // Product category related methods
  GetAllProducts() {
    this.loader.show();
    this.commonService.getProductList().subscribe({
      next: res => {
        this.AllProductList = res;
        this.FilteredProductList = this.AllProductList;
        this.loader.hide();
      },
      error: err => {
        this.loader.hide();
        this.count++;
        if (this.count < 2) { this.GetAllProducts(); }
      }
    });
  }

  GetAllProductCategory() {
    this.commonService.getProductCategoryList().subscribe({
      next: res => {
        this.ProductCategoryList = res;
      },
      error: err => {
        this.count++;
        if (this.count < 2) { this.GetAllProductCategory(); }
      }
    });
  }

  onSelectedProductTypeChange() {
    // Reset dependent fields
    this.SelectedCategory = 0;
    this.SelectedFirstSubCategory = 0;
    this.SelectedSecondSubCategory = 0;
    this.SelectedProductId = 0;
    this.ProductFirstSubCategoryList = [];
    this.ProductSecSubCategoryList = [];
    this.ProductStores = [];
    this.ProductRacks = [];
    this.HasStock = false;

    // Reload categories from the original list
    this.GetAllProductCategory();

    // After categories are loaded, filter by selected type
    setTimeout(() => {
      this.ProductCategoryList = this.ProductCategoryList.filter(x => x.ProductType == this.SelectedProductType);

      // Filter products by selected type
      this.FilteredProductList = this.AllProductList.filter(x => x.ProductType == this.SelectedProductType);
    }, 100);
  }

  GetAllFirstCategory(data: any) {
    // Reset dependent fields
    this.SelectedFirstSubCategory = 0;
    this.SelectedSecondSubCategory = 0;
    this.SelectedProductId = 0;
    this.ProductSecSubCategoryList = [];
    this.ProductStores = [];
    this.ProductRacks = [];
    this.HasStock = false;

    // Get first subcategories
    this.commonService.getProductFirstSubCategoryList().subscribe({
      next: res => {
        // Store the full list first
        const allFirstSubCategories = res;

        // Then filter by the selected category
        this.ProductFirstSubCategoryList = allFirstSubCategories.filter(x => x.ProductCategoryId == data);

        // Filter products by selected category
        this.FilteredProductList = this.AllProductList.filter(
          x => x.ProductType == this.SelectedProductType &&
            x.ProductCategoryId == this.SelectedCategory
        );
      },
      error: err => {
        console.error('Error loading first subcategories', err);
      }
    });
  }

  GetAllSecondCategory(data: any) {
    // Reset dependent fields
    this.SelectedSecondSubCategory = 0;
    this.SelectedProductId = 0;
    this.ProductStores = [];
    this.ProductRacks = [];
    this.HasStock = false;

    // Get second subcategories
    this.commonService.getProductSecondSubCategoryList().subscribe({
      next: res => {
        // Store the full list first
        const allSecondSubCategories = res;

        // Then filter by the selected first subcategory
        this.ProductSecSubCategoryList = allSecondSubCategories.filter(x => x.ProductFirstSubCategoryId == data);

        // Filter products by selected first subcategory
        this.FilteredProductList = this.AllProductList.filter(
          x => x.ProductType == this.SelectedProductType &&
            x.ProductCategoryId == this.SelectedCategory &&
            x.ProductFirstSubCategoryId == this.SelectedFirstSubCategory
        );
      },
      error: err => {
        console.error('Error loading second subcategories', err);
      }
    });
  }

  FilterforSecSubCategory(selectedSecSubCategoryId: any) {
    // Reset product selection
    this.SelectedProductId = 0;
    this.ProductStores = [];
    this.ProductRacks = [];
    this.HasStock = false;

    // Filter products by selected second subcategory
    this.FilteredProductList = this.AllProductList.filter(
      x => x.ProductType == this.SelectedProductType &&
        x.ProductCategoryId == this.SelectedCategory &&
        x.ProductFirstSubCategoryId == this.SelectedFirstSubCategory &&
        x.ProductSecSubCategoryId == this.SelectedSecondSubCategory
    );
  }

  OnProductChange() {
    // Clear manual entry fields when a product is selected
    this.ProductName = '';
    this.SelectedQty = 0;
    this.ActualQtyOnRack = 0;
    this.SelectedAmount = 0;
    this.InventoryPrice = 0;
    this.selectedunit = '';
    this.ProductStores = [];
    this.ProductRacks = [];
    this.StockBatchList = [];
    this.HasStock = false;

    // Reset selected product details
    this.selectedProductDetails = null;

    if (this.SelectedProductId > 0) {
      // Get product details first
      const selectedProduct = this.FilteredProductList.find(p => p.ProductId === this.SelectedProductId);
      if (selectedProduct) {
        this.selectedunit = selectedProduct.Unit;

        // Store the selected product details for the tooltip
        this.selectedProductDetails = selectedProduct;
      }

      // Get stores for this product
      this.GetStoresForProduct(this.SelectedProductId);
    }
  }

  // Get stores where this product is available
  GetStoresForProduct(ProductId: number) {
    if (ProductId > 0) {
      this.loader.show();
      // Use the new dedicated endpoint for getting stores for a product
      let url = this.ApiUrl + 'stock/getstoresforproduct/' + ProductId;
      this.http.get<StoreModel[]>(url).subscribe({
        next: (res) => {
          this.ProductStores = res;
          this.HasStock = this.ProductStores.length > 0;

          if (!this.HasStock) {
            // If no stock is available, show a message to the user
            this.alertService.warning("This product has no stock available. You can still create outpass for it manually.");
          }

          this.loader.hide();
        },
        error: (error) => {
          console.error('Error fetching stores for product', error);

          // Check if the error is "Entity not found"
          if (error.error === "Entity not found") {
            this.alertService.warning("This product has no stock available. You can still create outpass for it.");
          } else {
            this.alertService.error("Error fetching stock information: " + (error.error || error.message || "Unknown error"));
          }

          this.HasStock = false;
          this.loader.hide();
        }
      });
    }
  }

  // Handle store selection change
  OnStoreChange(storeId: number) {
    this.SelectedRackID = 0;
    this.ProductRacks = [];
    this.StockBatchList = [];

    if (storeId > 0 && this.SelectedProductId > 0) {
      this.GetRacksForProductInStore(this.SelectedProductId, storeId);
    }
  }

  // Get racks for this product in the selected store
  GetRacksForProductInStore(ProductId: number, storeId: number) {
    if (ProductId > 0 && storeId > 0) {
      this.loader.show();
      // Use the new dedicated endpoint for getting racks for a product in a store
      let url = this.ApiUrl + 'stock/getracksforproductinstore/' + ProductId + '?storeid=' + storeId;
      this.http.get<RackModel[]>(url).subscribe({
        next: (res) => {
          this.ProductRacks = res;
          this.loader.hide();
        },
        error: (error) => {
          console.error('Error fetching racks for product in store', error);
          this.loader.hide();
        }
      });
    }
  }

  // Handle rack selection change
  OnRackChange() {
    this.SelectedStockProductId = 0;
    this.StockBatchList = [];

    // Get stock quantity for the selected product in the selected rack
    if (this.SelectedRackID > 0 && this.SelectedProductId > 0 && this.SelectedStoreID > 0) {
      this.loader.show();

      // Call the API to get product stock with supplier information
      let url = this.ApiUrl + 'stock/getproductwisestockwithsupplier';

      // Create the request body
      const requestBody = {
        ProductId: this.SelectedProductId,
        StoreId: this.SelectedStoreID,
        RackId: this.SelectedRackID
      };

      this.http.post<any>(url, requestBody).subscribe({
        next: (res) => {
          if (res && res.length > 0) {
            // Create a list of batches
            this.StockBatchList = res.map((item: any) => ({
              Batch: item.Batch,
              StockProductId: item.StockProductId,
              Quantity: item.Quantity,
              PricePerUnit: item.PricePerUnit
            }));
            console.log(this.StockBatchList);
          } else {
            // No stock found
            this.SelectedQty = 0;
            this.ActualQtyOnRack = 0;
            this.SelectedAmount = 0;
            this.InventoryPrice = 0;
            this.alertService.warning("No stock information found for this product in the selected rack.");
          }
          this.loader.hide();
        },
        error: (error) => {
          console.error('Error fetching product stock information', error);
          this.alertService.error("Error fetching stock information: " + (error.error || error.message || "Unknown error"));
          this.SelectedQty = 0;
          this.ActualQtyOnRack = 0;
          this.SelectedAmount = 0;
          this.InventoryPrice = 0;
          this.loader.hide();
        }
      });
    }
  }

  OnBatchChange() {
    this.SelectedQty = 0;
    this.ActualQtyOnRack = 0;
    this.SelectedAmount = 0;
    this.InventoryPrice = 0;

    this.SelectedStockProductId = this.SelectedBatch.StockProductId;

    if (this.SelectedStockProductId > 0) {
      const selectedBatch = this.StockBatchList.find(x => x.StockProductId === this.SelectedStockProductId);
      if (selectedBatch) {
        this.ActualQtyOnRack = parseFloat(parseFloat(selectedBatch.Quantity).toFixed(2));
        this.SelectedQty = parseFloat(parseFloat(selectedBatch.Quantity).toFixed(2));
        this.InventoryPrice = parseFloat(parseFloat(selectedBatch.PricePerUnit).toFixed(2));
        // Set a default amount
        this.SelectedAmount = 0;
      } else {
        // No stock found
        this.SelectedQty = 0;
        this.ActualQtyOnRack = 0;
        this.SelectedAmount = 0;
        this.InventoryPrice = 0;
        this.alertService.warning("No stock information found for this product in the selected rack.");
      }
    }
  }

  // Handle manual product name changes
  // OnProductNameChange() {
  //   if (this.ProductName && this.ProductName.trim() !== '') {
  //     // If a product name is entered manually, clear stock product selection
  //     this.SelectedStockProductId = 0;
  //     this.HasStock = false;

  //     // Clear store and rack selections
  //     this.SelectedStoreID = 0;
  //     this.SelectedRackID = 0;
  //     this.ProductStores = [];
  //     this.ProductRacks = [];
  //   }
  // }
  get f() {
    return this.validateForm.controls;
  }
  handleRemoveRow(data: any) {
    this.NewOutPass.OutpassItems = this.NewOutPass.OutpassItems.filter((obj) => obj !== data);

  }
  GetAllOutPassPurposes() {
    this.isTableLoading = true;
    let url = this.ApiUrl + "outpass/getalloutpasspurpose";
    this.http.get<OutPassPurposeModel[]>(url).subscribe(res => {
      this.OutPassPurposeList = res;
    }, res => {
      this.count++;
      if (this.count < 2) {
        this.GetAllOutPassPurposes();
      }
    });
  }
  GetAllCustomer() {
    let url = this.ApiUrl + "customer/getallcustomers";
    this.http.get<CustomerModel[]>(url).subscribe(res => {
      this.CustomerList = res;
    }, res => {
      this.count++;
      if (this.count < 2) { this.GetAllCustomer(); }
    });
  }
  GetAllTransport() {
    let url = this.ApiUrl + "transport/getalltransport";
    this.http.get<TransportModel[]>(url).subscribe({
      next: res => { this.TransportList = res },
      error: res => {
        this.alertService.error("An error has been occured. Please try again");
        this.count++;
        if (this.count < 2) {
          this.GetAllTransport();
        }
      },
    });
  }
  onSelectedTransportChange($event: number) {
    var transport = this.TransportList.filter(x => x.TransportId == $event)[0];
    this.TransportVehicleList = transport.TransportVehicle;

  }

  onSelectedVehicleChange($event: number) {
    let url = this.ApiUrl + "gate/getvehiclestatus?vehicleid=" + $event;
    this.http.get<GateInModel>(url).subscribe({
      next: res => {
        this.VehicleStatus = null;
        if (res != null) {
          this.VehicleStatus = "Selected Vehicle is already Gate-in";
          if (res.GatePassIssue == true) {
            this.VehicleStatus = this.VehicleStatus + " and Gate pass issued";
          }
        }
      },
      error: res => {
        this.alertService.error("An error has been occured. Please try again");
      },
    });
  }
}
