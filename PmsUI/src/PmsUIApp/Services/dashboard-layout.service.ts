import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { catchError, map, tap } from 'rxjs/operators';
import {
  DashboardLayoutModel,
  DashboardSectionModel,
  DashboardTileModel,
  DashboardSettingsModel,
  GateDashboardModel,
  GateDashboardRequestModel
} from '../Models/GateDashboardModel';
import { environment } from '../../environments/environment';

interface CacheEntry {
  data: any;
  expiry: number;
  timestamp: number;
}

interface DashboardConfigResponse {
  success: boolean;
  message: string;
  data?: UserDashboardConfig;
  dataList?: UserDashboardConfig[];
}

interface UserDashboardConfig {
  configId: number;
  userId: string;
  dashboardType: string;
  configJson: string;
  configName?: string;
  description?: string;
  isDefault: boolean;
  version: number;
  tags?: string;
}

interface SaveDashboardConfigRequest {
  dashboardType: string;
  configJson: string;
  configName?: string;
  description?: string;
  isDefault: boolean;
  tags?: string;
}

@Injectable({
  providedIn: 'root'
})
export class DashboardLayoutService {
  private readonly STORAGE_KEY = 'gate_dashboard_layout';
  private readonly DEFAULT_LAYOUT_KEY = 'gate_dashboard_default_layout';
  private readonly DATA_CACHE_KEY = 'gate_dashboard_data_cache';
  private readonly API_BASE_URL = environment.Api_Url;

  private layoutSubject = new BehaviorSubject<DashboardLayoutModel>(this.getDefaultLayout());
  public layout$ = this.layoutSubject.asObservable();

  private settingsSubject = new BehaviorSubject<DashboardSettingsModel>(this.getDefaultSettings());
  public settings$ = this.settingsSubject.asObservable();

  // Data caching
  private dataCache = new Map<string, CacheEntry>();
  private readonly DEFAULT_CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

  // Performance optimization
  private visibleSectionsCache: DashboardSectionModel[] | null = null;
  private lastLayoutVersion: number = 0;

  // Database persistence flags
  private useDatabasePersistence = true;
  private currentUserId: string = '';

  constructor(private http: HttpClient) {
    this.initializeService();
  }

  private async initializeService(): Promise<void> {
    // Try to get current user ID (you may need to inject a user service here)
    this.currentUserId = this.getCurrentUserId();

    if (this.useDatabasePersistence && this.currentUserId) {
      await this.loadLayoutFromDatabase();
    } else {
      this.loadLayoutFromLocalStorage();
    }

    this.loadDataCache();
  }

  private getCurrentUserId(): string {
    // This should be replaced with actual user service injection
    // For now, try to get from localStorage or session
    return localStorage.getItem('currentUserId') || sessionStorage.getItem('currentUserId') || '';
  }

  // Get default dashboard layout
  private getDefaultLayout(): DashboardLayoutModel {
    const layout = new DashboardLayoutModel();
    layout.sections = [
      {
        id: 'gate-operations',
        title: 'Gate Operations',
        description: 'Current gate activity and pending operations',
        isVisible: true,
        order: 0,
        isCollapsed: false,
        tiles: [
          {
            id: 'pending-gate-out',
            title: 'Pending Gate-Out',
            description: 'Vehicles waiting for gate-out completion',
            value: 0,
            icon: 'export',
            color: '#ff7875',
            isVisible: true,
            order: 0,
            sectionId: 'gate-operations',
            actionRoute: '/home/<USER>/gateout',
            actionLabel: 'View Gate Out'
          },
          {
            id: 'pending-gate-pass',
            title: 'Pending Gate Passes',
            description: 'Vehicles waiting for gate pass issuance',
            value: 0,
            icon: 'file-text',
            color: '#ffa940',
            isVisible: true,
            order: 1,
            sectionId: 'gate-operations',
            actionRoute: '/home/<USER>/gatepass',
            actionLabel: 'Issue Gate Pass'
          },
          {
            id: 'invoices-without-po',
            title: 'Invoice Gate-ins Without Purchase Order',
            description: 'Invoices processed without purchase orders',
            value: 0,
            icon: 'warning',
            color: '#95de64',
            isVisible: true,
            order: 2,
            sectionId: 'gate-operations',
            actionRoute: '/home/<USER>/gatein',
            actionLabel: 'View Gate In'
          }
        ]
      },
      {
        id: 'purchase-orders',
        title: 'Purchase Order',
        description: 'Purchase order status and analytics',
        isVisible: true,
        order: 1,
        isCollapsed: false,
        tiles: [
          {
            id: 'active-po',
            title: 'Active Purchase Orders',
            description: 'Number of POs with Active status',
            value: 0,
            icon: 'file-done',
            color: '#1890ff',
            isVisible: true,
            order: 0,
            sectionId: 'purchase-orders',
            actionRoute: '/home/<USER>/list?status=Active',
            actionLabel: 'View Active POs'
          },
          {
            id: 'revised-po',
            title: 'Revised Purchase Orders',
            description: 'Number of POs with Revised status',
            value: 0,
            icon: 'file-sync',
            color: '#722ed1',
            isVisible: true,
            order: 1,
            sectionId: 'purchase-orders',
            actionRoute: '/home/<USER>/list?status=Revised',
            actionLabel: 'View Revised POs'
          },
          {
            id: 'delayed-delivery-po',
            title: 'Delayed Purchase Orders Delivery',
            description: 'POs past their delivery term deadline',
            value: 0,
            icon: 'clock-circle',
            color: '#ff4d4f',
            isVisible: true,
            order: 2,
            sectionId: 'purchase-orders',
            actionRoute: '/home/<USER>/list',
            actionLabel: 'View Delayed POs'
          },
          {
            id: 'delayed-payment-po',
            title: 'Delayed Purchase Orders Payment',
            description: 'POs past their payment term deadline',
            value: 0,
            icon: 'dollar-circle',
            color: '#faad14',
            isVisible: true,
            order: 3,
            sectionId: 'purchase-orders',
            actionRoute: '/home/<USER>/list',
            actionLabel: 'View Payment Status'
          }
        ]
      },
      {
        id: 'products',
        title: 'Product Management',
        description: 'Product inventory and stock analytics',
        isVisible: true,
        order: 2,
        isCollapsed: false,
        tiles: [
          {
            id: 'delayed-demands',
            title: 'Delayed Demands',
            description: 'Demands in Active state after 5 days from added date',
            value: 0,
            icon: 'clock-circle',
            color: '#fa8c16',
            isVisible: true,
            order: 0,
            sectionId: 'products',
            actionRoute: '/home/<USER>/demandlist?status=Active',
            actionLabel: 'View Delayed Demands'
          },
          {
            id: 'products-below-min-quantity',
            title: 'Products Below Minimum Quantity',
            description: 'Products with stock below minimum quantity level in non-WIP stores (includes all stock levels)',
            value: 0,
            icon: 'exclamation-circle',
            color: '#eb2f96',
            isVisible: true,
            order: 1,
            sectionId: 'products',
            actionRoute: '/home/<USER>/productwisestockwithsupplier?isMinumQuantityCheck=true',
            actionLabel: 'View Stock Report'
          },
          {
            id: 'pending-issue-requests',
            title: 'Pending Issue Requests',
            description: 'Issue requests with Pending status',
            value: 0,
            icon: 'file-exclamation',
            color: '#13c2c2',
            isVisible: true,
            order: 2,
            sectionId: 'products',
            actionRoute: '/home/<USER>/list?status=Pending',
            actionLabel: 'View Issue Requests'
          },
          {
            id: 'total-products',
            title: 'Total Products',
            description: 'Total number of active products in the system',
            value: 0,
            icon: 'appstore',
            color: '#52c41a',
            isVisible: true,
            order: 3,
            sectionId: 'products',
            actionRoute: '/home/<USER>/list',
            actionLabel: 'View All Products'
          },
          {
            id: 'low-stock-products',
            title: 'Low Stock Products',
            description: 'Products below minimum stock level but still have some stock (excludes out-of-stock items)',
            value: 0,
            icon: 'warning',
            color: '#faad14',
            isVisible: true,
            order: 4,
            sectionId: 'products',
            actionRoute: '/home/<USER>/list?stockStatus=low',
            actionLabel: 'View Low Stock'
          },
          {
            id: 'out-of-stock-products',
            title: 'Out of Stock Products',
            description: 'Products with zero current stock',
            value: 0,
            icon: 'stop',
            color: '#ff4d4f',
            isVisible: true,
            order: 5,
            sectionId: 'products',
            actionRoute: '/home/<USER>/list?stockStatus=out',
            actionLabel: 'View Out of Stock'
          }
        ]
      }
    ];
    layout.lastModified = new Date().toISOString();
    return layout;
  }

  // Get default settings
  private getDefaultSettings(): DashboardSettingsModel {
    const settings = new DashboardSettingsModel();
    settings.layout = this.getDefaultLayout();
    settings.defaultDateFilter = 'last30days';
    settings.autoRefresh = false;
    settings.refreshInterval = 30;
    return settings;
  }

  // Load layout from database
  private async loadLayoutFromDatabase(): Promise<void> {
    try {
      const headers = this.getAuthHeaders();
      const response = await this.http.get<DashboardConfigResponse>(
        `${this.API_BASE_URL}/dashboardconfig/getuserconfig?dashboardType=gate`,
        { headers }
      ).toPromise();

      if (response?.success && response.data) {
        const layout = JSON.parse(response.data.configJson) as DashboardLayoutModel;
        // Merge with default layout to ensure new tiles/sections are included
        const mergedLayout = this.mergeWithDefaultLayout(layout);
        this.layoutSubject.next(mergedLayout);
        console.log('Dashboard layout loaded from database and merged with defaults');
      } else {
        console.log('No database configuration found, using default');
        this.layoutSubject.next(this.getDefaultLayout());
      }
    } catch (error) {
      console.error('Error loading dashboard layout from database:', error);
      console.log('Falling back to localStorage');
      this.loadLayoutFromLocalStorage();
    }
  }

  // Load layout from localStorage (fallback)
  private loadLayoutFromLocalStorage(): void {
    try {
      const savedLayout = localStorage.getItem(this.STORAGE_KEY);
      if (savedLayout) {
        const layout = JSON.parse(savedLayout) as DashboardLayoutModel;
        // Merge with default layout to ensure new tiles/sections are included
        const mergedLayout = this.mergeWithDefaultLayout(layout);
        this.layoutSubject.next(mergedLayout);
        console.log('Dashboard layout loaded from localStorage and merged with defaults');
      } else {
        this.layoutSubject.next(this.getDefaultLayout());
      }
    } catch (error) {
      console.error('Error loading dashboard layout from localStorage:', error);
      this.resetToDefault();
    }
  }

  // Merge existing layout with default layout to include new tiles/sections
  private mergeWithDefaultLayout(existingLayout: DashboardLayoutModel): DashboardLayoutModel {
    const defaultLayout = this.getDefaultLayout();
    const mergedLayout = { ...existingLayout };

    // Handle migration: Move "invoices-without-po" tile from Analytics to Gate Operations
    this.migrateAnalyticsSection(mergedLayout);

    // Remove deprecated Analytics section
    mergedLayout.sections = mergedLayout.sections.filter(s => s.id !== 'analytics');

    // Ensure all default sections exist
    for (const defaultSection of defaultLayout.sections) {
      let existingSection = mergedLayout.sections.find(s => s.id === defaultSection.id);

      if (!existingSection) {
        // Add new section with proper order
        console.log(`Adding new section: ${defaultSection.id}`);
        mergedLayout.sections.push({ ...defaultSection });
      } else {
        // Merge tiles within existing section
        for (const defaultTile of defaultSection.tiles) {
          const existingTile = existingSection.tiles.find(t => t.id === defaultTile.id);

          if (!existingTile) {
            // Add new tile with proper order
            console.log(`Adding new tile: ${defaultTile.id} to section: ${defaultSection.id}`);
            existingSection.tiles.push({ ...defaultTile });
          } else {
            // Update existing tile properties (description, icon, color, etc.) while preserving user customizations
            existingTile.description = defaultTile.description;
            existingTile.icon = defaultTile.icon;
            existingTile.color = defaultTile.color;
            existingTile.actionRoute = defaultTile.actionRoute;
            existingTile.actionLabel = defaultTile.actionLabel;
            // Preserve user customizations: isVisible, order, custom title if any
            console.log(`Updated tile properties: ${defaultTile.id}`);
          }
        }

        // Re-sort tiles by order to maintain proper sequence
        existingSection.tiles.sort((a, b) => a.order - b.order);
      }
    }

    // Re-sort sections by order
    mergedLayout.sections.sort((a, b) => a.order - b.order);

    // Update version to trigger cache invalidation
    mergedLayout.version = (mergedLayout.version || 0) + 1;
    mergedLayout.lastModified = new Date().toISOString();

    // Save the merged layout back to storage
    this.saveLayoutToLocalStorage(mergedLayout);

    return mergedLayout;
  }

  // Migrate Analytics section tiles to appropriate sections
  private migrateAnalyticsSection(layout: DashboardLayoutModel): void {
    const analyticsSection = layout.sections.find(s => s.id === 'analytics');
    if (analyticsSection) {
      console.log('Migrating Analytics section tiles...');

      // Find the invoices-without-po tile in Analytics section
      const invoicesTile = analyticsSection.tiles.find(t => t.id === 'invoices-without-po');
      if (invoicesTile) {
        // Move it to Gate Operations section
        const gateOpsSection = layout.sections.find(s => s.id === 'gate-operations');
        if (gateOpsSection) {
          // Update tile properties for new section
          invoicesTile.sectionId = 'gate-operations';
          invoicesTile.order = 2; // Place it as the third tile in Gate Operations

          // Add to Gate Operations if not already there
          const existingTile = gateOpsSection.tiles.find(t => t.id === 'invoices-without-po');
          if (!existingTile) {
            gateOpsSection.tiles.push({ ...invoicesTile });
            console.log('Moved invoices-without-po tile from Analytics to Gate Operations');
          }

          // Re-sort tiles in Gate Operations
          gateOpsSection.tiles.sort((a, b) => a.order - b.order);
        }
      }
    }
  }

  private getAuthHeaders(): HttpHeaders {
    // Get authentication headers - this should be replaced with your actual auth service
    const token = localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
    return new HttpHeaders({
      'Content-Type': 'application/json',
      'X-User-Id': this.currentUserId,
      'Authorization': token ? `Bearer ${token}` : ''
    });
  }

  // Save layout to localStorage with debouncing
  private saveTimeout: any;

  public saveLayout(layout: DashboardLayoutModel): void {
    // Clear cache when layout changes
    this.visibleSectionsCache = null;

    // Debounce saves to prevent excessive writes
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(async () => {
      try {
        layout.lastModified = new Date().toISOString();
        layout.version = (layout.version || 0) + 1;

        if (this.useDatabasePersistence && this.currentUserId) {
          await this.saveLayoutToDatabase(layout);
        } else {
          this.saveLayoutToLocalStorage(layout);
        }

        this.layoutSubject.next(layout);
        console.log('Dashboard layout saved successfully', layout.version);
      } catch (error) {
        console.error('Error saving dashboard layout:', error);
        // Fallback to localStorage if database save fails
        try {
          this.saveLayoutToLocalStorage(layout);
          this.layoutSubject.next(layout);
          console.log('Fallback: Layout saved to localStorage');
        } catch (fallbackError) {
          console.error('Fallback save also failed:', fallbackError);
          throw new Error('Failed to save dashboard layout');
        }
      }
    }, 300); // 300ms debounce
  }

  private async saveLayoutToDatabase(layout: DashboardLayoutModel): Promise<void> {
    const request: SaveDashboardConfigRequest = {
      dashboardType: 'gate',
      configJson: JSON.stringify(layout),
      configName: 'User Layout',
      description: 'User customized dashboard layout',
      isDefault: true,
      tags: 'user,custom'
    };

    const headers = this.getAuthHeaders();
    const response = await this.http.post<DashboardConfigResponse>(
      `${this.API_BASE_URL}/dashboardconfig/saveconfig`,
      request,
      { headers }
    ).toPromise();

    if (!response?.success) {
      throw new Error(response?.message || 'Failed to save to database');
    }

    console.log('Dashboard layout saved to database');
  }

  private saveLayoutToLocalStorage(layout: DashboardLayoutModel): void {
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(layout));
    console.log('Dashboard layout saved to localStorage');
  }

  // Get current layout
  public getCurrentLayout(): DashboardLayoutModel {
    return this.layoutSubject.value;
  }

  // Update section order
  public updateSectionOrder(sections: DashboardSectionModel[]): void {
    const layout = this.getCurrentLayout();
    layout.sections = sections.map((section, index) => ({
      ...section,
      order: index
    }));
    this.saveLayout(layout);
  }

  // Update tile order within a section
  public updateTileOrder(sectionId: string, tiles: DashboardTileModel[]): void {
    const layout = this.getCurrentLayout();
    const section = layout.sections.find(s => s.id === sectionId);
    if (section) {
      section.tiles = tiles.map((tile, index) => ({
        ...tile,
        order: index,
        sectionId: sectionId
      }));
      this.saveLayout(layout);
    }
  }

  // Move tile between sections
  public moveTileBetweenSections(
    tileId: string,
    fromSectionId: string,
    toSectionId: string,
    newIndex: number
  ): void {
    const layout = this.getCurrentLayout();
    const fromSection = layout.sections.find(s => s.id === fromSectionId);
    const toSection = layout.sections.find(s => s.id === toSectionId);

    if (fromSection && toSection) {
      const tileIndex = fromSection.tiles.findIndex(t => t.id === tileId);
      if (tileIndex !== -1) {
        const tile = fromSection.tiles.splice(tileIndex, 1)[0];
        tile.sectionId = toSectionId;
        toSection.tiles.splice(newIndex, 0, tile);

        // Reorder tiles in both sections
        fromSection.tiles.forEach((t, i) => t.order = i);
        toSection.tiles.forEach((t, i) => t.order = i);

        this.saveLayout(layout);
      }
    }
  }

  // Toggle section visibility
  public toggleSectionVisibility(sectionId: string): void {
    const layout = this.getCurrentLayout();
    const section = layout.sections.find(s => s.id === sectionId);
    if (section) {
      section.isVisible = !section.isVisible;
      this.saveLayout(layout);
    }
  }

  // Toggle tile visibility
  public toggleTileVisibility(tileId: string): void {
    const layout = this.getCurrentLayout();
    for (const section of layout.sections) {
      const tile = section.tiles.find(t => t.id === tileId);
      if (tile) {
        tile.isVisible = !tile.isVisible;
        this.saveLayout(layout);
        break;
      }
    }
  }

  // Toggle section collapsed state
  public toggleSectionCollapsed(sectionId: string): void {
    const layout = this.getCurrentLayout();
    const section = layout.sections.find(s => s.id === sectionId);
    if (section) {
      section.isCollapsed = !section.isCollapsed;
      this.saveLayout(layout);
    }
  }

  // Reset to default layout
  public resetToDefault(): void {
    const defaultLayout = this.getDefaultLayout();
    this.saveLayout(defaultLayout);
  }

  // Force merge current layout with defaults (useful for adding new tiles/sections)
  public forceMergeWithDefaults(): void {
    const currentLayout = this.getCurrentLayout();
    const mergedLayout = this.mergeWithDefaultLayout(currentLayout);
    this.layoutSubject.next(mergedLayout);
    console.log('Layout forcefully merged with defaults - tile descriptions updated');
  }

  // Force update all tile descriptions to latest versions
  public updateTileDescriptions(): void {
    const currentLayout = this.getCurrentLayout();
    const defaultLayout = this.getDefaultLayout();

    // Update descriptions for all tiles
    for (const section of currentLayout.sections) {
      const defaultSection = defaultLayout.sections.find(s => s.id === section.id);
      if (defaultSection) {
        for (const tile of section.tiles) {
          const defaultTile = defaultSection.tiles.find(t => t.id === tile.id);
          if (defaultTile) {
            tile.description = defaultTile.description;
            console.log(`Updated description for tile: ${tile.id}`);
          }
        }
      }
    }

    // Update version and save
    currentLayout.version = (currentLayout.version || 0) + 1;
    currentLayout.lastModified = new Date().toISOString();

    this.saveLayout(currentLayout);
    console.log('All tile descriptions updated to latest versions');
  }

  // Update tile data
  public updateTileData(tileId: string, value: number): void {
    const layout = this.getCurrentLayout();
    for (const section of layout.sections) {
      const tile = section.tiles.find(t => t.id === tileId);
      if (tile) {
        tile.value = value;
        // Don't save to localStorage for data updates, only layout changes
        this.layoutSubject.next(layout);
        break;
      }
    }
  }

  // Get visible sections with caching
  public getVisibleSections(): DashboardSectionModel[] {
    const currentLayout = this.getCurrentLayout();

    // Use cache if layout hasn't changed
    if (this.visibleSectionsCache && this.lastLayoutVersion === currentLayout.version) {
      return this.visibleSectionsCache;
    }

    this.visibleSectionsCache = currentLayout.sections
      .filter(section => section.isVisible)
      .sort((a, b) => a.order - b.order);

    this.lastLayoutVersion = currentLayout.version;
    return this.visibleSectionsCache;
  }

  // Get visible tiles for a section with caching
  public getVisibleTiles(sectionId: string): DashboardTileModel[] {
    const cacheKey = `tiles_${sectionId}_${this.lastLayoutVersion}`;

    try {
      const cached = this.getCachedData(cacheKey);
      if (cached) {
        return cached as DashboardTileModel[];
      }
    } catch (error) {
      console.warn('Tile cache access failed:', error);
    }

    const section = this.getCurrentLayout().sections.find(s => s.id === sectionId);
    const tiles = section ? section.tiles
      .filter(tile => tile.isVisible)
      .sort((a, b) => a.order - b.order) : [];

    // Cache the result
    try {
      this.setCachedData(cacheKey, tiles, 10 * 60 * 1000); // 10 minutes
    } catch (error) {
      console.warn('Tile cache save failed:', error);
    }

    return tiles;
  }

  // Export layout configuration
  public exportLayout(): string {
    return JSON.stringify(this.getCurrentLayout(), null, 2);
  }

  // Import layout configuration
  public importLayout(layoutJson: string): void {
    try {
      const layout = JSON.parse(layoutJson) as DashboardLayoutModel;
      // Validate layout structure
      if (layout.sections && Array.isArray(layout.sections)) {
        this.saveLayout(layout);
      } else {
        throw new Error('Invalid layout format');
      }
    } catch (error) {
      console.error('Error importing layout:', error);
      throw new Error('Failed to import layout configuration');
    }
  }

  // Data caching methods
  private generateCacheKey(request: GateDashboardRequestModel): string {
    // Special handling for 'all' filter - no date-based cache invalidation
    if (request.DateFilterType === 'all') {
      return 'gate_dashboard_all_data';
    }

    const keyParts = [
      'gate_dashboard',
      request.DateFrom || 'null',
      request.DateTo || 'null',
      request.DateFilterType || 'fullday',
      // Add timestamp for current day to ensure fresh data
      this.isToday(request.DateTo) ? Math.floor(Date.now() / (5 * 60 * 1000)) : 'static'
    ];
    return keyParts.join('_');
  }

  private isToday(dateString?: string): boolean {
    if (!dateString) return false;
    const date = new Date(dateString);
    const today = new Date();
    return date.toDateString() === today.toDateString();
  }

  private isRecent(dateString?: string): boolean {
    if (!dateString) return false;
    const date = new Date(dateString);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return date >= weekAgo;
  }

  public getCachedDashboardData(request: GateDashboardRequestModel): GateDashboardModel | null {
    const key = this.generateCacheKey(request);
    const entry = this.dataCache.get(key);

    if (!entry || Date.now() > entry.expiry) {
      this.dataCache.delete(key);
      return null;
    }

    console.log(`Cache hit for dashboard data: ${key}`);
    return entry.data;
  }

  public setCachedDashboardData(request: GateDashboardRequestModel, data: GateDashboardModel): void {
    const key = this.generateCacheKey(request);
    const duration = this.calculateCacheDuration(request);

    this.dataCache.set(key, {
      data,
      expiry: Date.now() + duration,
      timestamp: Date.now()
    });

    // Save to localStorage for persistence
    this.saveDataCache();

    // Cleanup old entries to prevent memory leaks
    this.cleanupExpiredEntries();
  }

  private calculateCacheDuration(request: GateDashboardRequestModel): number {
    // Special handling for 'all' filter - longer cache since it's less likely to change frequently
    if (request.DateFilterType === 'all') {
      return 30 * 60 * 1000; // 30 minutes for all data
    }

    // Shorter cache for current data, longer for historical
    if (this.isToday(request.DateTo)) {
      return 2 * 60 * 1000; // 2 minutes for today's data
    }

    if (this.isRecent(request.DateTo)) {
      return 15 * 60 * 1000; // 15 minutes for recent data
    }

    return 60 * 60 * 1000; // 1 hour for historical data
  }

  private getCachedData(key: string): any | null {
    const entry = this.dataCache.get(key);
    if (!entry || Date.now() > entry.expiry) {
      this.dataCache.delete(key);
      return null;
    }
    return entry.data;
  }

  private setCachedData(key: string, data: any, customDuration?: number): void {
    const duration = customDuration || this.DEFAULT_CACHE_DURATION;
    this.dataCache.set(key, {
      data,
      expiry: Date.now() + duration,
      timestamp: Date.now()
    });
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    for (const [key, entry] of this.dataCache.entries()) {
      if (now > entry.expiry) {
        this.dataCache.delete(key);
      }
    }
  }

  private loadDataCache(): void {
    try {
      const savedCache = localStorage.getItem(this.DATA_CACHE_KEY);
      if (savedCache) {
        const cacheData = JSON.parse(savedCache);
        const now = Date.now();

        // Only load non-expired entries
        for (const [key, entry] of Object.entries(cacheData)) {
          const cacheEntry = entry as CacheEntry;
          if (now < cacheEntry.expiry) {
            this.dataCache.set(key, cacheEntry);
          }
        }
      }
    } catch (error) {
      console.warn('Error loading data cache:', error);
    }
  }

  private saveDataCache(): void {
    try {
      const cacheData: { [key: string]: CacheEntry } = {};
      for (const [key, entry] of this.dataCache.entries()) {
        cacheData[key] = entry;
      }
      localStorage.setItem(this.DATA_CACHE_KEY, JSON.stringify(cacheData));
    } catch (error) {
      console.warn('Error saving data cache:', error);
    }
  }

  // Clear cache when layout changes (but keep data cache)
  public invalidateLayoutCache(): void {
    this.visibleSectionsCache = null;

    // Clear tile caches
    for (const key of this.dataCache.keys()) {
      if (key.includes('tiles_')) {
        this.dataCache.delete(key);
      }
    }
  }

  // Clear all data cache
  public clearDataCache(): void {
    this.dataCache.clear();
    try {
      localStorage.removeItem(this.DATA_CACHE_KEY);
    } catch (error) {
      console.warn('Error clearing data cache:', error);
    }
  }

  // Section title management
  updateSectionTitle(sectionId: string, newTitle: string): void {
    try {
      const layout = this.getCurrentLayout();

      // Find and update the section title
      const section = layout.sections.find(s => s.id === sectionId);
      if (section) {
        section.title = newTitle;

        // Use the existing saveLayout method which handles all the proper saving logic
        this.saveLayout(layout);

        console.log(`Section title updated: ${sectionId} -> ${newTitle}`);
      } else {
        throw new Error(`Section not found: ${sectionId}`);
      }
    } catch (error) {
      console.error('Error updating section title:', error);
      throw error;
    }
  }

  // Database persistence management
  public enableDatabasePersistence(userId: string): void {
    this.useDatabasePersistence = true;
    this.currentUserId = userId;
    console.log('Database persistence enabled for user:', userId);
  }

  public disableDatabasePersistence(): void {
    this.useDatabasePersistence = false;
    console.log('Database persistence disabled, using localStorage only');
  }

  public isDatabasePersistenceEnabled(): boolean {
    return this.useDatabasePersistence && !!this.currentUserId;
  }

  // Sync methods
  public async syncWithDatabase(): Promise<boolean> {
    if (!this.isDatabasePersistenceEnabled()) {
      console.warn('Database persistence not enabled');
      return false;
    }

    try {
      await this.loadLayoutFromDatabase();
      return true;
    } catch (error) {
      console.error('Error syncing with database:', error);
      return false;
    }
  }

  public async resetToSystemDefault(): Promise<boolean> {
    if (!this.isDatabasePersistenceEnabled()) {
      this.resetToDefault();
      return true;
    }

    try {
      const headers = this.getAuthHeaders();
      const response = await this.http.post<DashboardConfigResponse>(
        `${this.API_BASE_URL}/dashboardconfig/resettodefault?dashboardType=gate`,
        {},
        { headers }
      ).toPromise();

      if (response?.success && response.data) {
        const layout = JSON.parse(response.data.configJson) as DashboardLayoutModel;
        this.layoutSubject.next(layout);
        console.log('Dashboard reset to system default');
        return true;
      } else {
        throw new Error(response?.message || 'Failed to reset to default');
      }
    } catch (error) {
      console.error('Error resetting to system default:', error);
      // Fallback to local reset
      this.resetToDefault();
      return false;
    }
  }

  // Export/Import with database support
  public async exportLayoutFromDatabase(): Promise<string | null> {
    if (!this.isDatabasePersistenceEnabled()) {
      return this.exportLayout();
    }

    try {
      const headers = this.getAuthHeaders();
      const response = await this.http.get<DashboardConfigResponse>(
        `${this.API_BASE_URL}/dashboardconfig/getuserconfig?dashboardType=gate`,
        { headers }
      ).toPromise();

      if (response?.success && response.data) {
        return response.data.configJson;
      }
      return null;
    } catch (error) {
      console.error('Error exporting from database:', error);
      return this.exportLayout(); // Fallback to current layout
    }
  }
}
