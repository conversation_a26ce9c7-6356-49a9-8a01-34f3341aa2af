export class GateDashboardModel {
    // Gate Operations - Enhanced with Total and Filtered counts
    PendingGateOutTotalCount: number = 0;
    PendingGateOutFilteredCount: number = 0;
    PendingGatePassTotalCount: number = 0;
    PendingGatePassFilteredCount: number = 0;
    InvoicesWithoutPOTotalCount: number = 0;
    InvoicesWithoutPOFilteredCount: number = 0;

    // Purchase Order Analytics - Enhanced with Total and Filtered counts
    ActivePOTotalCount: number = 0;
    ActivePOFilteredCount: number = 0;
    RevisedPOTotalCount: number = 0;
    RevisedPOFilteredCount: number = 0;
    DelayedDeliveryPOTotalCount: number = 0;
    DelayedDeliveryPOFilteredCount: number = 0;
    DelayedPaymentPOTotalCount: number = 0;
    DelayedPaymentPOFilteredCount: number = 0;

    // Product Analytics - Enhanced with Total and Filtered counts
    TotalProductsTotalCount: number = 0;
    TotalProductsFilteredCount: number = 0;
    LowStockProductsTotalCount: number = 0;
    LowStockProductsFilteredCount: number = 0;
    OutOfStockProductsTotalCount: number = 0;
    OutOfStockProductsFilteredCount: number = 0;
    DelayedDemandsTotalCount: number = 0;
    DelayedDemandsFilteredCount: number = 0;
    ProductsBelowMinQuantityTotalCount: number = 0;
    ProductsBelowMinQuantityFilteredCount: number = 0;
    PendingIssueRequestsTotalCount: number = 0;
    PendingIssueRequestsFilteredCount: number = 0;


}

export class GateDashboardRequestModel {
    DateFilterType: string = 'fullday'; // 'fullday' or 'datetime'
    DateFrom: string = '';
    DateTo: string = '';
}

// Dashboard Configuration Models
export class DashboardTileModel {
    id: string = '';
    title: string = '';
    description: string = '';
    value: number = 0;
    icon: string = '';
    color: string = '';
    isVisible: boolean = true;
    order: number = 0;
    sectionId: string = '';
    actionRoute?: string = '';
    actionLabel?: string = '';
}

export class DashboardSectionModel {
    id: string = '';
    title: string = '';
    description: string = '';
    isVisible: boolean = true;
    order: number = 0;
    tiles: DashboardTileModel[] = [];
    isCollapsed: boolean = false;
    hasPermission?: boolean = true; // Optional property for permission-based filtering
}

export class DashboardLayoutModel {
    sections: DashboardSectionModel[] = [];
    lastModified: string = '';
    version: number = 1;
}

export class DashboardSettingsModel {
    layout: DashboardLayoutModel = new DashboardLayoutModel();
    defaultDateFilter: string = 'last30days';
    userId: string = '';
    autoRefresh: boolean = false;
    refreshInterval: number = 30; // seconds
}
