<!-- Dashboard Header with Controls -->
<div class="dashboard-header" style="margin-bottom: 16px;">
  <nz-card>
    <div nz-row nzJustify="space-between" nzAlign="middle">
      <div nz-col>
        <h2 style="margin: 0;">Reporting Dashboard</h2>
      </div>
      <div nz-col>
        <nz-button-group>
          <button nz-button nzType="default" (click)="toggleSettingsPanel()"
            [nzType]="showSettingsPanel ? 'primary' : 'default'">
            <i nz-icon nzType="setting"></i>
            Settings
          </button>
          <button nz-button nzType="default" (click)="toggleLayoutEditMode()"
            [nzType]="isLayoutEditMode ? 'primary' : 'default'">
            <i nz-icon nzType="edit"></i>
            {{ isLayoutEditMode ? 'Save Layout' : 'Edit Layout' }}
          </button>
        </nz-button-group>
      </div>
    </div>
  </nz-card>
</div>

<!-- Date Filter Section -->
<div style="margin-bottom: 16px;">
  <nz-card nzTitle="Date Filter" [nzExtra]="dateFilterExtra" nzSize="small">
    <div nz-row [nzGutter]="16">
      <div nz-col [nzSpan]="6">
        <nz-form-item>
          <nz-form-control>
            <label>Date Range</label>
            <nz-select [(ngModel)]="selecteddateFilter" (ngModelChange)="getDateRange(selecteddateFilter)"
              nzPlaceHolder="Choose date range">
              <nz-option *ngFor="let data of dateFilterOptions" [nzValue]="data.Value" [nzLabel]="data.Text">
                <span>{{data.Text}}</span>
                <span *ngIf="data.Value === userDefaultFilter" style="color: #1890ff; margin-left: 8px;">
                  <i nz-icon nzType="star" nzTheme="fill"></i>
                </span>
              </nz-option>
            </nz-select>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col [nzSpan]="4" *ngIf="enableCustomDateRange">
        <nz-form-item>
          <nz-form-control>
            <label>From Date</label>
            <nz-date-picker [(ngModel)]="customDateFrom" (ngModelChange)="onCustomDateChange()"
              nzPlaceHolder="From Date"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col [nzSpan]="4" *ngIf="enableCustomDateRange">
        <nz-form-item>
          <nz-form-control>
            <label>To Date</label>
            <nz-date-picker [(ngModel)]="customDateTo" (ngModelChange)="onCustomDateChange()"
              nzPlaceHolder="To Date"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
      </div>
      <div nz-col [nzSpan]="6">
        <nz-form-item>
          <nz-form-control>
            <label>&nbsp;</label>
            <div>
              <button nz-button nzType="default" nzSize="small" (click)="toggleDefaultSelection()" nz-tooltip
                nzTooltipTitle="Manage default date filter">
                <i nz-icon nzType="setting"></i>
                Default: {{getCurrentDefaultLabel()}}
              </button>
            </div>
          </nz-form-control>
        </nz-form-item>
      </div>
    </div>

    <!-- Default Selection Panel -->
    <div *ngIf="showDefaultSelector" style="margin-top: 16px; padding: 12px; background: #f5f5f5; border-radius: 4px;">
      <div nz-row [nzGutter]="8" nzAlign="middle">
        <div nz-col [nzSpan]="12">
          <span style="font-weight: 500;">Default Filter Management:</span>
        </div>
        <div nz-col [nzSpan]="12" style="text-align: right;">
          <button nz-button nzType="primary" nzSize="small" (click)="setAsDefault()"
            [disabled]="selecteddateFilter === 'custom'" style="margin-right: 8px;">
            <i nz-icon nzType="star"></i>
            Set Current as Default
          </button>
          <button nz-button nzType="default" nzSize="small" (click)="resetToSystemDefault()" style="margin-right: 8px;">
            Reset to System Default
          </button>
          <button nz-button nzType="text" nzSize="small" (click)="toggleDefaultSelection()">
            <i nz-icon nzType="close"></i>
          </button>
        </div>
      </div>
      <div style="margin-top: 8px; font-size: 12px; color: #666;">
        Current default: <strong>{{getCurrentDefaultLabel()}}</strong>
        <span *ngIf="selecteddateFilter === 'custom'" style="color: #ff4d4f;">
          (Custom ranges cannot be set as default)
        </span>
      </div>
    </div>
  </nz-card>
</div>

<!-- Template for card extra content -->
<ng-template #dateFilterExtra>
  <div style="display: flex; align-items: center; gap: 8px;">
    <!-- Cache Status Indicator -->
    <div *ngIf="lastDataLoadTime" style="font-size: 12px; color: #666;">
      <span *ngIf="isDataFromCache" style="color: #52c41a;">
        <i nz-icon nzType="database"></i>
        Cached
      </span>
      <span *ngIf="!isDataFromCache" style="color: #1890ff;">
        <i nz-icon nzType="cloud"></i>
        Live
      </span>
      <span style="margin-left: 4px;">
        {{ lastDataLoadTime | date:'HH:mm:ss' }}
      </span>
    </div>

    <!-- Refresh Buttons -->
    <nz-button-group>
      <button nz-button nzType="default" nzSize="small" (click)="refreshDashboard(false)" [disabled]="isLoading"
        nz-tooltip nzTooltipTitle="Refresh data (uses cache if available)">
        <i nz-icon nzType="reload" [nzSpin]="isLoading"></i>
        Refresh
      </button>
      <button nz-button nzType="primary" nzSize="small" (click)="refreshDashboard(true)" [disabled]="isLoading"
        nz-tooltip nzTooltipTitle="Reload from source (bypasses cache)">
        <i nz-icon nzType="sync" [nzSpin]="isLoading"></i>
        Reload from Source
      </button>
    </nz-button-group>
  </div>
</ng-template>

<!-- Settings Panel -->
<div *ngIf="showSettingsPanel" style="margin-bottom: 16px;">
  <nz-card nzTitle="Dashboard Settings" nzSize="small">
    <div nz-row [nzGutter]="16">
      <div nz-col [nzSpan]="6">
        <button nz-button nzType="default" (click)="resetDashboardLayout()" nzBlock>
          <i nz-icon nzType="reload"></i>
          Reset Layout
        </button>
      </div>
      <div nz-col [nzSpan]="6">
        <button nz-button nzType="default" (click)="exportDashboardLayout()" nzBlock>
          <i nz-icon nzType="download"></i>
          Export Layout
        </button>
      </div>
      <div nz-col [nzSpan]="6">
        <input type="file" #fileInput (change)="importDashboardLayout($event)" accept=".json" style="display: none;">
        <button nz-button nzType="default" (click)="fileInput.click()" nzBlock>
          <i nz-icon nzType="upload"></i>
          Import Layout
        </button>
      </div>
      <div nz-col [nzSpan]="6">
        <button nz-button nzType="default" (click)="clearCacheAndReload()" nzBlock>
          <i nz-icon nzType="clear"></i>
          Clear Cache
        </button>
      </div>
    </div>

    <!-- Second Row - Permission Management -->
    <div nz-row [nzGutter]="16" style="margin-top: 16px;">
      <div nz-col [nzSpan]="6">
        <button nz-button nzType="default" (click)="refreshPermissions()" nzBlock>
          <i nz-icon nzType="safety-certificate"></i>
          Refresh Permissions
        </button>
      </div>
      <div nz-col [nzSpan]="6">
        <button nz-button [nzType]="showRestrictedSections ? 'primary' : 'default'" (click)="toggleRestrictedSections()"
          nzBlock>
          <i nz-icon [nzType]="showRestrictedSections ? 'eye' : 'eye-invisible'"></i>
          {{ showRestrictedSections ? 'Hide Restricted' : 'Show Restricted' }}
        </button>
      </div>
      <div nz-col [nzSpan]="12">
        <div style="padding: 8px; background: #f5f5f5; border-radius: 4px;">
          <strong>Permission Summary:</strong>
          <div style="margin-top: 4px;">
            <span *ngFor="let item of getPermissionSummary() | keyvalue" style="margin-right: 12px; font-size: 12px;">
              <nz-tag [nzColor]="item.value ? 'green' : 'red'" style="margin: 2px;">
                {{ item.key }}: {{ item.value ? 'Authorized' : 'Restricted' }}
              </nz-tag>
            </span>
          </div>
        </div>
      </div>
    </div>
  </nz-card>
</div>

<!-- Configurable Dashboard Layout -->
<div *ngIf="!isLoading" cdkDropList [cdkDropListData]="visibleSections" (cdkDropListDropped)="dropSection($event)"
  [cdkDropListDisabled]="!isLayoutEditMode">

  <!-- Dashboard Sections -->
  <div *ngFor="let section of visibleSections" class="dashboard-section" [ngClass]="getDropZoneClass(section.id)"
    style="margin-bottom: 16px;" cdkDrag [cdkDragDisabled]="!isLayoutEditMode" (cdkDragStarted)="onDragStart($event)"
    (cdkDragEnded)="onDragEnd($event)">

    <!-- Section Header -->
    <nz-card [nzTitle]="sectionTitleTemplate" [nzExtra]="sectionExtraTemplate"
      [nzBodyStyle]="section.isCollapsed ? {display: 'none'} : {}">

      <!-- Section Title Template -->
      <ng-template #sectionTitleTemplate>
        <div style="display: flex; align-items: center; width: 100%;">
          <span *ngIf="isLayoutEditMode" cdkDragHandle style="margin-right: 8px; cursor: move; color: #999;" nz-tooltip
            nzTooltipTitle="Drag to reorder section">
            <i nz-icon nzType="drag"></i>
          </span>

          <!-- Editable Section Title -->
          <div style="flex: 1; display: flex; align-items: center;">
            <!-- Display Mode -->
            <span *ngIf="editingSectionId !== section.id" style="flex: 1;">
              {{ section.title }}
            </span>

            <!-- Edit Mode -->
            <input *ngIf="editingSectionId === section.id" nz-input [(ngModel)]="editingSectionTitle"
              (keydown)="onSectionTitleKeyPress($event, section.id)" (blur)="saveSectionTitle(section.id)"
              style="flex: 1; margin-right: 8px;" placeholder="Enter section title" #titleInput>
          </div>

          <!-- Edit Title Button (only in edit mode) -->
          <button *ngIf="isLayoutEditMode && editingSectionId !== section.id" nz-button nzType="text" nzSize="small"
            (click)="startEditingSectionTitle(section.id, section.title)" nz-tooltip nzTooltipTitle="Edit section title"
            style="margin-right: 8px;">
            <i nz-icon nzType="edit"></i>
          </button>

          <!-- Save/Cancel Buttons (only when editing) -->
          <div *ngIf="editingSectionId === section.id" style="display: flex; gap: 4px; margin-right: 8px;">
            <button nz-button nzType="primary" nzSize="small" (click)="saveSectionTitle(section.id)" nz-tooltip
              nzTooltipTitle="Save title (Enter)">
              <i nz-icon nzType="check"></i>
            </button>
            <button nz-button nzType="default" nzSize="small" (click)="cancelEditingSectionTitle()" nz-tooltip
              nzTooltipTitle="Cancel (Escape)">
              <i nz-icon nzType="close"></i>
            </button>
          </div>

          <nz-badge [nzCount]="getVisibleTilesForSection(section.id).length" style="margin-left: 8px;"
            [nzShowZero]="true"></nz-badge>
        </div>
      </ng-template>

      <!-- Section Extra Template -->
      <ng-template #sectionExtraTemplate>
        <div style="display: flex; align-items: center; gap: 8px;">
          <!-- Permission Indicator -->
          <nz-tag [nzColor]="section.hasPermission ? 'green' : 'red'" style="margin: 0;">
            <i nz-icon [nzType]="section.hasPermission ? 'check-circle' : 'close-circle'"></i>
            {{ section.hasPermission ? 'Authorized' : 'Restricted' }}
          </nz-tag>

          <!-- Section Controls -->
          <button nz-button nzType="text" nzSize="small" (click)="toggleSectionCollapsed(section.id)" nz-tooltip
            nzTooltipTitle="{{ section.isCollapsed ? 'Expand' : 'Collapse' }} section">
            <i nz-icon [nzType]="section.isCollapsed ? 'down' : 'up'"></i>
          </button>
          <button *ngIf="isLayoutEditMode" nz-button nzType="text" nzSize="small"
            (click)="toggleSectionVisibility(section.id)" nz-tooltip nzTooltipTitle="Hide section">
            <i nz-icon nzType="eye-invisible"></i>
          </button>
        </div>
      </ng-template>

      <!-- Tiles Container -->
      <div cdkDropList [id]="section.id" [cdkDropListData]="getVisibleTilesForSection(section.id)"
        [cdkDropListConnectedTo]="getSectionConnectedDropLists()" (cdkDropListDropped)="dropTile($event, section.id)"
        (cdkDropListEntered)="onDragEntered($event)" (cdkDropListExited)="onDragExited($event)"
        [cdkDropListDisabled]="!isLayoutEditMode" class="tiles-container" [ngClass]="getDropZoneClass(section.id)">

        <div nz-row [nzGutter]="[16, 16]">
          <div *ngFor="let tile of getVisibleTilesForSection(section.id)" nz-col [nzSpan]="8" cdkDrag
            [cdkDragData]="tile" [cdkDragDisabled]="!isLayoutEditMode" (cdkDragStarted)="onDragStart($event)"
            (cdkDragEnded)="onDragEnd($event)" class="dashboard-tile">

            <!-- Tile Content -->
            <nz-card class="metric-card" [ngClass]="'tile-' + tile.id" nzHoverable [nzBodyStyle]="{padding: '16px'}">

              <!-- Edit Mode Controls Bar -->
              <div *ngIf="isLayoutEditMode" class="tile-edit-controls"
                style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 4px 0;">
                <!-- Drag Handle -->
                <div cdkDragHandle class="drag-handle" nz-tooltip nzTooltipTitle="Drag to move tile"
                  style="cursor: move; color: #999; padding: 4px;">
                  <i nz-icon nzType="drag"></i>
                </div>

                <!-- Tile Controls -->
                <div class="tile-controls" style="display: flex; gap: 8px;">
                  <button nz-button nzType="text" nzSize="small" (click)="toggleTileVisibility(tile.id)" nz-tooltip
                    nzTooltipTitle="Hide tile" style="padding: 4px 8px;">
                    <i nz-icon nzType="eye-invisible"></i>
                  </button>
                </div>
              </div>

              <!-- Tile Header with Icon and Value -->
              <div class="tile-header"
                style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px;">
                <div class="tile-icon-value" style="display: flex; align-items: center; gap: 12px;">
                  <div class="tile-icon" [ngStyle]="{color: tile.color}">
                    <i nz-icon [nzType]="tile.icon" nzTheme="outline" style="font-size: 24px;"></i>
                  </div>
                  <div class="metric-value-container"
                    style="display: flex; flex-direction: column; align-items: flex-start;">
                    <!-- Dual Count Display: New/Total -->
                    <div class="metric-value"
                      style="font-size: 32px; font-weight: bold; color: #262626; line-height: 1;">
                      {{ getTileDualCountDisplay(tile.id) }}
                    </div>
                    <!-- Count Labels -->
                    <div class="metric-labels"
                      style="font-size: 10px; color: #8c8c8c; margin-top: 2px; display: flex; gap: 8px;">
                      <span>New/Total</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Tile Content -->
              <div class="tile-content">
                <div class="metric-title" style="font-size: 16px; font-weight: 500; margin-bottom: 4px;">
                  {{ tile.title }}
                </div>
                <div class="metric-description" style="font-size: 12px; color: #666; margin-bottom: 12px;">
                  {{ tile.description }}
                </div>
              </div>

              <!-- Tile Actions -->
              <div class="tile-actions" *ngIf="tile.actionRoute">
                <button nz-button nzType="link" (click)="navigateToTileAction(tile)" style="padding: 0;">
                  <i nz-icon nzType="arrow-right"></i>
                  {{ tile.actionLabel || 'View Details' }}
                </button>
              </div>
            </nz-card>
          </div>
        </div>
      </div>
    </nz-card>
  </div>
</div>

<!-- Enhanced Loading State with Skeleton -->
<div *ngIf="isLoading">
  <app-dashboard-skeleton></app-dashboard-skeleton>
</div>

<!-- Edit Mode Overlay -->
<div *ngIf="isLayoutEditMode && showEditModeAlert" class="edit-mode-overlay">
  <nz-alert nzType="info" nzMessage="Edit Mode Active"
    nzDescription="Drag sections and tiles to reorder them. Click 'Save Layout' when done." nzShowIcon
    nzCloseable="true" (nzOnClose)="dismissEditModeAlert()" style="margin-bottom: 16px;">
  </nz-alert>
</div>