.border-left-primary {
  border-left: 0.25rem solid #4e73df !important;
}

.border-left-warning {
  border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
  border-left: 0.25rem solid #e74a3b !important;
}

.text-gray-800 {
  color: #5a5c69 !important;
}

.text-primary {
  color: #4e73df !important;
}

.text-warning {
  color: #f6c23e !important;
}

.text-danger {
  color: #e74a3b !important;
}

.shadow {
  box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
}

.card {
  transition: transform 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  cursor: pointer;
}

.card-footer a {
  text-decoration: none;
  cursor: pointer;
}

.font-weight-bold {
  font-weight: 700 !important;
}

.text-uppercase {
  text-transform: uppercase !important;
}

.text-xs {
  font-size: 0.7rem;
}

.h5 {
  font-size: 1.25rem;
}

.mb-1 {
  margin-bottom: 0.25rem !important;
}

.mb-0 {
  margin-bottom: 0 !important;
}

.mt-2 {
  margin-top: 0.5rem !important;
}

.mr-2 {
  margin-right: 0.5rem !important;
}

.no-gutters {
  margin-right: 0;
  margin-left: 0;
}

.no-gutters>.col,
.no-gutters>[class*="col-"] {
  padding-right: 0;
  padding-left: 0;
}

.align-items-center {
  align-items: center !important;
}

.justify-content-between {
  justify-content: space-between !important;
}

.d-flex {
  display: flex !important;
}

.row {
  display: flex;
  flex-wrap: wrap;
}

.container-fluid {
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
  margin-right: auto;
  margin-left: auto;
}

.bg-transparent {
  background-color: transparent !important;
}

.border-top-0 {
  border-top: 0 !important;
}

/* Configurable Dashboard Styles */

/* Dashboard Header */
.dashboard-header {
  margin-bottom: 16px;
}

/* Dashboard Sections */
.dashboard-section {
  position: relative;
}

.dashboard-section.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.dashboard-section.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Tiles Container */
.tiles-container {
  min-height: 120px;
  position: relative;
}

.tiles-container.cdk-drop-list-dragging .dashboard-tile:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Dashboard Tiles */
.dashboard-tile {
  position: relative;
}

.dashboard-tile.cdk-drag-preview {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.dashboard-tile.cdk-drag-placeholder {
  opacity: 0;
}

.dashboard-tile.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Metric Cards */
.metric-card {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

/* Tile Edit Controls Bar */
.tile-edit-controls {
  border-bottom: 1px solid #f0f0f0;
  background: rgba(250, 250, 250, 0.8);
  border-radius: 4px 4px 0 0;
  margin: -16px -16px 8px -16px;
  padding: 8px 16px;
}

/* Drag Handles */
.drag-handle {
  cursor: move;
  color: #999;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.drag-handle:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.drag-handle i {
  font-size: 14px;
}

/* Tile Specific Styles */
.tile-pending-gate-out .metric-card {
  border-left: 4px solid #ff7875;
}

.tile-pending-gate-pass .metric-card {
  border-left: 4px solid #ffa940;
}

.tile-invoices-without-po .metric-card {
  border-left: 4px solid #95de64;
}

/* Edit Mode Styles */
.edit-mode-overlay {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  max-width: 400px;
}

/* Enhanced Drag and Drop Visual Feedback */
.cdk-drop-list {
  min-height: 60px;
  transition: all 0.3s ease;
}

.cdk-drop-list.cdk-drop-list-receiving {
  background: rgba(24, 144, 255, 0.05);
  border: 2px dashed #1890ff;
  border-radius: 8px;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
  transform: rotate(3deg) scale(1.05);
  opacity: 0.95;
  border: 2px solid #1890ff;
}

.cdk-drag-placeholder {
  opacity: 0.3;
  border: 2px dashed #ccc;
  background: #f5f5f5;
  border-radius: 8px;
  animation: placeholder-pulse 1s infinite;
}

@keyframes placeholder-pulse {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.1;
  }
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

/* Enhanced Drop Zone States */
.drop-zone-idle {
  transition: all 0.3s ease;
}

.drop-zone-compatible {
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05));
  border: 2px dashed #52c41a;
  border-radius: 12px;
  animation: pulse-compatible 1.5s infinite;
}

.drop-zone-incompatible {
  background: linear-gradient(135deg, rgba(255, 77, 79, 0.1), rgba(255, 77, 79, 0.05));
  border: 2px dashed #ff4d4f;
  border-radius: 12px;
  opacity: 0.6;
}

@keyframes pulse-compatible {

  0%,
  100% {
    border-color: #52c41a;
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.4);
  }

  50% {
    border-color: #73d13d;
    box-shadow: 0 0 0 8px rgba(82, 196, 26, 0);
  }
}

/* Global dragging state */
body.dragging-active .dashboard-tile:not(.cdk-drag-preview) {
  opacity: 0.7;
  transition: opacity 0.3s ease;
}

body.dragging-active .tiles-container {
  min-height: 150px;
  transition: min-height 0.3s ease;
}

/* Section Drag Handle */
.dashboard-section .cdk-drag-handle {
  cursor: move;
  color: #999;
}

.dashboard-section .cdk-drag-handle:hover {
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-tile {
    margin-bottom: 16px;
  }

  .edit-mode-overlay {
    position: relative;
    top: auto;
    right: auto;
    margin-bottom: 16px;
  }

  .drag-handle {
    display: none;
  }
}

/* Loading State */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
}

/* Animation for tile visibility changes */
.dashboard-tile {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Display Mode Toggle Styles */
.display-mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.display-mode-toggle .ant-switch {
  margin-right: 8px;
}

.display-mode-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
  transition: color 0.3s ease;
}

.display-mode-label.active {
  color: #1890ff;
}

/* Enhanced filter section styling */
.filters-section .ant-form-item-label > label {
  font-weight: 500;
  color: #262626;
}

.filters-section .ant-switch-checked {
  background-color: #52c41a;
}

.filters-section .ant-switch-checked .ant-switch-handle {
  left: calc(100% - 20px - 2px);
}

/* Section collapse animation */
.dashboard-section .ant-card-body {
  transition: all 0.3s ease;
}

/* Tile controls */
.tile-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.tile-controls button {
  border-radius: 4px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
}

.tile-controls button:hover {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
  border-color: #ff4d4f;
}

.tile-controls button i {
  font-size: 14px;
}

/* Edit mode visibility */
.tile-edit-controls {
  opacity: 1;
  transition: opacity 0.3s ease;
}

/* Ensure proper spacing between drag handle and controls */
.tile-edit-controls .drag-handle {
  margin-right: auto;
}

.tile-edit-controls .tile-controls {
  margin-left: auto;
}