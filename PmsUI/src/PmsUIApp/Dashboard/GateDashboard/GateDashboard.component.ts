import { Component, OnInit, OnDestroy } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../environments/environment';
import {
  GateDashboardModel,
  GateDashboardRequestModel,
  DashboardLayoutModel,
  DashboardSectionModel,
  DashboardTileModel
} from '../../Models/GateDashboardModel';
import { LoadingService } from '../../Services/loadingService';
import { AlertMessageService } from '../../Services/AlertMessageService';
import { Router } from '@angular/router';
import { AuthService } from '../../Services/auth.service';
import { Modules, Responsibility } from '../../Models/Enums';
import { DashboardLayoutService } from '../../Services/dashboard-layout.service';
import { DashboardPermissionService } from '../../Services/dashboard-permission.service';
import { CdkDragDrop, moveItemInArray } from '@angular/cdk/drag-drop';
import { Subscription } from 'rxjs';

@Component({
  selector: 'app-gate-dashboard',
  templateUrl: './GateDashboard.component.html',
  styleUrls: ['./GateDashboard.component.css']
})
export class GateDashboardComponent implements OnInit, OnDestroy {
  ApiUrl = environment.Reporting_Api_Url;
  isLoading: boolean = false;
  dashboardData: GateDashboardModel = new GateDashboardModel();
  permission = {
    View: false
  };

  // Layout management properties
  currentLayout: DashboardLayoutModel = new DashboardLayoutModel();
  visibleSections: DashboardSectionModel[] = [];
  isLayoutEditMode: boolean = false;
  showSettingsPanel: boolean = false;
  showEditModeAlert: boolean = true;

  // Section editing properties
  editingSectionId: string | null = null;
  editingSectionTitle: string = '';

  // Enhanced drag and drop properties
  draggedTile: DashboardTileModel | null = null;
  dropZoneStates = new Map<string, 'idle' | 'compatible' | 'incompatible'>();

  // Cache status
  isDataFromCache: boolean = false;
  lastDataLoadTime: Date | null = null;

  // Permission filtering properties
  showRestrictedSections: boolean = false; // Toggle to show/hide restricted sections

  // Date filter properties
  dateFilterOptions: any = [
    // { "Text": "All", "Value": 'all' },
    { "Text": "Today", "Value": 'today' },
    { "Text": "Yesterday", "Value": 'yesterday' },
    { "Text": "Last 7 Days", "Value": 'last7days' },
    { "Text": "Last 30 Days", "Value": 'last30days' },
    { "Text": "Last Month", "Value": 'lastmonth' },
    // { "Text": "Last Year", "Value": 'lastyear' },
    { "Text": "Custom Range", "Value": 'custom' }
  ];

  selecteddateFilter: string = 'last7days';
  enableCustomDateRange: boolean = false;
  customDateFrom: Date | null = null;
  customDateTo: Date | null = null;
  userDefaultFilter: string = 'Last 7 Days';
  showDefaultSelector: boolean = false;

  // Settings key for localStorage
  private readonly DASHBOARD_SETTINGS_KEY = 'gate_dashboard_settings';
  private readonly EDIT_MODE_ALERT_KEY = 'gate_dashboard_edit_mode_alert';
  private layoutSubscription: Subscription = new Subscription();

  constructor(
    private http: HttpClient,
    private loader: LoadingService,
    private alertService: AlertMessageService,
    private router: Router,
    private auth: AuthService,
    private layoutService: DashboardLayoutService,
    private permissionService: DashboardPermissionService
  ) { }

  ngOnInit() {
    // Use DashboardWelcome module permission instead of GateDashboard
    this.permission.View = this.auth.CheckResponsibility(Modules.DashboardWelcome, Responsibility.View);
    if (!this.permission.View) {
      this.router.navigate(['/home/<USER>']);
      return;
    }

    // Initialize permission service for section-level permissions
    this.permissionService.initializePermissions();

    // Load user's default date filter preference
    this.loadUserDefaultFilter();
    this.loadEditModeAlertPreference();

    // Subscribe to layout changes
    this.layoutSubscription = this.layoutService.layout$.subscribe(layout => {
      this.currentLayout = layout;
      this.visibleSections = this.getPermissionFilteredSections();
    });

    // Force merge with defaults to ensure new tiles are visible and descriptions are updated
    this.layoutService.forceMergeWithDefaults();

    // Also update tile descriptions to latest versions
    this.layoutService.updateTileDescriptions();

    this.loadDashboardData();
  }

  ngOnDestroy() {
    if (this.layoutSubscription) {
      this.layoutSubscription.unsubscribe();
    }
  }

  loadDashboardData(dateFrom?: string, dateTo?: string, bypassCache: boolean = false): void {
    this.isLoading = true;
    // this.loader.show();

    const request = new GateDashboardRequestModel();

    // If no dates provided, use the selected filter
    if (!dateFrom || !dateTo) {
      this.getDateRangeWithCache(this.selecteddateFilter, bypassCache);
      return; // getDateRangeWithCache will call this method again with dates
    }

    request.DateFrom = dateFrom;
    request.DateTo = dateTo;
    request.DateFilterType = this.enableCustomDateRange ? 'datetime' : 'fullday';

    // Try cache first only if not bypassing cache
    if (!bypassCache) {
      try {
        const cachedData = this.layoutService.getCachedDashboardData(request);
        if (cachedData) {
          console.log('Using cached dashboard data');
          this.processDashboardData(cachedData, true);
          this.isLoading = false;
          this.loader.hide();
          return;
        }
      } catch (error) {
        console.warn('Cache service unavailable, proceeding with API call:', error);
      }
    } else {
      console.log('Bypassing cache - forcing fresh data load');
    }

    const url = this.ApiUrl + 'report/gatedashboard';

    console.log('Calling API:', url);
    console.log('Request:', request);

    this.http.post<GateDashboardModel>(url, request).subscribe({
      next: (res) => {
        console.log('API Response:', res);
        this.processDashboardData(res);

        // Cache the result (graceful degradation if caching fails)
        try {
          this.layoutService.setCachedDashboardData(request, res);
        } catch (error) {
          console.warn('Failed to cache dashboard data:', error);
        }

        this.isLoading = false;
        this.loader.hide();
      },
      error: (err) => {
        this.isLoading = false;
        this.loader.hide();
        this.alertService.error('Error loading dashboard data. Please try again later.');
        console.error('Error loading dashboard data:', err);
      }
    });
  }

  private processDashboardData(data: GateDashboardModel, fromCache: boolean = false): void {
    this.dashboardData = data;
    this.isDataFromCache = fromCache;
    this.lastDataLoadTime = new Date();

    // Update tile data in layout service (graceful degradation)
    try {
      // Gate Operations tiles - use filtered counts for tile display
      this.layoutService.updateTileData('pending-gate-out', data.PendingGateOutFilteredCount);
      this.layoutService.updateTileData('pending-gate-pass', data.PendingGatePassFilteredCount);
      this.layoutService.updateTileData('invoices-without-po', data.InvoicesWithoutPOFilteredCount);

      // Purchase Order tiles - use filtered counts for tile display
      this.layoutService.updateTileData('active-po', data.ActivePOFilteredCount);
      this.layoutService.updateTileData('revised-po', data.RevisedPOFilteredCount);
      this.layoutService.updateTileData('delayed-delivery-po', data.DelayedDeliveryPOFilteredCount);
      this.layoutService.updateTileData('delayed-payment-po', data.DelayedPaymentPOFilteredCount);

      // Product tiles - use filtered counts for tile display
      this.layoutService.updateTileData('total-products', data.TotalProductsFilteredCount);
      this.layoutService.updateTileData('low-stock-products', data.LowStockProductsFilteredCount);
      this.layoutService.updateTileData('out-of-stock-products', data.OutOfStockProductsFilteredCount);
      this.layoutService.updateTileData('delayed-demands', data.DelayedDemandsFilteredCount);
      this.layoutService.updateTileData('products-below-min-quantity', data.ProductsBelowMinQuantityFilteredCount);
      this.layoutService.updateTileData('pending-issue-requests', data.PendingIssueRequestsFilteredCount);
    } catch (error) {
      console.warn('Layout service update failed:', error);
    }

    // Show warning for empty data (but not for 'All' filter as it's expected to show all data)
    const hasAnyData = data.PendingGateOutFilteredCount > 0 || data.PendingGatePassFilteredCount > 0 ||
      data.InvoicesWithoutPOFilteredCount > 0 || data.ActivePOFilteredCount > 0 || data.RevisedPOFilteredCount > 0 ||
      data.DelayedDeliveryPOFilteredCount > 0 || data.DelayedPaymentPOFilteredCount > 0 ||
      data.TotalProductsFilteredCount > 0 || data.LowStockProductsFilteredCount > 0 || data.OutOfStockProductsFilteredCount > 0 ||
      data.DelayedDemandsFilteredCount > 0 || data.ProductsBelowMinQuantityFilteredCount > 0 || data.PendingIssueRequestsFilteredCount > 0;

    if (!hasAnyData && this.selecteddateFilter !== 'all') {
      this.alertService.warning('No data found for the selected date range.');
    }
  }

  // Load dashboard data without date filtering (for 'All' option)
  loadDashboardDataWithoutDateFilter(bypassCache: boolean = false): void {
    this.isLoading = true;
    this.loader.show();

    const request = new GateDashboardRequestModel();
    // Don't set DateFrom and DateTo - this will bypass date filtering in backend
    request.DateFilterType = 'last7days';

    // Try cache first only if not bypassing cache
    if (!bypassCache) {
      try {
        const cachedData = this.layoutService.getCachedDashboardData(request);
        if (cachedData) {
          console.log('Using cached dashboard data (All)');
          this.processDashboardData(cachedData, true);
          this.isLoading = false;
          this.loader.hide();
          return;
        }
      } catch (error) {
        console.warn('Cache service unavailable, proceeding with API call:', error);
      }
    } else {
      console.log('Bypassing cache - forcing fresh data load (All)');
    }

    const url = this.ApiUrl + 'report/gatedashboard';

    console.log('Calling API for All data:', url);
    console.log('Request:', request);

    this.http.post<GateDashboardModel>(url, request).subscribe({
      next: (res) => {
        console.log('API Response (All data):', res);
        this.processDashboardData(res);

        // Cache the result (graceful degradation if caching fails)
        try {
          this.layoutService.setCachedDashboardData(request, res);
        } catch (error) {
          console.warn('Failed to cache dashboard data:', error);
        }

        this.isLoading = false;
        this.loader.hide();
      },
      error: (err) => {
        this.isLoading = false;
        this.loader.hide();
        this.alertService.error('Error loading dashboard data. Please try again later.');
        console.error('Error loading dashboard data:', err);
      }
    });
  }

  refreshDashboard(bypassCache: boolean = false) {
    if (bypassCache) {
      console.log('Forcing fresh data reload...');
    }

    if (this.selecteddateFilter === 'all') {
      this.loadDashboardDataWithoutDateFilter(bypassCache);
    } else {
      this.loadDashboardData(undefined, undefined, bypassCache);
    }
  }

  navigateToGateIn() {
    this.router.navigate(['/home/<USER>/gatein']);
  }

  // Date filter methods
  loadUserDefaultFilter(): void {
    try {
      const savedSettings = localStorage.getItem(this.DASHBOARD_SETTINGS_KEY);
      if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        this.userDefaultFilter = settings.defaultFilter || 'last7days';
        this.selecteddateFilter = this.userDefaultFilter;
      } else {
        this.userDefaultFilter = 'last7days';
        this.selecteddateFilter = 'last7days';
      }
    } catch (error) {
      console.error('Error loading user default filter:', error);
      this.userDefaultFilter = 'last7days';
      this.selecteddateFilter = 'last7days';
    }
  }

  saveUserDefaultFilter(filterValue: string): void {
    try {
      const settings = {
        defaultFilter: filterValue,
        userId: 'current_user', // Replace with actual user ID method
        lastUpdated: new Date().toISOString()
      };
      localStorage.setItem(this.DASHBOARD_SETTINGS_KEY, JSON.stringify(settings));
      this.userDefaultFilter = filterValue;
      this.alertService.success('Default date filter saved successfully!');
    } catch (error) {
      console.error('Error saving user default filter:', error);
      this.alertService.error('Failed to save default filter preference.');
    }
  }

  getDateRange(label: string): void {
    this.getDateRangeWithCache(label, false);
  }

  getDateRangeWithCache(label: string, bypassCache: boolean = false): void {
    const today = new Date();
    let startDate: Date;
    let endDate: Date;

    // Clear cache when date range changes
    try {
      this.layoutService.invalidateLayoutCache();
    } catch (error) {
      console.warn('Cache invalidation failed:', error);
    }

    switch (label) {
      case 'all':
        // For 'All' option, don't set dates - this will bypass date filtering
        this.enableCustomDateRange = false;
        this.loadDashboardDataWithoutDateFilter(bypassCache);
        return;

      case 'today':
        startDate = new Date(today);
        endDate = new Date(today);
        this.enableCustomDateRange = false;
        break;

      case 'yesterday':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 1);
        endDate = new Date(today);
        endDate.setDate(today.getDate() - 1);
        this.enableCustomDateRange = false;
        break;

      case 'last7days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 6);
        endDate = new Date(today);
        this.enableCustomDateRange = false;
        break;

      case 'last30days':
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        this.enableCustomDateRange = false;
        break;

      case 'lastmonth':
        startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        endDate = new Date(today.getFullYear(), today.getMonth(), 0);
        this.enableCustomDateRange = false;
        break;

      case 'lastyear':
        startDate = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate());
        endDate = new Date(today);
        this.enableCustomDateRange = false;
        break;

      case 'custom':
        startDate = new Date(today);
        endDate = new Date(today);
        this.enableCustomDateRange = true;
        return;

      default:
        startDate = new Date(today);
        startDate.setDate(today.getDate() - 29);
        endDate = new Date(today);
        this.enableCustomDateRange = false;
        break;
    }

    this.updateDateRangeWithCache(startDate, endDate, bypassCache);
  }

  updateDateRange(startDate: Date, endDate: Date): void {
    this.updateDateRangeWithCache(startDate, endDate, false);
  }

  updateDateRangeWithCache(startDate: Date, endDate: Date, bypassCache: boolean = false): void {
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);
    this.loadDashboardData(startDate.toISOString(), endDate.toISOString(), bypassCache);
  }

  onCustomDateChange(): void {
    if (this.customDateFrom && this.customDateTo) {
      this.updateDateRange(new Date(this.customDateFrom), new Date(this.customDateTo));
    }
  }

  toggleDefaultSelection(): void {
    this.showDefaultSelector = !this.showDefaultSelector;
  }

  setAsDefault(): void {
    if (this.selecteddateFilter !== 'custom') {
      this.saveUserDefaultFilter(this.selecteddateFilter);
      this.showDefaultSelector = false;
    } else {
      this.alertService.warning('Custom date ranges cannot be set as default. Please select a predefined range.');
    }
  }

  resetToSystemDefault(): void {
    this.saveUserDefaultFilter('last7days');
    this.selecteddateFilter = 'last7days';
    this.getDateRange('last7days');
    this.showDefaultSelector = false;
  }

  getCurrentDefaultLabel(): string {
    const defaultOption = this.dateFilterOptions.find((option: any) => option.Value === this.userDefaultFilter);
    return defaultOption ? defaultOption.Text : 'Last 7 Days';
  }

  // Layout management methods
  toggleLayoutEditMode(): void {
    this.isLayoutEditMode = !this.isLayoutEditMode;

    if (this.isLayoutEditMode) {
      // Only show alert if user hasn't dismissed it permanently
      try {
        const savedPreference = localStorage.getItem(this.EDIT_MODE_ALERT_KEY);
        this.showEditModeAlert = savedPreference !== 'false';
      } catch (error) {
        this.showEditModeAlert = true; // Default to showing alert
      }
      this.alertService.success('Edit mode activated. Drag sections and tiles to reorder them.');
    } else {
      this.alertService.success('Layout changes saved successfully!');
    }
  }

  // Load edit mode alert preference
  loadEditModeAlertPreference(): void {
    try {
      const savedPreference = localStorage.getItem(this.EDIT_MODE_ALERT_KEY);
      if (savedPreference !== null) {
        this.showEditModeAlert = JSON.parse(savedPreference);
      }
    } catch (error) {
      console.warn('Error loading edit mode alert preference:', error);
      this.showEditModeAlert = true; // Default to showing alert
    }
  }

  // Dismiss the edit mode alert and save preference
  dismissEditModeAlert(): void {
    this.showEditModeAlert = false;
    try {
      localStorage.setItem(this.EDIT_MODE_ALERT_KEY, 'false');
    } catch (error) {
      console.warn('Error saving edit mode alert preference:', error);
    }
  }

  toggleSettingsPanel(): void {
    this.showSettingsPanel = !this.showSettingsPanel;
  }

  // Section title editing methods
  startEditingSectionTitle(sectionId: string, currentTitle: string): void {
    this.editingSectionId = sectionId;
    this.editingSectionTitle = currentTitle;

    // Auto-focus the input field after view update
    setTimeout(() => {
      const inputElement = document.querySelector(`input[ng-reflect-model="${this.editingSectionTitle}"]`) as HTMLInputElement;
      if (inputElement) {
        inputElement.focus();
        inputElement.select(); // Select all text for easy replacement
      }
    }, 100);
  }

  saveSectionTitle(sectionId: string): void {
    if (this.editingSectionTitle.trim()) {
      // Update the section title in the current layout
      const section = this.visibleSections.find(s => s.id === sectionId);
      if (section) {
        section.title = this.editingSectionTitle.trim();

        // Save to layout service
        try {
          this.layoutService.updateSectionTitle(sectionId, this.editingSectionTitle.trim());
          this.alertService.success('Section title updated successfully!');
        } catch (error) {
          console.warn('Failed to save section title:', error);
          this.alertService.warning('Section title updated locally but may not persist.');
        }
      }
    }
    this.cancelEditingSectionTitle();
  }

  cancelEditingSectionTitle(): void {
    this.editingSectionId = null;
    this.editingSectionTitle = '';
  }

  onSectionTitleKeyPress(event: KeyboardEvent, sectionId: string): void {
    if (event.key === 'Enter') {
      this.saveSectionTitle(sectionId);
    } else if (event.key === 'Escape') {
      this.cancelEditingSectionTitle();
    }
  }

  // Enhanced drag and drop methods
  onDragStart(event: any): void {
    this.draggedTile = event.source.data;
    this.updateDropZoneStates();
    document.body.classList.add('dragging-active');
  }

  onDragEnd(_event: any): void {
    this.draggedTile = null;
    this.dropZoneStates.clear();
    document.body.classList.remove('dragging-active');
  }

  onDragEntered(event: any): void {
    const sectionId = event.container.id;
    this.dropZoneStates.set(sectionId, 'compatible');
  }

  onDragExited(event: any): void {
    const sectionId = event.container.id;
    this.dropZoneStates.set(sectionId, 'idle');
  }

  private updateDropZoneStates(): void {
    this.visibleSections.forEach(section => {
      const canAccept = this.canSectionAcceptTile(section, this.draggedTile!);
      this.dropZoneStates.set(section.id, canAccept ? 'compatible' : 'incompatible');
    });
  }

  private canSectionAcceptTile(section: DashboardSectionModel, tile: DashboardTileModel): boolean {
    // Business logic for tile compatibility
    if (section.id === 'gate-operations' && tile.id.includes('gate')) return true;
    if (section.id === 'analytics' && tile.id.includes('analytics')) return true;
    return true; // Allow all moves for flexibility
  }

  getDropZoneClass(sectionId: string): string {
    const state = this.dropZoneStates.get(sectionId) || 'idle';
    return `drop-zone-${state}`;
  }

  dropSection(event: CdkDragDrop<DashboardSectionModel[]>): void {
    if (event.previousIndex !== event.currentIndex) {
      moveItemInArray(this.visibleSections, event.previousIndex, event.currentIndex);
      this.layoutService.updateSectionOrder(this.visibleSections);
      this.alertService.success('Section order updated!');
    }
  }

  dropTile(event: CdkDragDrop<DashboardTileModel[]>, sectionId: string): void {
    if (event.previousContainer === event.container) {
      // Reordering within the same section
      if (event.previousIndex !== event.currentIndex) {
        const tiles = this.layoutService.getVisibleTiles(sectionId);
        moveItemInArray(tiles, event.previousIndex, event.currentIndex);
        this.layoutService.updateTileOrder(sectionId, tiles);
        this.alertService.success('Tile order updated!');
      }
    } else {
      // Moving between sections
      const fromSectionId = event.previousContainer.id;
      const toSectionId = event.container.id;
      const tileId = event.item.data.id;

      this.layoutService.moveTileBetweenSections(tileId, fromSectionId, toSectionId, event.currentIndex);
      this.alertService.success('Tile moved to different section!');
    }
  }

  // Visibility management
  toggleSectionVisibility(sectionId: string): void {
    this.layoutService.toggleSectionVisibility(sectionId);
    this.visibleSections = this.layoutService.getVisibleSections();
  }

  toggleTileVisibility(tileId: string): void {
    this.layoutService.toggleTileVisibility(tileId);
  }

  toggleSectionCollapsed(sectionId: string): void {
    this.layoutService.toggleSectionCollapsed(sectionId);
  }

  // Utility methods
  getVisibleTilesForSection(sectionId: string): DashboardTileModel[] {
    return this.layoutService.getVisibleTiles(sectionId);
  }

  navigateToTileAction(tile: DashboardTileModel): void {
    if (tile.actionRoute) {
      this.router.navigate([tile.actionRoute]);
    }
  }

  resetDashboardLayout(): void {
    if (confirm('Are you sure you want to reset the dashboard to default layout? This will remove all your customizations.')) {
      this.layoutService.resetToDefault();
      this.visibleSections = this.layoutService.getVisibleSections();
      this.isLayoutEditMode = false;
      this.alertService.success('Dashboard layout reset to default!');
    }
  }

  exportDashboardLayout(): void {
    try {
      const layoutJson = this.layoutService.exportLayout();
      const blob = new Blob([layoutJson], { type: 'application/json' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'gate-dashboard-layout.json';
      a.click();
      window.URL.revokeObjectURL(url);
      this.alertService.success('Dashboard layout exported successfully!');
    } catch (error) {
      this.alertService.error('Failed to export dashboard layout.');
    }
  }

  importDashboardLayout(event: any): void {
    const file = event.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const layoutJson = e.target?.result as string;
          this.layoutService.importLayout(layoutJson);
          this.visibleSections = this.layoutService.getVisibleSections();
          this.alertService.success('Dashboard layout imported successfully!');
        } catch (error) {
          this.alertService.error('Invalid layout file or import failed.');
        }
      };
      reader.readAsText(file);
    }
  }

  // Helper methods for template
  getSectionConnectedDropLists(): string[] {
    return this.visibleSections.map(section => section.id);
  }

  getTileValue(tileId: string): number {
    switch (tileId) {
      // Gate Operations tiles - return filtered count for backward compatibility
      case 'pending-gate-out':
        return this.dashboardData.PendingGateOutFilteredCount;
      case 'pending-gate-pass':
        return this.dashboardData.PendingGatePassFilteredCount;
      case 'invoices-without-po':
        return this.dashboardData.InvoicesWithoutPOFilteredCount;

      // Purchase Order tiles - return filtered count for backward compatibility
      case 'active-po':
        return this.dashboardData.ActivePOFilteredCount;
      case 'revised-po':
        return this.dashboardData.RevisedPOFilteredCount;
      case 'delayed-delivery-po':
        return this.dashboardData.DelayedDeliveryPOFilteredCount;
      case 'delayed-payment-po':
        return this.dashboardData.DelayedPaymentPOFilteredCount;

      // Product tiles - return filtered count for backward compatibility
      case 'total-products':
        return this.dashboardData.TotalProductsFilteredCount;
      case 'low-stock-products':
        return this.dashboardData.LowStockProductsFilteredCount;
      case 'out-of-stock-products':
        return this.dashboardData.OutOfStockProductsFilteredCount;
      case 'delayed-demands':
        return this.dashboardData.DelayedDemandsFilteredCount;
      case 'products-below-min-quantity':
        return this.dashboardData.ProductsBelowMinQuantityFilteredCount;
      case 'pending-issue-requests':
        return this.dashboardData.PendingIssueRequestsFilteredCount;

      default:
        return 0;
    }
  }

  // New method to get total count for a tile
  getTileTotalValue(tileId: string): number {
    switch (tileId) {
      // Gate Operations tiles
      case 'pending-gate-out':
        return this.dashboardData.PendingGateOutTotalCount;
      case 'pending-gate-pass':
        return this.dashboardData.PendingGatePassTotalCount;
      case 'invoices-without-po':
        return this.dashboardData.InvoicesWithoutPOTotalCount;

      // Purchase Order tiles
      case 'active-po':
        return this.dashboardData.ActivePOTotalCount;
      case 'revised-po':
        return this.dashboardData.RevisedPOTotalCount;
      case 'delayed-delivery-po':
        return this.dashboardData.DelayedDeliveryPOTotalCount;
      case 'delayed-payment-po':
        return this.dashboardData.DelayedPaymentPOTotalCount;

      // Product tiles
      case 'total-products':
        return this.dashboardData.TotalProductsTotalCount;
      case 'low-stock-products':
        return this.dashboardData.LowStockProductsTotalCount;
      case 'out-of-stock-products':
        return this.dashboardData.OutOfStockProductsTotalCount;
      case 'delayed-demands':
        return this.dashboardData.DelayedDemandsTotalCount;
      case 'products-below-min-quantity':
        return this.dashboardData.ProductsBelowMinQuantityTotalCount;
      case 'pending-issue-requests':
        return this.dashboardData.PendingIssueRequestsTotalCount;

      default:
        return 0;
    }
  }

  // New method to get formatted dual count display
  getTileDualCountDisplay(tileId: string): string {
    const filteredCount = this.getTileValue(tileId);
    const totalCount = this.getTileTotalValue(tileId);
    return `${filteredCount}/${totalCount}`;
  }

  // ==================== PERMISSION-BASED FILTERING ENHANCEMENT ====================

  // Get sections filtered by user permissions (enhancement layer)
  getPermissionFilteredSections(): DashboardSectionModel[] {
    const allSections = this.layoutService.getVisibleSections();

    // Apply permission filtering as an enhancement layer
    const sectionsWithPermissions = allSections.map(section => {
      const hasPermission = this.permissionService.getSectionPermission(section.id);

      // Create enhanced section with permission information
      const enhancedSection = {
        ...section,
        hasPermission: hasPermission
      };

      return enhancedSection;
    });

    // Filter based on showRestrictedSections toggle
    if (this.showRestrictedSections) {
      // Show all sections with permission indicators
      return sectionsWithPermissions;
    } else {
      // Only show sections user has permission for
      return sectionsWithPermissions.filter(section => section.hasPermission);
    }
  }

  // Get permission status for a section
  getSectionPermission(sectionId: string): boolean {
    return this.permissionService.getSectionPermission(sectionId);
  }

  // Get permission summary for debugging
  getPermissionSummary(): { [sectionId: string]: boolean } {
    return this.permissionService.getPermissionSummary();
  }

  // Refresh permissions (useful for testing or when user roles change)
  refreshPermissions(): void {
    this.permissionService.refreshPermissions();
    this.visibleSections = this.getPermissionFilteredSections();
    this.alertService.success('Permissions refreshed successfully!');
  }

  // Toggle visibility of restricted sections
  toggleRestrictedSections(): void {
    this.showRestrictedSections = !this.showRestrictedSections;
    this.visibleSections = this.getPermissionFilteredSections();

    if (this.showRestrictedSections) {
      this.alertService.success('Now showing all sections including restricted ones.');
    } else {
      this.alertService.success('Now showing only authorized sections.');
    }
  }

  // ==================== ORIGINAL FUNCTIONALITY PRESERVED ====================

  // Clear all caches and reload
  clearCacheAndReload(): void {
    try {
      this.layoutService.clearDataCache();
      this.layoutService.invalidateLayoutCache();
      this.alertService.success('Cache cleared successfully!');
      this.loadDashboardData();
    } catch (error) {
      console.warn('Cache clear failed:', error);
      this.alertService.warning('Cache clear failed, but data will be refreshed.');
      this.loadDashboardData();
    }
  }
}
