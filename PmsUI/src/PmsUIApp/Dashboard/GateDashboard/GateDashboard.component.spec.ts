import { ComponentFixture, TestBed } from '@angular/core/testing';
import { HttpClientTestingModule, HttpTestingController } from '@angular/common/http/testing';
import { RouterTestingModule } from '@angular/router/testing';
import { of } from 'rxjs';

import { GateDashboardComponent } from './GateDashboard.component';
import { GateDashboardModel } from '../../Models/GateDashboardModel';
import { LoadingService } from '../../Services/loadingService';
import { AlertMessageService } from '../../Services/AlertMessageService';
import { AuthService } from '../../Services/auth.service';
import { DashboardLayoutService } from '../../Services/dashboard-layout.service';
import { environment } from '../../../environments/environment';

describe('GateDashboardComponent - Tile Data Verification', () => {
  let component: GateDashboardComponent;
  let fixture: ComponentFixture<GateDashboardComponent>;
  let httpMock: HttpTestingController;
  let mockLoadingService: jasmine.SpyObj<LoadingService>;
  let mockAlertService: jasmine.SpyObj<AlertMessageService>;
  let mockAuthService: jasmine.SpyObj<AuthService>;
  let mockLayoutService: jasmine.SpyObj<DashboardLayoutService>;

  // Test data scenarios
  const mockDashboardDataWithValues: GateDashboardModel = {
    // Gate Operations - Enhanced with Total and Filtered counts
    PendingGateOutTotalCount: 20,
    PendingGateOutFilteredCount: 12,
    PendingGatePassTotalCount: 15,
    PendingGatePassFilteredCount: 8,
    InvoicesWithoutPOTotalCount: 10,
    InvoicesWithoutPOFilteredCount: 5,

    // Purchase Order Analytics - Enhanced with Total and Filtered counts
    ActivePOTotalCount: 40,
    ActivePOFilteredCount: 25,
    RevisedPOTotalCount: 12,
    RevisedPOFilteredCount: 7,
    DelayedDeliveryPOTotalCount: 8,
    DelayedDeliveryPOFilteredCount: 3,
    DelayedPaymentPOTotalCount: 15,
    DelayedPaymentPOFilteredCount: 9,

    // Product Analytics - Enhanced with Total and Filtered counts
    TotalProductsTotalCount: 200,
    TotalProductsFilteredCount: 150,
    LowStockProductsTotalCount: 20,
    LowStockProductsFilteredCount: 12,
    OutOfStockProductsTotalCount: 10,
    OutOfStockProductsFilteredCount: 6,
    DelayedDemandsTotalCount: 15,
    DelayedDemandsFilteredCount: 8,
    ProductsBelowMinQuantityTotalCount: 25,
    ProductsBelowMinQuantityFilteredCount: 15,
    PendingIssueRequestsTotalCount: 18,
    PendingIssueRequestsFilteredCount: 9
  };

  const mockDashboardDataEmpty: GateDashboardModel = {
    // Gate Operations - All zeros for empty state
    PendingGateOutTotalCount: 0,
    PendingGateOutFilteredCount: 0,
    PendingGatePassTotalCount: 0,
    PendingGatePassFilteredCount: 0,
    InvoicesWithoutPOTotalCount: 0,
    InvoicesWithoutPOFilteredCount: 0,

    // Purchase Order Analytics - All zeros for empty state
    ActivePOTotalCount: 0,
    ActivePOFilteredCount: 0,
    RevisedPOTotalCount: 0,
    RevisedPOFilteredCount: 0,
    DelayedDeliveryPOTotalCount: 0,
    DelayedDeliveryPOFilteredCount: 0,
    DelayedPaymentPOTotalCount: 0,
    DelayedPaymentPOFilteredCount: 0,

    // Product Analytics - All zeros for empty state
    TotalProductsTotalCount: 0,
    TotalProductsFilteredCount: 0,
    LowStockProductsTotalCount: 0,
    LowStockProductsFilteredCount: 0,
    OutOfStockProductsTotalCount: 0,
    OutOfStockProductsFilteredCount: 0,
    DelayedDemandsTotalCount: 0,
    DelayedDemandsFilteredCount: 0,
    ProductsBelowMinQuantityTotalCount: 0,
    ProductsBelowMinQuantityFilteredCount: 0,
    PendingIssueRequestsTotalCount: 0,
    PendingIssueRequestsFilteredCount: 0
  };

  const mockDashboardDataTestValues: GateDashboardModel = {
    // Gate Operations - Test values with dual counts
    PendingGateOutTotalCount: 10,
    PendingGateOutFilteredCount: 5,
    PendingGatePassTotalCount: 15,
    PendingGatePassFilteredCount: 8,
    InvoicesWithoutPOTotalCount: 8,
    InvoicesWithoutPOFilteredCount: 3,

    // Purchase Order Analytics - Test values with dual counts
    ActivePOTotalCount: 15,
    ActivePOFilteredCount: 8,
    RevisedPOTotalCount: 10,
    RevisedPOFilteredCount: 4,
    DelayedDeliveryPOTotalCount: 12,
    DelayedDeliveryPOFilteredCount: 4,
    DelayedPaymentPOTotalCount: 20,
    DelayedPaymentPOFilteredCount: 7,

    // Product Analytics - Test values with dual counts
    TotalProductsTotalCount: 300,
    TotalProductsFilteredCount: 150,
    LowStockProductsTotalCount: 25,
    LowStockProductsFilteredCount: 12,
    OutOfStockProductsTotalCount: 15,
    OutOfStockProductsFilteredCount: 6,
    DelayedDemandsTotalCount: 20,
    DelayedDemandsFilteredCount: 8,
    ProductsBelowMinQuantityTotalCount: 30,
    ProductsBelowMinQuantityFilteredCount: 15,
    PendingIssueRequestsTotalCount: 25,
    PendingIssueRequestsFilteredCount: 9
  };

  beforeEach(async () => {
    const loadingSpy = jasmine.createSpyObj('LoadingService', ['show', 'hide']);
    const alertSpy = jasmine.createSpyObj('AlertMessageService', ['success', 'error', 'warning']);
    const authSpy = jasmine.createSpyObj('AuthService', ['CheckResponsibility']);
    const layoutSpy = jasmine.createSpyObj('DashboardLayoutService', [
      'updateTileData', 'getCachedDashboardData', 'setCachedDashboardData',
      'invalidateLayoutCache', 'getVisibleSections'
    ]);

    await TestBed.configureTestingModule({
      declarations: [GateDashboardComponent],
      imports: [HttpClientTestingModule, RouterTestingModule],
      providers: [
        { provide: LoadingService, useValue: loadingSpy },
        { provide: AlertMessageService, useValue: alertSpy },
        { provide: AuthService, useValue: authSpy },
        { provide: DashboardLayoutService, useValue: layoutSpy }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(GateDashboardComponent);
    component = fixture.componentInstance;
    httpMock = TestBed.inject(HttpTestingController);
    mockLoadingService = TestBed.inject(LoadingService) as jasmine.SpyObj<LoadingService>;
    mockAlertService = TestBed.inject(AlertMessageService) as jasmine.SpyObj<AlertMessageService>;
    mockAuthService = TestBed.inject(AuthService) as jasmine.SpyObj<AuthService>;
    mockLayoutService = TestBed.inject(DashboardLayoutService) as jasmine.SpyObj<DashboardLayoutService>;

    // Setup default mocks
    mockAuthService.CheckResponsibility.and.returnValue(true);
    mockLayoutService.getCachedDashboardData.and.returnValue(null);
    mockLayoutService.getVisibleSections.and.returnValue([]);
  });

  afterEach(() => {
    httpMock.verify();
  });

  describe('🧪 Tile Data Verification Tests', () => {

    it('should verify all Gate Operations tiles display correct data', () => {
      // Arrange
      component.dashboardData = mockDashboardDataWithValues;

      // Act & Assert - Gate Operations Tiles
      expect(component.getTileValue('pending-gate-out')).toBe(12);
      expect(component.getTileValue('pending-gate-pass')).toBe(8);
      expect(component.getTileValue('invoices-without-po')).toBe(5);
    });

    it('should verify all Purchase Order tiles display correct data', () => {
      // Arrange
      component.dashboardData = mockDashboardDataWithValues;

      // Act & Assert - Purchase Order Tiles
      expect(component.getTileValue('active-po')).toBe(25);
      expect(component.getTileValue('revised-po')).toBe(7);
      expect(component.getTileValue('delayed-delivery-po')).toBe(3);
      expect(component.getTileValue('delayed-payment-po')).toBe(9);
    });

    it('should verify all Product tiles display correct data', () => {
      // Arrange
      component.dashboardData = mockDashboardDataWithValues;

      // Act & Assert - Product Tiles
      expect(component.getTileValue('total-products')).toBe(150);
      expect(component.getTileValue('low-stock-products')).toBe(12);
      expect(component.getTileValue('out-of-stock-products')).toBe(6);
      expect(component.getTileValue('delayed-demands')).toBe(8);
      expect(component.getTileValue('products-below-min-quantity')).toBe(15);
      expect(component.getTileValue('pending-issue-requests')).toBe(9);
    });

    it('should return 0 for unknown tile IDs', () => {
      // Arrange
      component.dashboardData = mockDashboardDataWithValues;

      // Act & Assert
      expect(component.getTileValue('unknown-tile')).toBe(0);
      expect(component.getTileValue('')).toBe(0);
      expect(component.getTileValue('invalid-tile-id')).toBe(0);
    });

    it('should handle empty/zero data correctly', () => {
      // Arrange
      component.dashboardData = mockDashboardDataEmpty;

      // Act & Assert - All tiles should return 0
      expect(component.getTileValue('pending-gate-out')).toBe(0);
      expect(component.getTileValue('pending-gate-pass')).toBe(0);
      expect(component.getTileValue('invoices-without-po')).toBe(0);
      expect(component.getTileValue('active-po')).toBe(0);
      expect(component.getTileValue('revised-po')).toBe(0);
      expect(component.getTileValue('delayed-delivery-po')).toBe(0);
      expect(component.getTileValue('delayed-payment-po')).toBe(0);
      expect(component.getTileValue('total-products')).toBe(0);
      expect(component.getTileValue('low-stock-products')).toBe(0);
      expect(component.getTileValue('out-of-stock-products')).toBe(0);
      expect(component.getTileValue('delayed-demands')).toBe(0);
      expect(component.getTileValue('products-below-min-quantity')).toBe(0);
      expect(component.getTileValue('pending-issue-requests')).toBe(0);
    });

    it('should verify test data values (local development)', () => {
      // Arrange
      component.dashboardData = mockDashboardDataTestValues;

      // Act & Assert - Test data values
      expect(component.getTileValue('pending-gate-out')).toBe(5);
      expect(component.getTileValue('pending-gate-pass')).toBe(8);
      expect(component.getTileValue('invoices-without-po')).toBe(3);
      expect(component.getTileValue('active-po')).toBe(8);
      expect(component.getTileValue('revised-po')).toBe(4);
      expect(component.getTileValue('delayed-delivery-po')).toBe(4);
      expect(component.getTileValue('delayed-payment-po')).toBe(7);
      expect(component.getTileValue('total-products')).toBe(150);
      expect(component.getTileValue('low-stock-products')).toBe(12);
      expect(component.getTileValue('out-of-stock-products')).toBe(6);
      expect(component.getTileValue('delayed-demands')).toBe(8);
      expect(component.getTileValue('products-below-min-quantity')).toBe(15);
      expect(component.getTileValue('pending-issue-requests')).toBe(9);
    });
  });

  describe('🔄 API Integration Tests', () => {

    it('should load dashboard data and update all tiles via API call', () => {
      // Arrange
      const expectedUrl = `${environment.Reporting_Api_Url}report/gatedashboard`;

      // Act
      component.loadDashboardDataWithoutDateFilter(true); // Bypass cache

      // Assert API call
      const req = httpMock.expectOne(expectedUrl);
      expect(req.request.method).toBe('POST');
      expect(req.request.body.DateFilterType).toBe('all');

      // Respond with test data
      req.flush(mockDashboardDataWithValues);

      // Verify tile updates were called
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('pending-gate-out', 12);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('pending-gate-pass', 8);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('invoices-without-po', 5);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('active-po', 25);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('revised-po', 7);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('delayed-delivery-po', 3);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('delayed-payment-po', 9);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('total-products', 150);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('low-stock-products', 12);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('out-of-stock-products', 6);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('delayed-demands', 8);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('products-below-min-quantity', 15);
      expect(mockLayoutService.updateTileData).toHaveBeenCalledWith('pending-issue-requests', 9);
    });

    it('should force refresh and bypass cache when force button is clicked', () => {
      // Arrange
      const expectedUrl = `${environment.Reporting_Api_Url}report/gatedashboard`;
      component.selecteddateFilter = 'all';

      // Act
      component.refreshDashboard(true); // Force refresh

      // Assert
      const req = httpMock.expectOne(expectedUrl);
      expect(req.request.method).toBe('POST');

      // Respond with data
      req.flush(mockDashboardDataTestValues);

      // Verify data was processed
      expect(component.dashboardData).toEqual(mockDashboardDataTestValues);
      expect(component.isDataFromCache).toBe(false);
    });

    it('should handle API errors gracefully', () => {
      // Arrange
      const expectedUrl = `${environment.Reporting_Api_Url}report/gatedashboard`;

      // Act
      component.loadDashboardDataWithoutDateFilter(true);

      // Assert API call and simulate error
      const req = httpMock.expectOne(expectedUrl);
      req.error(new ErrorEvent('Network error'));

      // Verify error handling
      expect(mockAlertService.error).toHaveBeenCalledWith('Error loading dashboard data. Please try again later.');
      expect(mockLoadingService.hide).toHaveBeenCalled();
    });
  });

  describe('📊 Data Processing Tests', () => {

    it('should process dashboard data and update component state', () => {
      // Act
      component['processDashboardData'](mockDashboardDataWithValues, false);

      // Assert
      expect(component.dashboardData).toEqual(mockDashboardDataWithValues);
      expect(component.isDataFromCache).toBe(false);
      expect(component.lastDataLoadTime).toBeDefined();
    });

    it('should detect when data is from cache', () => {
      // Act
      component['processDashboardData'](mockDashboardDataWithValues, true);

      // Assert
      expect(component.isDataFromCache).toBe(true);
    });

    it('should show warning for empty data (except All filter)', () => {
      // Arrange
      component.selecteddateFilter = 'today';

      // Act
      component['processDashboardData'](mockDashboardDataEmpty, false);

      // Assert
      expect(mockAlertService.warning).toHaveBeenCalledWith('No data found for the selected date range.');
    });

    it('should not show warning for empty data with All filter', () => {
      // Arrange
      component.selecteddateFilter = 'all';

      // Act
      component['processDashboardData'](mockDashboardDataEmpty, false);

      // Assert
      expect(mockAlertService.warning).not.toHaveBeenCalled();
    });
  });

  describe('🎯 Tile Navigation Tests', () => {

    it('should navigate to correct routes when tiles are clicked', () => {
      // Arrange
      const mockTile = {
        id: 'active-po',
        actionRoute: '/home/<USER>/list?status=Active'
      };
      spyOn(component['router'], 'navigate');

      // Act
      component.navigateToTileAction(mockTile as any);

      // Assert
      expect(component['router'].navigate).toHaveBeenCalledWith(['/home/<USER>/list?status=Active']);
    });
  });

  describe('🔧 Edge Cases and Error Handling', () => {

    it('should handle undefined dashboard data gracefully', () => {
      // Arrange
      component.dashboardData = undefined as any;

      // Act & Assert - Should not throw errors
      expect(() => component.getTileValue('pending-gate-out')).not.toThrow();
      expect(component.getTileValue('pending-gate-out')).toBe(0);
    });

    it('should handle null dashboard data gracefully', () => {
      // Arrange
      component.dashboardData = null as any;

      // Act & Assert - Should not throw errors
      expect(() => component.getTileValue('active-po')).not.toThrow();
      expect(component.getTileValue('active-po')).toBe(0);
    });

    it('should handle layout service failures gracefully', () => {
      // Arrange
      mockLayoutService.updateTileData.and.throwError('Layout service error');

      // Act & Assert - Should not throw errors
      expect(() => component['processDashboardData'](mockDashboardDataWithValues, false)).not.toThrow();
    });
  });
});

// Test data verification helper
export class DashboardTestHelper {
  static verifyTileData(component: GateDashboardComponent, expectedData: GateDashboardModel): boolean {
    const tileIds = [
      'pending-gate-out', 'pending-gate-pass', 'invoices-without-po',
      'active-po', 'revised-po', 'delayed-delivery-po', 'delayed-payment-po',
      'total-products', 'low-stock-products', 'out-of-stock-products',
      'delayed-demands', 'products-below-min-quantity', 'pending-issue-requests'
    ];

    const expectedValues = [
      expectedData.PendingGateOutFilteredCount,
      expectedData.PendingGatePassFilteredCount,
      expectedData.InvoicesWithoutPOFilteredCount,
      expectedData.ActivePOFilteredCount,
      expectedData.RevisedPOFilteredCount,
      expectedData.DelayedDeliveryPOFilteredCount,
      expectedData.DelayedPaymentPOFilteredCount,
      expectedData.TotalProductsFilteredCount,
      expectedData.LowStockProductsFilteredCount,
      expectedData.OutOfStockProductsFilteredCount,
      expectedData.DelayedDemandsFilteredCount,
      expectedData.ProductsBelowMinQuantityFilteredCount,
      expectedData.PendingIssueRequestsFilteredCount
    ];

    for (let i = 0; i < tileIds.length; i++) {
      const actualValue = component.getTileValue(tileIds[i]);
      const expectedValue = expectedValues[i];

      if (actualValue !== expectedValue) {
        console.error(`Tile ${tileIds[i]}: Expected ${expectedValue}, got ${actualValue}`);
        return false;
      }
    }

    return true;
  }

  static logTileValues(component: GateDashboardComponent): void {
    console.log('=== DASHBOARD TILE VALUES ===');
    console.log('Gate Operations:');
    console.log(`  Pending Gate-Out: ${component.getTileValue('pending-gate-out')}`);
    console.log(`  Pending Gate Pass: ${component.getTileValue('pending-gate-pass')}`);
    console.log(`  Invoices Without PO: ${component.getTileValue('invoices-without-po')}`);
    console.log('Purchase Orders:');
    console.log(`  Active POs: ${component.getTileValue('active-po')}`);
    console.log(`  Revised POs: ${component.getTileValue('revised-po')}`);
    console.log(`  Delayed Delivery POs: ${component.getTileValue('delayed-delivery-po')}`);
    console.log(`  Delayed Payment POs: ${component.getTileValue('delayed-payment-po')}`);
    console.log('Products:');
    console.log(`  Total Products: ${component.getTileValue('total-products')}`);
    console.log(`  Low Stock Products: ${component.getTileValue('low-stock-products')}`);
    console.log(`  Out of Stock Products: ${component.getTileValue('out-of-stock-products')}`);
    console.log(`  Delayed Demands: ${component.getTileValue('delayed-demands')}`);
    console.log(`  Products Below Min Quantity: ${component.getTileValue('products-below-min-quantity')}`);
    console.log(`  Pending Issue Requests: ${component.getTileValue('pending-issue-requests')}`);
    console.log('==============================');
  }
}
